import*as e from"../common/common.js";import*as t from"../platform/platform.js";import{assertNotNullOrUndefined as r}from"../platform/platform.js";import*as n from"../../models/cpu_profile/cpu_profile.js";import*as s from"../../models/text_utils/text_utils.js";import*as i from"../i18n/i18n.js";import*as a from"../root/root.js";import*as o from"../host/host.js";import*as l from"../protocol_client/protocol_client.js";const d=new Map;class c extends e.ObjectWrapper.ObjectWrapper{#e;constructor(e){super(),this.#e=e}target(){return this.#e}async preSuspendModel(e){}async suspendModel(e){}async resumeModel(){}async postResumeModel(){}dispose(){}static register(e,t){if(t.early&&!t.autostart)throw new Error(`Error registering model ${e.name}: early models must be autostarted.`);d.set(e,t)}static get registeredModels(){return d}}var h=Object.freeze({__proto__:null,SDKModel:c});const u=[{longhands:["animation-delay-start","animation-delay-end"],name:"-alternative-animation-delay"},{longhands:["animation-duration","animation-timing-function","animation-delay-start","animation-delay-end","animation-iteration-count","animation-direction","animation-fill-mode","animation-play-state","animation-name","animation-timeline","animation-range-start","animation-range-end"],name:"-alternative-animation-with-delay-start-end"},{longhands:["animation-duration","animation-timing-function","animation-delay","animation-iteration-count","animation-direction","animation-fill-mode","animation-play-state","animation-name","animation-timeline","animation-range-start","animation-range-end"],name:"-alternative-animation-with-timeline"},{longhands:["mask-image","-webkit-mask-position-x","-webkit-mask-position-y","mask-size","mask-repeat","mask-origin","mask-clip","mask-composite","mask-mode"],name:"-alternative-mask"},{longhands:["view-timeline-name","view-timeline-axis","view-timeline-inset"],name:"-alternative-view-timeline-with-inset"},{inherited:!0,name:"-webkit-border-horizontal-spacing"},{name:"-webkit-border-image"},{inherited:!0,name:"-webkit-border-vertical-spacing"},{keywords:["stretch","start","center","end","baseline"],name:"-webkit-box-align"},{keywords:["slice","clone"],name:"-webkit-box-decoration-break"},{keywords:["normal","reverse"],name:"-webkit-box-direction"},{name:"-webkit-box-flex"},{name:"-webkit-box-ordinal-group"},{keywords:["horizontal","vertical"],name:"-webkit-box-orient"},{keywords:["start","center","end","justify"],name:"-webkit-box-pack"},{name:"-webkit-box-reflect"},{longhands:["break-after"],name:"-webkit-column-break-after"},{longhands:["break-before"],name:"-webkit-column-break-before"},{longhands:["break-inside"],name:"-webkit-column-break-inside"},{inherited:!0,name:"-webkit-font-smoothing"},{inherited:!0,keywords:["auto","loose","normal","strict","after-white-space","anywhere"],name:"-webkit-line-break"},{name:"-webkit-line-clamp"},{inherited:!0,name:"-webkit-locale"},{longhands:["-webkit-mask-image","-webkit-mask-position-x","-webkit-mask-position-y","-webkit-mask-size","-webkit-mask-repeat","-webkit-mask-origin","-webkit-mask-clip"],name:"-webkit-mask"},{longhands:["-webkit-mask-box-image-source","-webkit-mask-box-image-slice","-webkit-mask-box-image-width","-webkit-mask-box-image-outset","-webkit-mask-box-image-repeat"],name:"-webkit-mask-box-image"},{name:"-webkit-mask-box-image-outset"},{name:"-webkit-mask-box-image-repeat"},{name:"-webkit-mask-box-image-slice"},{name:"-webkit-mask-box-image-source"},{name:"-webkit-mask-box-image-width"},{name:"-webkit-mask-clip"},{name:"-webkit-mask-composite"},{name:"-webkit-mask-image"},{name:"-webkit-mask-origin"},{longhands:["-webkit-mask-position-x","-webkit-mask-position-y"],name:"-webkit-mask-position"},{name:"-webkit-mask-position-x"},{name:"-webkit-mask-position-y"},{name:"-webkit-mask-repeat"},{name:"-webkit-mask-size"},{name:"-webkit-perspective-origin-x"},{name:"-webkit-perspective-origin-y"},{inherited:!0,keywords:["economy","exact"],name:"-webkit-print-color-adjust"},{inherited:!0,keywords:["logical","visual"],name:"-webkit-rtl-ordering"},{inherited:!0,keywords:["before","after"],name:"-webkit-ruby-position"},{inherited:!0,name:"-webkit-tap-highlight-color"},{inherited:!0,name:"-webkit-text-combine"},{inherited:!0,name:"-webkit-text-decorations-in-effect"},{inherited:!0,name:"-webkit-text-fill-color"},{inherited:!0,name:"-webkit-text-orientation"},{inherited:!0,keywords:["none","disc","circle","square"],name:"-webkit-text-security"},{inherited:!0,longhands:["-webkit-text-stroke-width","-webkit-text-stroke-color"],name:"-webkit-text-stroke"},{inherited:!0,name:"-webkit-text-stroke-color"},{inherited:!0,name:"-webkit-text-stroke-width"},{name:"-webkit-transform-origin-x"},{name:"-webkit-transform-origin-y"},{name:"-webkit-transform-origin-z"},{keywords:["auto","none","element"],name:"-webkit-user-drag"},{inherited:!0,keywords:["read-only","read-write","read-write-plaintext-only"],name:"-webkit-user-modify"},{inherited:!0,name:"-webkit-writing-mode"},{inherited:!0,keywords:["auto","currentcolor"],name:"accent-color"},{name:"additive-symbols"},{name:"align-content"},{name:"align-items"},{name:"align-self"},{keywords:["auto","baseline","alphabetic","ideographic","middle","central","mathematical","before-edge","text-before-edge","after-edge","text-after-edge","hanging"],name:"alignment-baseline"},{name:"all"},{keywords:["none"],name:"anchor-default"},{keywords:["none"],name:"anchor-name"},{longhands:["animation-duration","animation-timing-function","animation-delay","animation-iteration-count","animation-direction","animation-fill-mode","animation-play-state","animation-name"],name:"animation"},{keywords:["replace","add","accumulate"],name:"animation-composition"},{name:"animation-delay"},{name:"animation-delay-end"},{name:"animation-delay-start"},{keywords:["normal","reverse","alternate","alternate-reverse"],name:"animation-direction"},{name:"animation-duration"},{keywords:["none","forwards","backwards","both"],name:"animation-fill-mode"},{keywords:["infinite"],name:"animation-iteration-count"},{keywords:["none"],name:"animation-name"},{keywords:["running","paused"],name:"animation-play-state"},{longhands:["animation-range-start","animation-range-end"],name:"animation-range"},{name:"animation-range-end"},{name:"animation-range-start"},{keywords:["none","auto"],name:"animation-timeline"},{keywords:["linear","ease","ease-in","ease-out","ease-in-out","jump-both","jump-end","jump-none","jump-start","step-start","step-end"],name:"animation-timing-function"},{keywords:["none","drag","no-drag"],name:"app-region"},{name:"appearance"},{name:"ascent-override"},{keywords:["auto"],name:"aspect-ratio"},{keywords:["none"],name:"backdrop-filter"},{keywords:["visible","hidden"],name:"backface-visibility"},{longhands:["background-image","background-position-x","background-position-y","background-size","background-repeat","background-attachment","background-origin","background-clip","background-color"],name:"background"},{keywords:["scroll","fixed","local"],name:"background-attachment"},{keywords:["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],name:"background-blend-mode"},{keywords:["border-box","padding-box","content-box","text"],name:"background-clip"},{keywords:["currentcolor"],name:"background-color"},{keywords:["auto","none"],name:"background-image"},{keywords:["border-box","padding-box","content-box"],name:"background-origin"},{longhands:["background-position-x","background-position-y"],name:"background-position"},{name:"background-position-x"},{name:"background-position-y"},{name:"background-repeat"},{keywords:["auto","cover","contain"],name:"background-size"},{name:"base-palette"},{keywords:["baseline","sub","super"],name:"baseline-shift"},{keywords:["auto","first","last"],name:"baseline-source"},{keywords:["auto"],name:"block-size"},{longhands:["border-top-color","border-top-style","border-top-width","border-right-color","border-right-style","border-right-width","border-bottom-color","border-bottom-style","border-bottom-width","border-left-color","border-left-style","border-left-width","border-image-source","border-image-slice","border-image-width","border-image-outset","border-image-repeat"],name:"border"},{longhands:["border-block-start-color","border-block-start-style","border-block-start-width","border-block-end-color","border-block-end-style","border-block-end-width"],name:"border-block"},{longhands:["border-block-start-color","border-block-end-color"],name:"border-block-color"},{longhands:["border-block-end-width","border-block-end-style","border-block-end-color"],name:"border-block-end"},{name:"border-block-end-color"},{name:"border-block-end-style"},{name:"border-block-end-width"},{longhands:["border-block-start-width","border-block-start-style","border-block-start-color"],name:"border-block-start"},{name:"border-block-start-color"},{name:"border-block-start-style"},{name:"border-block-start-width"},{longhands:["border-block-start-style","border-block-end-style"],name:"border-block-style"},{longhands:["border-block-start-width","border-block-end-width"],name:"border-block-width"},{longhands:["border-bottom-width","border-bottom-style","border-bottom-color"],name:"border-bottom"},{keywords:["currentcolor"],name:"border-bottom-color"},{name:"border-bottom-left-radius"},{name:"border-bottom-right-radius"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"border-bottom-style"},{keywords:["thin","medium","thick"],name:"border-bottom-width"},{inherited:!0,keywords:["separate","collapse"],name:"border-collapse"},{longhands:["border-top-color","border-right-color","border-bottom-color","border-left-color"],name:"border-color"},{name:"border-end-end-radius"},{name:"border-end-start-radius"},{longhands:["border-image-source","border-image-slice","border-image-width","border-image-outset","border-image-repeat"],name:"border-image"},{name:"border-image-outset"},{keywords:["stretch","repeat","round","space"],name:"border-image-repeat"},{name:"border-image-slice"},{keywords:["none"],name:"border-image-source"},{keywords:["auto"],name:"border-image-width"},{longhands:["border-inline-start-color","border-inline-start-style","border-inline-start-width","border-inline-end-color","border-inline-end-style","border-inline-end-width"],name:"border-inline"},{longhands:["border-inline-start-color","border-inline-end-color"],name:"border-inline-color"},{longhands:["border-inline-end-width","border-inline-end-style","border-inline-end-color"],name:"border-inline-end"},{name:"border-inline-end-color"},{name:"border-inline-end-style"},{name:"border-inline-end-width"},{longhands:["border-inline-start-width","border-inline-start-style","border-inline-start-color"],name:"border-inline-start"},{name:"border-inline-start-color"},{name:"border-inline-start-style"},{name:"border-inline-start-width"},{longhands:["border-inline-start-style","border-inline-end-style"],name:"border-inline-style"},{longhands:["border-inline-start-width","border-inline-end-width"],name:"border-inline-width"},{longhands:["border-left-width","border-left-style","border-left-color"],name:"border-left"},{keywords:["currentcolor"],name:"border-left-color"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"border-left-style"},{keywords:["thin","medium","thick"],name:"border-left-width"},{longhands:["border-top-left-radius","border-top-right-radius","border-bottom-right-radius","border-bottom-left-radius"],name:"border-radius"},{longhands:["border-right-width","border-right-style","border-right-color"],name:"border-right"},{keywords:["currentcolor"],name:"border-right-color"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"border-right-style"},{keywords:["thin","medium","thick"],name:"border-right-width"},{inherited:!0,longhands:["-webkit-border-horizontal-spacing","-webkit-border-vertical-spacing"],name:"border-spacing"},{name:"border-start-end-radius"},{name:"border-start-start-radius"},{keywords:["none"],longhands:["border-top-style","border-right-style","border-bottom-style","border-left-style"],name:"border-style"},{longhands:["border-top-width","border-top-style","border-top-color"],name:"border-top"},{keywords:["currentcolor"],name:"border-top-color"},{name:"border-top-left-radius"},{name:"border-top-right-radius"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"border-top-style"},{keywords:["thin","medium","thick"],name:"border-top-width"},{longhands:["border-top-width","border-right-width","border-bottom-width","border-left-width"],name:"border-width"},{keywords:["auto"],name:"bottom"},{keywords:["none"],name:"box-shadow"},{keywords:["content-box","border-box"],name:"box-sizing"},{keywords:["auto","avoid","avoid-column","avoid-page","column","left","page","recto","right","verso"],name:"break-after"},{keywords:["auto","avoid","avoid-column","avoid-page","column","left","page","recto","right","verso"],name:"break-before"},{keywords:["auto","avoid","avoid-column","avoid-page"],name:"break-inside"},{keywords:["auto","dynamic","static"],name:"buffered-rendering"},{inherited:!0,keywords:["top","bottom"],name:"caption-side"},{inherited:!0,keywords:["auto","currentcolor"],name:"caret-color"},{keywords:["none","left","right","both","inline-start","inline-end"],name:"clear"},{keywords:["auto"],name:"clip"},{keywords:["border-box","padding-box","content-box","margin-box","fill-box","stroke-box","view-box","none"],name:"clip-path"},{inherited:!0,keywords:["nonzero","evenodd"],name:"clip-rule"},{inherited:!0,keywords:["currentcolor"],name:"color"},{inherited:!0,keywords:["auto","srgb","linearrgb"],name:"color-interpolation"},{inherited:!0,keywords:["auto","srgb","linearrgb"],name:"color-interpolation-filters"},{inherited:!0,keywords:["auto","optimizespeed","optimizequality"],name:"color-rendering"},{inherited:!0,name:"color-scheme"},{keywords:["auto"],name:"column-count"},{keywords:["balance","auto"],name:"column-fill"},{keywords:["normal"],name:"column-gap"},{longhands:["column-rule-width","column-rule-style","column-rule-color"],name:"column-rule"},{keywords:["currentcolor"],name:"column-rule-color"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"column-rule-style"},{keywords:["thin","medium","thick"],name:"column-rule-width"},{keywords:["none","all"],name:"column-span"},{keywords:["auto"],name:"column-width"},{longhands:["column-width","column-count"],name:"columns"},{keywords:["none","strict","content","size","layout","style","paint","inline-size","block-size"],name:"contain"},{name:"contain-intrinsic-block-size"},{keywords:["none"],name:"contain-intrinsic-height"},{name:"contain-intrinsic-inline-size"},{longhands:["contain-intrinsic-width","contain-intrinsic-height"],name:"contain-intrinsic-size"},{keywords:["none"],name:"contain-intrinsic-width"},{longhands:["container-name","container-type"],name:"container"},{keywords:["none"],name:"container-name"},{keywords:["normal","inline-size","size","scroll-state"],name:"container-type"},{name:"content"},{keywords:["visible","auto","hidden"],name:"content-visibility"},{keywords:["none"],name:"counter-increment"},{keywords:["none"],name:"counter-reset"},{keywords:["none"],name:"counter-set"},{inherited:!0,keywords:["auto","default","none","context-menu","help","pointer","progress","wait","cell","crosshair","text","vertical-text","alias","copy","move","no-drop","not-allowed","e-resize","n-resize","ne-resize","nw-resize","s-resize","se-resize","sw-resize","w-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","col-resize","row-resize","all-scroll","zoom-in","zoom-out","grab","grabbing"],name:"cursor"},{name:"cx"},{name:"cy"},{keywords:["none"],name:"d"},{name:"descent-override"},{inherited:!0,keywords:["ltr","rtl"],name:"direction"},{keywords:["inline","block","list-item","inline-block","table","inline-table","table-row-group","table-header-group","table-footer-group","table-row","table-column-group","table-column","table-cell","table-caption","-webkit-box","-webkit-inline-box","flex","inline-flex","grid","inline-grid","contents","flow-root","none","flow","math","ruby","ruby-text"],name:"display"},{inherited:!0,keywords:["auto","alphabetic","ideographic","middle","central","mathematical","hanging","use-script","no-change","reset-size","text-after-edge","text-before-edge"],name:"dominant-baseline"},{inherited:!0,keywords:["standard","high","constrained-high"],name:"dynamic-range-limit"},{inherited:!0,keywords:["show","hide"],name:"empty-cells"},{name:"fallback"},{keywords:["fixed","content"],name:"field-sizing"},{inherited:!0,name:"fill"},{inherited:!0,name:"fill-opacity"},{inherited:!0,keywords:["nonzero","evenodd"],name:"fill-rule"},{keywords:["none"],name:"filter"},{longhands:["flex-grow","flex-shrink","flex-basis"],name:"flex"},{keywords:["auto","fit-content","min-content","max-content","content"],name:"flex-basis"},{keywords:["row","row-reverse","column","column-reverse"],name:"flex-direction"},{longhands:["flex-direction","flex-wrap"],name:"flex-flow"},{name:"flex-grow"},{name:"flex-shrink"},{keywords:["nowrap","wrap","wrap-reverse"],name:"flex-wrap"},{keywords:["none","left","right","inline-start","inline-end"],name:"float"},{keywords:["currentcolor"],name:"flood-color"},{name:"flood-opacity"},{inherited:!0,longhands:["font-style","font-variant-ligatures","font-variant-caps","font-variant-numeric","font-variant-east-asian","font-variant-alternates","font-variant-position","font-weight","font-stretch","font-size","line-height","font-family","font-optical-sizing","font-size-adjust","font-kerning","font-feature-settings","font-variation-settings"],name:"font"},{name:"font-display"},{inherited:!0,name:"font-family"},{inherited:!0,keywords:["normal"],name:"font-feature-settings"},{inherited:!0,keywords:["auto","normal","none"],name:"font-kerning"},{inherited:!0,keywords:["auto","none"],name:"font-optical-sizing"},{inherited:!0,keywords:["normal","light","dark"],name:"font-palette"},{inherited:!0,keywords:["xx-small","x-small","small","medium","large","x-large","xx-large","xxx-large","larger","smaller","-webkit-xxx-large"],name:"font-size"},{inherited:!0,keywords:["none","ex-height","cap-height","ch-width","ic-width","from-font"],name:"font-size-adjust"},{inherited:!0,keywords:["normal","ultra-condensed","extra-condensed","condensed","semi-condensed","semi-expanded","expanded","extra-expanded","ultra-expanded"],name:"font-stretch"},{inherited:!0,keywords:["normal","italic","oblique"],name:"font-style"},{inherited:!0,longhands:["font-synthesis-weight","font-synthesis-style","font-synthesis-small-caps"],name:"font-synthesis"},{inherited:!0,keywords:["auto","none"],name:"font-synthesis-small-caps"},{inherited:!0,keywords:["auto","none"],name:"font-synthesis-style"},{inherited:!0,keywords:["auto","none"],name:"font-synthesis-weight"},{inherited:!0,longhands:["font-variant-ligatures","font-variant-caps","font-variant-alternates","font-variant-numeric","font-variant-east-asian","font-variant-position"],name:"font-variant"},{inherited:!0,keywords:["normal"],name:"font-variant-alternates"},{inherited:!0,keywords:["normal","small-caps","all-small-caps","petite-caps","all-petite-caps","unicase","titling-caps"],name:"font-variant-caps"},{inherited:!0,keywords:["normal","jis78","jis83","jis90","jis04","simplified","traditional","full-width","proportional-width","ruby"],name:"font-variant-east-asian"},{inherited:!0,keywords:["normal","none","common-ligatures","no-common-ligatures","discretionary-ligatures","no-discretionary-ligatures","historical-ligatures","no-historical-ligatures","contextual","no-contextual"],name:"font-variant-ligatures"},{inherited:!0,keywords:["normal","lining-nums","oldstyle-nums","proportional-nums","tabular-nums","diagonal-fractions","stacked-fractions","ordinal","slashed-zero"],name:"font-variant-numeric"},{inherited:!0,keywords:["normal","sub","super"],name:"font-variant-position"},{inherited:!0,keywords:["normal"],name:"font-variation-settings"},{inherited:!0,keywords:["normal","bold","bolder","lighter"],name:"font-weight"},{inherited:!0,keywords:["auto","none","preserve-parent-color"],name:"forced-color-adjust"},{longhands:["row-gap","column-gap"],name:"gap"},{longhands:["grid-template-rows","grid-template-columns","grid-template-areas","grid-auto-flow","grid-auto-rows","grid-auto-columns"],name:"grid"},{longhands:["grid-row-start","grid-column-start","grid-row-end","grid-column-end"],name:"grid-area"},{keywords:["auto","min-content","max-content"],name:"grid-auto-columns"},{keywords:["row","column"],name:"grid-auto-flow"},{keywords:["auto","min-content","max-content"],name:"grid-auto-rows"},{longhands:["grid-column-start","grid-column-end"],name:"grid-column"},{keywords:["auto"],name:"grid-column-end"},{longhands:["column-gap"],name:"grid-column-gap"},{keywords:["auto"],name:"grid-column-start"},{longhands:["row-gap","column-gap"],name:"grid-gap"},{longhands:["grid-row-start","grid-row-end"],name:"grid-row"},{keywords:["auto"],name:"grid-row-end"},{longhands:["row-gap"],name:"grid-row-gap"},{keywords:["auto"],name:"grid-row-start"},{longhands:["grid-template-rows","grid-template-columns","grid-template-areas"],name:"grid-template"},{keywords:["none"],name:"grid-template-areas"},{keywords:["none"],name:"grid-template-columns"},{keywords:["none"],name:"grid-template-rows"},{keywords:["auto","fit-content","min-content","max-content"],name:"height"},{inherited:!0,name:"hyphenate-character"},{inherited:!0,keywords:["auto"],name:"hyphenate-limit-chars"},{inherited:!0,keywords:["none","manual","auto"],name:"hyphens"},{inherited:!0,name:"image-orientation"},{inherited:!0,keywords:["auto","optimizespeed","optimizequality","-webkit-optimize-contrast","pixelated"],name:"image-rendering"},{name:"inherits"},{inherited:!1,keywords:["drop","normal","raise"],name:"initial-letter"},{name:"initial-value"},{keywords:["auto"],name:"inline-size"},{longhands:["top","right","bottom","left"],name:"inset"},{keywords:["none","top","bottom","center","left","right","x-start","x-end","y-start","y-end","start","end","self-start","self-end","all"],name:"inset-area"},{longhands:["inset-block-start","inset-block-end"],name:"inset-block"},{name:"inset-block-end"},{name:"inset-block-start"},{longhands:["inset-inline-start","inset-inline-end"],name:"inset-inline"},{name:"inset-inline-end"},{name:"inset-inline-start"},{keywords:["auto","isolate"],name:"isolation"},{name:"justify-content"},{name:"justify-items"},{name:"justify-self"},{keywords:["auto"],name:"left"},{inherited:!0,keywords:["normal"],name:"letter-spacing"},{keywords:["currentcolor"],name:"lighting-color"},{inherited:!0,keywords:["auto","loose","normal","strict","anywhere"],name:"line-break"},{name:"line-gap-override"},{inherited:!0,keywords:["normal"],name:"line-height"},{inherited:!0,longhands:["list-style-position","list-style-image","list-style-type"],name:"list-style"},{inherited:!0,keywords:["none"],name:"list-style-image"},{inherited:!0,keywords:["outside","inside"],name:"list-style-position"},{inherited:!0,keywords:["disc","circle","square","disclosure-open","disclosure-closed","decimal","none"],name:"list-style-type"},{longhands:["margin-top","margin-right","margin-bottom","margin-left"],name:"margin"},{longhands:["margin-block-start","margin-block-end"],name:"margin-block"},{keywords:["auto"],name:"margin-block-end"},{keywords:["auto"],name:"margin-block-start"},{keywords:["auto"],name:"margin-bottom"},{longhands:["margin-inline-start","margin-inline-end"],name:"margin-inline"},{keywords:["auto"],name:"margin-inline-end"},{keywords:["auto"],name:"margin-inline-start"},{keywords:["auto"],name:"margin-left"},{keywords:["auto"],name:"margin-right"},{keywords:["auto"],name:"margin-top"},{inherited:!0,longhands:["marker-start","marker-mid","marker-end"],name:"marker"},{inherited:!0,keywords:["none"],name:"marker-end"},{inherited:!0,keywords:["none"],name:"marker-mid"},{inherited:!0,keywords:["none"],name:"marker-start"},{name:"mask"},{name:"mask-clip"},{name:"mask-composite"},{name:"mask-image"},{name:"mask-mode"},{name:"mask-origin"},{longhands:["-webkit-mask-position-x","-webkit-mask-position-y"],name:"mask-position"},{name:"mask-repeat"},{name:"mask-size"},{keywords:["luminance","alpha"],name:"mask-type"},{inherited:!0,name:"math-depth"},{inherited:!0,keywords:["normal","compact"],name:"math-shift"},{inherited:!0,keywords:["normal","compact"],name:"math-style"},{keywords:["none"],name:"max-block-size"},{keywords:["none"],name:"max-height"},{keywords:["none"],name:"max-inline-size"},{keywords:["none"],name:"max-width"},{name:"min-block-size"},{name:"min-height"},{name:"min-inline-size"},{name:"min-width"},{keywords:["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"],name:"mix-blend-mode"},{name:"navigation"},{name:"negative"},{keywords:["fill","contain","cover","none","scale-down"],name:"object-fit"},{name:"object-position"},{keywords:["none"],name:"object-view-box"},{longhands:["offset-position","offset-path","offset-distance","offset-rotate","offset-anchor"],name:"offset"},{keywords:["auto"],name:"offset-anchor"},{name:"offset-distance"},{keywords:["none"],name:"offset-path"},{keywords:["auto","normal"],name:"offset-position"},{keywords:["auto","reverse"],name:"offset-rotate"},{name:"opacity"},{name:"order"},{keywords:["normal","none"],name:"origin-trial-test-property"},{inherited:!0,name:"orphans"},{longhands:["outline-color","outline-style","outline-width"],name:"outline"},{keywords:["currentcolor"],name:"outline-color"},{name:"outline-offset"},{keywords:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"],name:"outline-style"},{keywords:["thin","medium","thick"],name:"outline-width"},{longhands:["overflow-x","overflow-y"],name:"overflow"},{inherited:!1,keywords:["visible","none","auto"],name:"overflow-anchor"},{name:"overflow-block"},{keywords:["border-box","content-box","padding-box"],name:"overflow-clip-margin"},{name:"overflow-inline"},{inherited:!0,keywords:["normal","break-word","anywhere"],name:"overflow-wrap"},{keywords:["visible","hidden","scroll","auto","overlay","clip"],name:"overflow-x"},{keywords:["visible","hidden","scroll","auto","overlay","clip"],name:"overflow-y"},{keywords:["none","auto"],name:"overlay"},{name:"override-colors"},{longhands:["overscroll-behavior-x","overscroll-behavior-y"],name:"overscroll-behavior"},{name:"overscroll-behavior-block"},{name:"overscroll-behavior-inline"},{keywords:["auto","contain","none"],name:"overscroll-behavior-x"},{keywords:["auto","contain","none"],name:"overscroll-behavior-y"},{name:"pad"},{longhands:["padding-top","padding-right","padding-bottom","padding-left"],name:"padding"},{longhands:["padding-block-start","padding-block-end"],name:"padding-block"},{name:"padding-block-end"},{name:"padding-block-start"},{name:"padding-bottom"},{longhands:["padding-inline-start","padding-inline-end"],name:"padding-inline"},{name:"padding-inline-end"},{name:"padding-inline-start"},{name:"padding-left"},{name:"padding-right"},{name:"padding-top"},{keywords:["auto"],name:"page"},{longhands:["break-after"],name:"page-break-after"},{longhands:["break-before"],name:"page-break-before"},{longhands:["break-inside"],name:"page-break-inside"},{name:"page-orientation"},{inherited:!0,keywords:["normal","fill","stroke","markers"],name:"paint-order"},{keywords:["none"],name:"perspective"},{name:"perspective-origin"},{longhands:["align-content","justify-content"],name:"place-content"},{longhands:["align-items","justify-items"],name:"place-items"},{longhands:["align-self","justify-self"],name:"place-self"},{inherited:!0,keywords:["none","auto","stroke","fill","painted","visible","visiblestroke","visiblefill","visiblepainted","bounding-box","all"],name:"pointer-events"},{name:"popover-hide-delay"},{name:"popover-show-delay"},{keywords:["static","relative","absolute","fixed","sticky"],name:"position"},{keywords:["none"],name:"position-fallback"},{keywords:["normal"],name:"position-fallback-bounds"},{longhands:["position-try-order","position-try-options"],name:"position-try"},{keywords:["none","flip-block","flip-inline","flip-start"],name:"position-try-options"},{keywords:["normal","most-width","most-height","most-block-size","most-inline-size"],name:"position-try-order"},{name:"prefix"},{inherited:!0,keywords:["auto","none"],name:"quotes"},{name:"r"},{name:"range"},{keywords:["normal","flex-visual","flex-flow","grid-rows","grid-columns","grid-order"],name:"reading-order-items"},{keywords:["none","both","horizontal","vertical","block","inline"],name:"resize"},{keywords:["auto"],name:"right"},{name:"rotate"},{keywords:["normal"],name:"row-gap"},{inherited:!0,name:"ruby-position"},{keywords:["auto"],name:"rx"},{keywords:["auto"],name:"ry"},{name:"scale"},{keywords:["auto","smooth"],name:"scroll-behavior"},{longhands:["scroll-margin-top","scroll-margin-right","scroll-margin-bottom","scroll-margin-left"],name:"scroll-margin"},{longhands:["scroll-margin-block-start","scroll-margin-block-end"],name:"scroll-margin-block"},{name:"scroll-margin-block-end"},{name:"scroll-margin-block-start"},{name:"scroll-margin-bottom"},{longhands:["scroll-margin-inline-start","scroll-margin-inline-end"],name:"scroll-margin-inline"},{name:"scroll-margin-inline-end"},{name:"scroll-margin-inline-start"},{name:"scroll-margin-left"},{name:"scroll-margin-right"},{name:"scroll-margin-top"},{longhands:["scroll-padding-top","scroll-padding-right","scroll-padding-bottom","scroll-padding-left"],name:"scroll-padding"},{longhands:["scroll-padding-block-start","scroll-padding-block-end"],name:"scroll-padding-block"},{keywords:["auto"],name:"scroll-padding-block-end"},{keywords:["auto"],name:"scroll-padding-block-start"},{keywords:["auto"],name:"scroll-padding-bottom"},{longhands:["scroll-padding-inline-start","scroll-padding-inline-end"],name:"scroll-padding-inline"},{keywords:["auto"],name:"scroll-padding-inline-end"},{keywords:["auto"],name:"scroll-padding-inline-start"},{keywords:["auto"],name:"scroll-padding-left"},{keywords:["auto"],name:"scroll-padding-right"},{keywords:["auto"],name:"scroll-padding-top"},{keywords:["none","start","end","center"],name:"scroll-snap-align"},{keywords:["normal","always"],name:"scroll-snap-stop"},{keywords:["none","x","y","block","inline","both","mandatory","proximity"],name:"scroll-snap-type"},{longhands:["scroll-start-block","scroll-start-inline"],name:"scroll-start"},{keywords:["auto","start","end","center","top","bottom","left","right"],name:"scroll-start-block"},{keywords:["auto","start","end","center","top","bottom","left","right"],name:"scroll-start-inline"},{longhands:["scroll-start-target-block","scroll-start-target-inline"],name:"scroll-start-target"},{keywords:["none","auto"],name:"scroll-start-target-block"},{keywords:["none","auto"],name:"scroll-start-target-inline"},{keywords:["none","auto"],name:"scroll-start-target-x"},{keywords:["none","auto"],name:"scroll-start-target-y"},{name:"scroll-start-x"},{name:"scroll-start-y"},{longhands:["scroll-timeline-name","scroll-timeline-axis"],name:"scroll-timeline"},{name:"scroll-timeline-axis"},{name:"scroll-timeline-name"},{inherited:!0,keywords:["auto"],name:"scrollbar-color"},{inherited:!1,keywords:["auto","stable","both-edges"],name:"scrollbar-gutter"},{inherited:!1,keywords:["auto","thin","none"],name:"scrollbar-width"},{name:"shape-image-threshold"},{keywords:["none"],name:"shape-margin"},{keywords:["none"],name:"shape-outside"},{inherited:!0,keywords:["auto","optimizespeed","crispedges","geometricprecision"],name:"shape-rendering"},{name:"size"},{name:"size-adjust"},{inherited:!0,keywords:["none","normal","spell-out","digits","literal-punctuation","no-punctuation"],name:"speak"},{name:"speak-as"},{name:"src"},{keywords:["currentcolor"],name:"stop-color"},{name:"stop-opacity"},{inherited:!0,name:"stroke"},{inherited:!0,keywords:["none"],name:"stroke-dasharray"},{inherited:!0,name:"stroke-dashoffset"},{inherited:!0,keywords:["butt","round","square"],name:"stroke-linecap"},{inherited:!0,keywords:["miter","bevel","round"],name:"stroke-linejoin"},{inherited:!0,name:"stroke-miterlimit"},{inherited:!0,name:"stroke-opacity"},{inherited:!0,name:"stroke-width"},{name:"suffix"},{name:"symbols"},{name:"syntax"},{name:"system"},{inherited:!0,name:"tab-size"},{keywords:["auto","fixed"],name:"table-layout"},{inherited:!0,keywords:["left","right","center","justify","-webkit-left","-webkit-right","-webkit-center","start","end"],name:"text-align"},{inherited:!0,keywords:["auto","start","end","left","right","center","justify"],name:"text-align-last"},{inherited:!0,keywords:["start","middle","end"],name:"text-anchor"},{inherited:!0,keywords:["normal","no-autospace"],name:"text-autospace"},{name:"text-box-edge"},{keywords:["none","start","end","both"],name:"text-box-trim"},{inherited:!0,keywords:["none","all"],name:"text-combine-upright"},{longhands:["text-decoration-line","text-decoration-thickness","text-decoration-style","text-decoration-color"],name:"text-decoration"},{keywords:["currentcolor"],name:"text-decoration-color"},{keywords:["none","underline","overline","line-through","blink","spelling-error","grammar-error"],name:"text-decoration-line"},{inherited:!0,keywords:["none","auto"],name:"text-decoration-skip-ink"},{keywords:["solid","double","dotted","dashed","wavy"],name:"text-decoration-style"},{inherited:!1,keywords:["auto","from-font"],name:"text-decoration-thickness"},{inherited:!0,longhands:["text-emphasis-style","text-emphasis-color"],name:"text-emphasis"},{inherited:!0,keywords:["currentcolor"],name:"text-emphasis-color"},{inherited:!0,name:"text-emphasis-position"},{inherited:!0,name:"text-emphasis-style"},{inherited:!0,name:"text-indent"},{inherited:!0,keywords:["sideways","mixed","upright"],name:"text-orientation"},{keywords:["clip","ellipsis"],name:"text-overflow"},{inherited:!0,keywords:["auto","optimizespeed","optimizelegibility","geometricprecision"],name:"text-rendering"},{inherited:!0,keywords:["none"],name:"text-shadow"},{inherited:!0,keywords:["none","auto"],name:"text-size-adjust"},{inherited:!0,longhands:["text-autospace","text-spacing-trim"],name:"text-spacing"},{inherited:!0,keywords:["normal","space-all","space-first","trim-start"],name:"text-spacing-trim"},{inherited:!0,keywords:["capitalize","uppercase","lowercase","none","math-auto"],name:"text-transform"},{inherited:!0,keywords:["auto"],name:"text-underline-offset"},{inherited:!0,keywords:["auto","from-font","under","left","right"],name:"text-underline-position"},{inherited:!0,keywords:["wrap","nowrap","balance","pretty"],name:"text-wrap"},{name:"timeline-scope"},{keywords:["auto"],name:"top"},{keywords:["auto","none","pan-x","pan-left","pan-right","pan-y","pan-up","pan-down","pinch-zoom","manipulation"],name:"touch-action"},{keywords:["none"],name:"transform"},{keywords:["content-box","border-box","fill-box","stroke-box","view-box"],name:"transform-box"},{name:"transform-origin"},{keywords:["flat","preserve-3d"],name:"transform-style"},{longhands:["transition-property","transition-duration","transition-timing-function","transition-delay","transition-behavior"],name:"transition"},{name:"transition-behavior"},{name:"transition-delay"},{name:"transition-duration"},{keywords:["none"],name:"transition-property"},{keywords:["linear","ease","ease-in","ease-out","ease-in-out","jump-both","jump-end","jump-none","jump-start","step-start","step-end"],name:"transition-timing-function"},{name:"translate"},{keywords:["normal","embed","bidi-override","isolate","plaintext","isolate-override"],name:"unicode-bidi"},{name:"unicode-range"},{inherited:!0,keywords:["auto","none","text","all","contain"],name:"user-select"},{keywords:["none","non-scaling-stroke"],name:"vector-effect"},{keywords:["baseline","sub","super","text-top","text-bottom","middle"],name:"vertical-align"},{longhands:["view-timeline-name","view-timeline-axis"],name:"view-timeline"},{name:"view-timeline-axis"},{name:"view-timeline-inset"},{name:"view-timeline-name"},{keywords:["none"],name:"view-transition-class"},{keywords:["none"],name:"view-transition-name"},{inherited:!0,keywords:["visible","hidden","collapse"],name:"visibility"},{inherited:!0,longhands:["white-space-collapse","text-wrap"],name:"white-space"},{inherited:!0,keywords:["collapse","preserve","preserve-breaks","break-spaces"],name:"white-space-collapse"},{inherited:!0,name:"widows"},{keywords:["auto","fit-content","min-content","max-content"],name:"width"},{keywords:["auto"],name:"will-change"},{inherited:!0,keywords:["normal","break-all","keep-all","break-word","auto-phrase"],name:"word-break"},{inherited:!0,keywords:["normal"],name:"word-spacing"},{inherited:!0,keywords:["horizontal-tb","vertical-rl","vertical-lr"],name:"writing-mode"},{name:"x"},{name:"y"},{keywords:["auto"],name:"z-index"},{name:"zoom"}],g={"-webkit-box-align":{values:["stretch","start","center","end","baseline"]},"-webkit-box-decoration-break":{values:["slice","clone"]},"-webkit-box-direction":{values:["normal","reverse"]},"-webkit-box-orient":{values:["horizontal","vertical"]},"-webkit-box-pack":{values:["start","center","end","justify"]},"-webkit-line-break":{values:["auto","loose","normal","strict","after-white-space","anywhere"]},"-webkit-print-color-adjust":{values:["economy","exact"]},"-webkit-rtl-ordering":{values:["logical","visual"]},"-webkit-ruby-position":{values:["before","after"]},"-webkit-text-security":{values:["none","disc","circle","square"]},"-webkit-user-drag":{values:["auto","none","element"]},"-webkit-user-modify":{values:["read-only","read-write","read-write-plaintext-only"]},"accent-color":{values:["auto","currentcolor"]},"alignment-baseline":{values:["auto","baseline","alphabetic","ideographic","middle","central","mathematical","before-edge","text-before-edge","after-edge","text-after-edge","hanging"]},"anchor-default":{values:["none"]},"anchor-name":{values:["none"]},"animation-composition":{values:["replace","add","accumulate"]},"animation-direction":{values:["normal","reverse","alternate","alternate-reverse"]},"animation-fill-mode":{values:["none","forwards","backwards","both"]},"animation-iteration-count":{values:["infinite"]},"animation-name":{values:["none"]},"animation-play-state":{values:["running","paused"]},"animation-timeline":{values:["none","auto"]},"animation-timing-function":{values:["linear","ease","ease-in","ease-out","ease-in-out","jump-both","jump-end","jump-none","jump-start","step-start","step-end"]},"app-region":{values:["none","drag","no-drag"]},"aspect-ratio":{values:["auto"]},"backdrop-filter":{values:["none"]},"backface-visibility":{values:["visible","hidden"]},"background-attachment":{values:["scroll","fixed","local"]},"background-blend-mode":{values:["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]},"background-clip":{values:["border-box","padding-box","content-box","text"]},"background-color":{values:["currentcolor"]},"background-image":{values:["auto","none"]},"background-origin":{values:["border-box","padding-box","content-box"]},"background-size":{values:["auto","cover","contain"]},"baseline-shift":{values:["baseline","sub","super"]},"baseline-source":{values:["auto","first","last"]},"block-size":{values:["auto"]},"border-bottom-color":{values:["currentcolor"]},"border-bottom-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"border-bottom-width":{values:["thin","medium","thick"]},"border-collapse":{values:["separate","collapse"]},"border-image-repeat":{values:["stretch","repeat","round","space"]},"border-image-source":{values:["none"]},"border-image-width":{values:["auto"]},"border-left-color":{values:["currentcolor"]},"border-left-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"border-left-width":{values:["thin","medium","thick"]},"border-right-color":{values:["currentcolor"]},"border-right-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"border-right-width":{values:["thin","medium","thick"]},"border-style":{values:["none"]},"border-top-color":{values:["currentcolor"]},"border-top-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"border-top-width":{values:["thin","medium","thick"]},bottom:{values:["auto"]},"box-shadow":{values:["none"]},"box-sizing":{values:["content-box","border-box"]},"break-after":{values:["auto","avoid","avoid-column","avoid-page","column","left","page","recto","right","verso"]},"break-before":{values:["auto","avoid","avoid-column","avoid-page","column","left","page","recto","right","verso"]},"break-inside":{values:["auto","avoid","avoid-column","avoid-page"]},"buffered-rendering":{values:["auto","dynamic","static"]},"caption-side":{values:["top","bottom"]},"caret-color":{values:["auto","currentcolor"]},clear:{values:["none","left","right","both","inline-start","inline-end"]},clip:{values:["auto"]},"clip-path":{values:["border-box","padding-box","content-box","margin-box","fill-box","stroke-box","view-box","none"]},"clip-rule":{values:["nonzero","evenodd"]},color:{values:["currentcolor"]},"color-interpolation":{values:["auto","srgb","linearrgb"]},"color-interpolation-filters":{values:["auto","srgb","linearrgb"]},"color-rendering":{values:["auto","optimizespeed","optimizequality"]},"column-count":{values:["auto"]},"column-fill":{values:["balance","auto"]},"column-gap":{values:["normal"]},"column-rule-color":{values:["currentcolor"]},"column-rule-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"column-rule-width":{values:["thin","medium","thick"]},"column-span":{values:["none","all"]},"column-width":{values:["auto"]},contain:{values:["none","strict","content","size","layout","style","paint","inline-size","block-size"]},"contain-intrinsic-height":{values:["none"]},"contain-intrinsic-width":{values:["none"]},"container-name":{values:["none"]},"container-type":{values:["normal","inline-size","size","scroll-state"]},"content-visibility":{values:["visible","auto","hidden"]},"counter-increment":{values:["none"]},"counter-reset":{values:["none"]},"counter-set":{values:["none"]},cursor:{values:["auto","default","none","context-menu","help","pointer","progress","wait","cell","crosshair","text","vertical-text","alias","copy","move","no-drop","not-allowed","e-resize","n-resize","ne-resize","nw-resize","s-resize","se-resize","sw-resize","w-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","col-resize","row-resize","all-scroll","zoom-in","zoom-out","grab","grabbing"]},d:{values:["none"]},direction:{values:["ltr","rtl"]},display:{values:["inline","block","list-item","inline-block","table","inline-table","table-row-group","table-header-group","table-footer-group","table-row","table-column-group","table-column","table-cell","table-caption","-webkit-box","-webkit-inline-box","flex","inline-flex","grid","inline-grid","contents","flow-root","none","flow","math","ruby","ruby-text"]},"dominant-baseline":{values:["auto","alphabetic","ideographic","middle","central","mathematical","hanging","use-script","no-change","reset-size","text-after-edge","text-before-edge"]},"dynamic-range-limit":{values:["standard","high","constrained-high"]},"empty-cells":{values:["show","hide"]},"field-sizing":{values:["fixed","content"]},"fill-rule":{values:["nonzero","evenodd"]},filter:{values:["none"]},"flex-basis":{values:["auto","fit-content","min-content","max-content","content"]},"flex-direction":{values:["row","row-reverse","column","column-reverse"]},"flex-wrap":{values:["nowrap","wrap","wrap-reverse"]},float:{values:["none","left","right","inline-start","inline-end"]},"flood-color":{values:["currentcolor"]},"font-feature-settings":{values:["normal"]},"font-kerning":{values:["auto","normal","none"]},"font-optical-sizing":{values:["auto","none"]},"font-palette":{values:["normal","light","dark"]},"font-size":{values:["xx-small","x-small","small","medium","large","x-large","xx-large","xxx-large","larger","smaller","-webkit-xxx-large"]},"font-size-adjust":{values:["none","ex-height","cap-height","ch-width","ic-width","from-font"]},"font-stretch":{values:["normal","ultra-condensed","extra-condensed","condensed","semi-condensed","semi-expanded","expanded","extra-expanded","ultra-expanded"]},"font-style":{values:["normal","italic","oblique"]},"font-synthesis-small-caps":{values:["auto","none"]},"font-synthesis-style":{values:["auto","none"]},"font-synthesis-weight":{values:["auto","none"]},"font-variant-alternates":{values:["normal"]},"font-variant-caps":{values:["normal","small-caps","all-small-caps","petite-caps","all-petite-caps","unicase","titling-caps"]},"font-variant-east-asian":{values:["normal","jis78","jis83","jis90","jis04","simplified","traditional","full-width","proportional-width","ruby"]},"font-variant-ligatures":{values:["normal","none","common-ligatures","no-common-ligatures","discretionary-ligatures","no-discretionary-ligatures","historical-ligatures","no-historical-ligatures","contextual","no-contextual"]},"font-variant-numeric":{values:["normal","lining-nums","oldstyle-nums","proportional-nums","tabular-nums","diagonal-fractions","stacked-fractions","ordinal","slashed-zero"]},"font-variant-position":{values:["normal","sub","super"]},"font-variation-settings":{values:["normal"]},"font-weight":{values:["normal","bold","bolder","lighter"]},"forced-color-adjust":{values:["auto","none","preserve-parent-color"]},"grid-auto-columns":{values:["auto","min-content","max-content"]},"grid-auto-flow":{values:["row","column"]},"grid-auto-rows":{values:["auto","min-content","max-content"]},"grid-column-end":{values:["auto"]},"grid-column-start":{values:["auto"]},"grid-row-end":{values:["auto"]},"grid-row-start":{values:["auto"]},"grid-template-areas":{values:["none"]},"grid-template-columns":{values:["none"]},"grid-template-rows":{values:["none"]},height:{values:["auto","fit-content","min-content","max-content"]},"hyphenate-limit-chars":{values:["auto"]},hyphens:{values:["none","manual","auto"]},"image-rendering":{values:["auto","optimizespeed","optimizequality","-webkit-optimize-contrast","pixelated"]},"initial-letter":{values:["drop","normal","raise"]},"inline-size":{values:["auto"]},"inset-area":{values:["none","top","bottom","center","left","right","x-start","x-end","y-start","y-end","start","end","self-start","self-end","all"]},isolation:{values:["auto","isolate"]},left:{values:["auto"]},"letter-spacing":{values:["normal"]},"lighting-color":{values:["currentcolor"]},"line-break":{values:["auto","loose","normal","strict","anywhere"]},"line-height":{values:["normal"]},"list-style-image":{values:["none"]},"list-style-position":{values:["outside","inside"]},"list-style-type":{values:["disc","circle","square","disclosure-open","disclosure-closed","decimal","none"]},"margin-block-end":{values:["auto"]},"margin-block-start":{values:["auto"]},"margin-bottom":{values:["auto"]},"margin-inline-end":{values:["auto"]},"margin-inline-start":{values:["auto"]},"margin-left":{values:["auto"]},"margin-right":{values:["auto"]},"margin-top":{values:["auto"]},"marker-end":{values:["none"]},"marker-mid":{values:["none"]},"marker-start":{values:["none"]},"mask-type":{values:["luminance","alpha"]},"math-shift":{values:["normal","compact"]},"math-style":{values:["normal","compact"]},"max-block-size":{values:["none"]},"max-height":{values:["none"]},"max-inline-size":{values:["none"]},"max-width":{values:["none"]},"mix-blend-mode":{values:["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-lighter"]},"object-fit":{values:["fill","contain","cover","none","scale-down"]},"object-view-box":{values:["none"]},"offset-anchor":{values:["auto"]},"offset-path":{values:["none"]},"offset-position":{values:["auto","normal"]},"offset-rotate":{values:["auto","reverse"]},"origin-trial-test-property":{values:["normal","none"]},"outline-color":{values:["currentcolor"]},"outline-style":{values:["none","hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"outline-width":{values:["thin","medium","thick"]},"overflow-anchor":{values:["visible","none","auto"]},"overflow-clip-margin":{values:["border-box","content-box","padding-box"]},"overflow-wrap":{values:["normal","break-word","anywhere"]},"overflow-x":{values:["visible","hidden","scroll","auto","overlay","clip"]},"overflow-y":{values:["visible","hidden","scroll","auto","overlay","clip"]},overlay:{values:["none","auto"]},"overscroll-behavior-x":{values:["auto","contain","none"]},"overscroll-behavior-y":{values:["auto","contain","none"]},page:{values:["auto"]},"paint-order":{values:["normal","fill","stroke","markers"]},perspective:{values:["none"]},"pointer-events":{values:["none","auto","stroke","fill","painted","visible","visiblestroke","visiblefill","visiblepainted","bounding-box","all"]},position:{values:["static","relative","absolute","fixed","sticky"]},"position-fallback":{values:["none"]},"position-fallback-bounds":{values:["normal"]},"position-try-options":{values:["none","flip-block","flip-inline","flip-start"]},"position-try-order":{values:["normal","most-width","most-height","most-block-size","most-inline-size"]},quotes:{values:["auto","none"]},"reading-order-items":{values:["normal","flex-visual","flex-flow","grid-rows","grid-columns","grid-order"]},resize:{values:["none","both","horizontal","vertical","block","inline"]},right:{values:["auto"]},"row-gap":{values:["normal"]},rx:{values:["auto"]},ry:{values:["auto"]},"scroll-behavior":{values:["auto","smooth"]},"scroll-padding-block-end":{values:["auto"]},"scroll-padding-block-start":{values:["auto"]},"scroll-padding-bottom":{values:["auto"]},"scroll-padding-inline-end":{values:["auto"]},"scroll-padding-inline-start":{values:["auto"]},"scroll-padding-left":{values:["auto"]},"scroll-padding-right":{values:["auto"]},"scroll-padding-top":{values:["auto"]},"scroll-snap-align":{values:["none","start","end","center"]},"scroll-snap-stop":{values:["normal","always"]},"scroll-snap-type":{values:["none","x","y","block","inline","both","mandatory","proximity"]},"scroll-start-block":{values:["auto","start","end","center","top","bottom","left","right"]},"scroll-start-inline":{values:["auto","start","end","center","top","bottom","left","right"]},"scroll-start-target-block":{values:["none","auto"]},"scroll-start-target-inline":{values:["none","auto"]},"scroll-start-target-x":{values:["none","auto"]},"scroll-start-target-y":{values:["none","auto"]},"scrollbar-color":{values:["auto"]},"scrollbar-gutter":{values:["auto","stable","both-edges"]},"scrollbar-width":{values:["auto","thin","none"]},"shape-margin":{values:["none"]},"shape-outside":{values:["none"]},"shape-rendering":{values:["auto","optimizespeed","crispedges","geometricprecision"]},speak:{values:["none","normal","spell-out","digits","literal-punctuation","no-punctuation"]},"stop-color":{values:["currentcolor"]},"stroke-dasharray":{values:["none"]},"stroke-linecap":{values:["butt","round","square"]},"stroke-linejoin":{values:["miter","bevel","round"]},"table-layout":{values:["auto","fixed"]},"text-align":{values:["left","right","center","justify","-webkit-left","-webkit-right","-webkit-center","start","end"]},"text-align-last":{values:["auto","start","end","left","right","center","justify"]},"text-anchor":{values:["start","middle","end"]},"text-autospace":{values:["normal","no-autospace"]},"text-box-trim":{values:["none","start","end","both"]},"text-combine-upright":{values:["none","all"]},"text-decoration-color":{values:["currentcolor"]},"text-decoration-line":{values:["none","underline","overline","line-through","blink","spelling-error","grammar-error"]},"text-decoration-skip-ink":{values:["none","auto"]},"text-decoration-style":{values:["solid","double","dotted","dashed","wavy"]},"text-decoration-thickness":{values:["auto","from-font"]},"text-emphasis-color":{values:["currentcolor"]},"text-orientation":{values:["sideways","mixed","upright"]},"text-overflow":{values:["clip","ellipsis"]},"text-rendering":{values:["auto","optimizespeed","optimizelegibility","geometricprecision"]},"text-shadow":{values:["none"]},"text-size-adjust":{values:["none","auto"]},"text-spacing-trim":{values:["normal","space-all","space-first","trim-start"]},"text-transform":{values:["capitalize","uppercase","lowercase","none","math-auto"]},"text-underline-offset":{values:["auto"]},"text-underline-position":{values:["auto","from-font","under","left","right"]},"text-wrap":{values:["wrap","nowrap","balance","pretty"]},top:{values:["auto"]},"touch-action":{values:["auto","none","pan-x","pan-left","pan-right","pan-y","pan-up","pan-down","pinch-zoom","manipulation"]},transform:{values:["none"]},"transform-box":{values:["content-box","border-box","fill-box","stroke-box","view-box"]},"transform-style":{values:["flat","preserve-3d"]},"transition-property":{values:["none"]},"transition-timing-function":{values:["linear","ease","ease-in","ease-out","ease-in-out","jump-both","jump-end","jump-none","jump-start","step-start","step-end"]},"unicode-bidi":{values:["normal","embed","bidi-override","isolate","plaintext","isolate-override"]},"user-select":{values:["auto","none","text","all","contain"]},"vector-effect":{values:["none","non-scaling-stroke"]},"vertical-align":{values:["baseline","sub","super","text-top","text-bottom","middle"]},"view-transition-class":{values:["none"]},"view-transition-name":{values:["none"]},visibility:{values:["visible","hidden","collapse"]},"white-space-collapse":{values:["collapse","preserve","preserve-breaks","break-spaces"]},width:{values:["auto","fit-content","min-content","max-content"]},"will-change":{values:["auto"]},"word-break":{values:["normal","break-all","keep-all","break-word","auto-phrase"]},"word-spacing":{values:["normal"]},"writing-mode":{values:["horizontal-tb","vertical-rl","vertical-lr"]},"z-index":{values:["auto"]}},p=new Map([["-epub-caption-side","caption-side"],["-epub-text-combine","-webkit-text-combine"],["-epub-text-emphasis","text-emphasis"],["-epub-text-emphasis-color","text-emphasis-color"],["-epub-text-emphasis-style","text-emphasis-style"],["-epub-text-orientation","-webkit-text-orientation"],["-epub-text-transform","text-transform"],["-epub-word-break","word-break"],["-epub-writing-mode","-webkit-writing-mode"],["-webkit-align-content","align-content"],["-webkit-align-items","align-items"],["-webkit-align-self","align-self"],["-webkit-alternative-animation-delay","-alternative-animation-delay"],["-webkit-alternative-animation-with-delay-start-end","-alternative-animation-with-delay-start-end"],["-webkit-alternative-animation-with-timeline","-alternative-animation-with-timeline"],["-webkit-alternative-mask","-alternative-mask"],["-webkit-alternative-mask-clip","mask-clip"],["-webkit-alternative-mask-composite","mask-composite"],["-webkit-alternative-mask-image","mask-image"],["-webkit-alternative-mask-origin","mask-origin"],["-webkit-alternative-mask-position","mask-position"],["-webkit-alternative-mask-repeat","mask-repeat"],["-webkit-alternative-mask-size","mask-size"],["-webkit-animation","animation"],["-webkit-animation-delay","animation-delay"],["-webkit-animation-direction","animation-direction"],["-webkit-animation-duration","animation-duration"],["-webkit-animation-fill-mode","animation-fill-mode"],["-webkit-animation-iteration-count","animation-iteration-count"],["-webkit-animation-name","animation-name"],["-webkit-animation-play-state","animation-play-state"],["-webkit-animation-timing-function","animation-timing-function"],["-webkit-app-region","app-region"],["-webkit-appearance","appearance"],["-webkit-backface-visibility","backface-visibility"],["-webkit-background-clip","background-clip"],["-webkit-background-origin","background-origin"],["-webkit-background-size","background-size"],["-webkit-border-after","border-block-end"],["-webkit-border-after-color","border-block-end-color"],["-webkit-border-after-style","border-block-end-style"],["-webkit-border-after-width","border-block-end-width"],["-webkit-border-before","border-block-start"],["-webkit-border-before-color","border-block-start-color"],["-webkit-border-before-style","border-block-start-style"],["-webkit-border-before-width","border-block-start-width"],["-webkit-border-bottom-left-radius","border-bottom-left-radius"],["-webkit-border-bottom-right-radius","border-bottom-right-radius"],["-webkit-border-end","border-inline-end"],["-webkit-border-end-color","border-inline-end-color"],["-webkit-border-end-style","border-inline-end-style"],["-webkit-border-end-width","border-inline-end-width"],["-webkit-border-radius","border-radius"],["-webkit-border-start","border-inline-start"],["-webkit-border-start-color","border-inline-start-color"],["-webkit-border-start-style","border-inline-start-style"],["-webkit-border-start-width","border-inline-start-width"],["-webkit-border-top-left-radius","border-top-left-radius"],["-webkit-border-top-right-radius","border-top-right-radius"],["-webkit-box-shadow","box-shadow"],["-webkit-box-sizing","box-sizing"],["-webkit-clip-path","clip-path"],["-webkit-column-count","column-count"],["-webkit-column-gap","column-gap"],["-webkit-column-rule","column-rule"],["-webkit-column-rule-color","column-rule-color"],["-webkit-column-rule-style","column-rule-style"],["-webkit-column-rule-width","column-rule-width"],["-webkit-column-span","column-span"],["-webkit-column-width","column-width"],["-webkit-columns","columns"],["-webkit-filter","filter"],["-webkit-flex","flex"],["-webkit-flex-basis","flex-basis"],["-webkit-flex-direction","flex-direction"],["-webkit-flex-flow","flex-flow"],["-webkit-flex-grow","flex-grow"],["-webkit-flex-shrink","flex-shrink"],["-webkit-flex-wrap","flex-wrap"],["-webkit-font-feature-settings","font-feature-settings"],["-webkit-hyphenate-character","hyphenate-character"],["-webkit-justify-content","justify-content"],["-webkit-logical-height","block-size"],["-webkit-logical-width","inline-size"],["-webkit-margin-after","margin-block-end"],["-webkit-margin-before","margin-block-start"],["-webkit-margin-end","margin-inline-end"],["-webkit-margin-start","margin-inline-start"],["-webkit-max-logical-height","max-block-size"],["-webkit-max-logical-width","max-inline-size"],["-webkit-min-logical-height","min-block-size"],["-webkit-min-logical-width","min-inline-size"],["-webkit-opacity","opacity"],["-webkit-order","order"],["-webkit-padding-after","padding-block-end"],["-webkit-padding-before","padding-block-start"],["-webkit-padding-end","padding-inline-end"],["-webkit-padding-start","padding-inline-start"],["-webkit-perspective","perspective"],["-webkit-perspective-origin","perspective-origin"],["-webkit-shape-image-threshold","shape-image-threshold"],["-webkit-shape-margin","shape-margin"],["-webkit-shape-outside","shape-outside"],["-webkit-text-emphasis","text-emphasis"],["-webkit-text-emphasis-color","text-emphasis-color"],["-webkit-text-emphasis-position","text-emphasis-position"],["-webkit-text-emphasis-style","text-emphasis-style"],["-webkit-text-size-adjust","text-size-adjust"],["-webkit-transform","transform"],["-webkit-transform-origin","transform-origin"],["-webkit-transform-style","transform-style"],["-webkit-transition","transition"],["-webkit-transition-delay","transition-delay"],["-webkit-transition-duration","transition-duration"],["-webkit-transition-property","transition-property"],["-webkit-transition-timing-function","transition-timing-function"],["-webkit-user-select","user-select"],["word-wrap","overflow-wrap"]]);class m{#t;#r;#n;#s;#i;#a;#o;#l;#d;#c;constructor(e,r){this.#t=[],this.#r=new Map,this.#n=new Map,this.#s=new Set,this.#i=new Set,this.#a=new Map,this.#o=r;for(let t=0;t<e.length;++t){const r=e[t],n=r.name;if(!CSS.supports(n,"initial"))continue;this.#t.push(n),r.inherited&&this.#s.add(n),r.svg&&this.#i.add(n);const s=e[t].longhands;if(s){this.#r.set(n,s);for(let e=0;e<s.length;++e){const t=s[e];let r=this.#n.get(t);r||(r=[],this.#n.set(t,r)),r.push(n)}}}this.#t.sort(m.sortPrefixesAndCSSWideKeywordsToEnd),this.#l=new Set(this.#t);const n=new Map;for(const[e,t]of Object.entries(g))n.set(e,new Set(t.values));for(const[e,r]of Object.entries(P)){const s=n.get(e);s?t.SetUtilities.addAll(s,r.values):n.set(e,new Set(r.values))}for(const[e,t]of n){for(const r of A)!t.has(r)&&CSS.supports(e,r)&&t.add(r);this.#a.set(e,[...t])}this.#d=[],this.#c=[];for(const e of this.#l){const t=this.specificPropertyValues(e).filter((t=>CSS.supports(e,t))).sort(m.sortPrefixesAndCSSWideKeywordsToEnd).map((t=>`${e}: ${t}`));this.isSVGProperty(e)||this.#d.push(...t),this.#c.push(...t)}}static sortPrefixesAndCSSWideKeywordsToEnd(e,t){const r=f.includes(e),n=f.includes(t);if(r&&!n)return 1;if(!r&&n)return-1;const s=e.startsWith("-webkit-"),i=t.startsWith("-webkit-");return s&&!i?1:!s&&i||e<t?-1:e>t?1:0}allProperties(){return this.#t}aliasesFor(){return this.#o}nameValuePresets(e){return e?this.#c:this.#d}isSVGProperty(e){return e=e.toLowerCase(),this.#i.has(e)}getLonghands(e){return this.#r.get(e)||null}getShorthands(e){return this.#n.get(e)||null}isColorAwareProperty(e){return T.has(e.toLowerCase())||this.isCustomProperty(e.toLowerCase())}isFontFamilyProperty(e){return"font-family"===e.toLowerCase()}isAngleAwareProperty(e){const t=e.toLowerCase();return T.has(t)||M.has(t)}isGridAreaDefiningProperty(e){return"grid"===(e=e.toLowerCase())||"grid-template"===e||"grid-template-areas"===e}isLengthProperty(e){return"line-height"!==(e=e.toLowerCase())&&(C.has(e)||e.startsWith("margin")||e.startsWith("padding")||-1!==e.indexOf("width")||-1!==e.indexOf("height"))}isBezierAwareProperty(e){return e=e.toLowerCase(),R.has(e)||this.isCustomProperty(e)}isFontAwareProperty(e){return e=e.toLowerCase(),x.has(e)||this.isCustomProperty(e)}isCustomProperty(e){return e.startsWith("--")}isShadowProperty(e){return"box-shadow"===(e=e.toLowerCase())||"text-shadow"===e||"-webkit-box-shadow"===e}isStringProperty(e){return"content"===(e=e.toLowerCase())}canonicalPropertyName(e){if(this.isCustomProperty(e))return e;e=e.toLowerCase();const t=this.#o.get(e);if(t)return t;if(!e||e.length<9||"-"!==e.charAt(0))return e;const r=e.match(/(?:-webkit-)(.+)/);return r&&this.#l.has(r[1])?r[1]:e}isCSSPropertyName(e){return!!((e=e.toLowerCase()).startsWith("--")&&e.length>2||e.startsWith("-moz-")||e.startsWith("-ms-")||e.startsWith("-o-")||e.startsWith("-webkit-"))||this.#l.has(e)}isPropertyInherited(e){return(e=e.toLowerCase()).startsWith("--")||this.#s.has(this.canonicalPropertyName(e))||this.#s.has(e)}specificPropertyValues(e){const t=e.replace(/^-webkit-/,""),r=this.#a;let n=r.get(e)||r.get(t);if(!n){n=[];for(const t of A)CSS.supports(e,t)&&n.push(t);r.set(e,n)}return n}getPropertyValues(t){t=t.toLowerCase();const r=[...this.specificPropertyValues(t),...f];if(this.isColorAwareProperty(t)){r.push("currentColor");for(const t of e.Color.Nicknames.keys())r.push(t)}return r.sort(m.sortPrefixesAndCSSWideKeywordsToEnd)}propertyUsageWeight(e){return L.get(e)||L.get(this.canonicalPropertyName(e))||0}getValuePreset(e,t){const r=S.get(e);let n=r?r.get(t):null;if(!n)return null;let s=n.length,i=n.length;return n&&(s=n.indexOf("|"),i=n.lastIndexOf("|"),i=s===i?i:i-1,n=n.replace(/\|/g,"")),{text:n,startColumn:s,endColumn:i}}isHighlightPseudoType(e){return"highlight"===e||"selection"===e||"target-text"===e||"grammar-error"===e||"spelling-error"===e}}const f=["inherit","initial","revert","revert-layer","unset"],b=/(var\(\s*--.*?\))/gs,y=/((?:\[[\w\- ]+\]\s*)*(?:"[^"]+"|'[^']+'))[^'"\[]*\[?[^'"\[]*/;let v=null;function I(){if(!v){v=new m(u,p)}return v}const k=new Map([["linear-gradient","linear-gradient(|45deg, black, transparent|)"],["radial-gradient","radial-gradient(|black, transparent|)"],["repeating-linear-gradient","repeating-linear-gradient(|45deg, black, transparent 100px|)"],["repeating-radial-gradient","repeating-radial-gradient(|black, transparent 100px|)"],["url","url(||)"]]),w=new Map([["blur","blur(|1px|)"],["brightness","brightness(|0.5|)"],["contrast","contrast(|0.5|)"],["drop-shadow","drop-shadow(|2px 4px 6px black|)"],["grayscale","grayscale(|1|)"],["hue-rotate","hue-rotate(|45deg|)"],["invert","invert(|1|)"],["opacity","opacity(|0.5|)"],["saturate","saturate(|0.5|)"],["sepia","sepia(|1|)"],["url","url(||)"]]),S=new Map([["filter",w],["backdrop-filter",w],["background",k],["background-image",k],["-webkit-mask-image",k],["transform",new Map([["scale","scale(|1.5|)"],["scaleX","scaleX(|1.5|)"],["scaleY","scaleY(|1.5|)"],["scale3d","scale3d(|1.5, 1.5, 1.5|)"],["rotate","rotate(|45deg|)"],["rotateX","rotateX(|45deg|)"],["rotateY","rotateY(|45deg|)"],["rotateZ","rotateZ(|45deg|)"],["rotate3d","rotate3d(|1, 1, 1, 45deg|)"],["skew","skew(|10deg, 10deg|)"],["skewX","skewX(|10deg|)"],["skewY","skewY(|10deg|)"],["translate","translate(|10px, 10px|)"],["translateX","translateX(|10px|)"],["translateY","translateY(|10px|)"],["translateZ","translateZ(|10px|)"],["translate3d","translate3d(|10px, 10px, 10px|)"],["matrix","matrix(|1, 0, 0, 1, 0, 0|)"],["matrix3d","matrix3d(|1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1|)"],["perspective","perspective(|10px|)"]])]]),C=new Set(["background-position","border-spacing","bottom","font-size","height","left","letter-spacing","max-height","max-width","min-height","min-width","right","text-indent","top","width","word-spacing","grid-row-gap","grid-column-gap","row-gap"]),R=new Set(["animation","animation-timing-function","transition","transition-timing-function","-webkit-animation","-webkit-animation-timing-function","-webkit-transition","-webkit-transition-timing-function"]),x=new Set(["font-size","line-height","font-weight","font-family","letter-spacing"]),T=new Set(["accent-color","background","background-color","background-image","border","border-color","border-image","border-image-source","border-bottom","border-bottom-color","border-left","border-left-color","border-right","border-right-color","border-top","border-top-color","box-shadow","caret-color","color","column-rule","column-rule-color","content","fill","list-style-image","mask","mask-image","mask-border","mask-border-source","outline","outline-color","scrollbar-color","stop-color","stroke","text-decoration-color","text-shadow","-webkit-border-after","-webkit-border-after-color","-webkit-border-before","-webkit-border-before-color","-webkit-border-end","-webkit-border-end-color","-webkit-border-start","-webkit-border-start-color","-webkit-box-reflect","-webkit-box-shadow","-webkit-column-rule-color","-webkit-mask","-webkit-mask-box-image","-webkit-mask-box-image-source","-webkit-mask-image","-webkit-tap-highlight-color","-webkit-text-emphasis","-webkit-text-emphasis-color","-webkit-text-fill-color","-webkit-text-stroke","-webkit-text-stroke-color"]),M=new Set(["-webkit-border-image","transform","-webkit-transform","rotate","filter","-webkit-filter","backdrop-filter","offset","offset-rotate","font-style"]),P={"background-repeat":{values:["repeat","repeat-x","repeat-y","no-repeat","space","round"]},content:{values:["normal","close-quote","no-close-quote","no-open-quote","open-quote"]},"baseline-shift":{values:["baseline"]},"max-height":{values:["min-content","max-content","-webkit-fill-available","fit-content"]},color:{values:["black"]},"background-color":{values:["white"]},"box-shadow":{values:["inset"]},"text-shadow":{values:["0 0 black"]},"-webkit-writing-mode":{values:["horizontal-tb","vertical-rl","vertical-lr"]},"writing-mode":{values:["lr","rl","tb","lr-tb","rl-tb","tb-rl"]},"page-break-inside":{values:["avoid"]},cursor:{values:["-webkit-zoom-in","-webkit-zoom-out","-webkit-grab","-webkit-grabbing"]},"border-width":{values:["medium","thick","thin"]},"border-style":{values:["hidden","inset","groove","ridge","outset","dotted","dashed","solid","double"]},size:{values:["a3","a4","a5","b4","b5","landscape","ledger","legal","letter","portrait"]},overflow:{values:["hidden","visible","overlay","scroll"]},"overscroll-behavior":{values:["contain"]},"text-rendering":{values:["optimizeSpeed","optimizeLegibility","geometricPrecision"]},"text-align":{values:["-webkit-auto","-webkit-match-parent"]},"clip-path":{values:["circle","ellipse","inset","polygon","url"]},"color-interpolation":{values:["sRGB","linearRGB"]},"word-wrap":{values:["normal","break-word"]},"font-weight":{values:["100","200","300","400","500","600","700","800","900"]},"-webkit-text-emphasis":{values:["circle","filled","open","dot","double-circle","triangle","sesame"]},"color-rendering":{values:["optimizeSpeed","optimizeQuality"]},"-webkit-text-combine":{values:["horizontal"]},"text-orientation":{values:["sideways-right"]},outline:{values:["inset","groove","ridge","outset","dotted","dashed","solid","double","medium","thick","thin"]},font:{values:["caption","icon","menu","message-box","small-caption","-webkit-mini-control","-webkit-small-control","-webkit-control","status-bar"]},"dominant-baseline":{values:["text-before-edge","text-after-edge","use-script","no-change","reset-size"]},"-webkit-text-emphasis-position":{values:["over","under"]},"alignment-baseline":{values:["before-edge","after-edge","text-before-edge","text-after-edge","hanging"]},"page-break-before":{values:["left","right","always","avoid"]},"border-image":{values:["repeat","stretch","space","round"]},"text-decoration":{values:["blink","line-through","overline","underline","wavy","double","solid","dashed","dotted"]},"font-family":{values:["serif","sans-serif","cursive","fantasy","monospace","system-ui","emoji","math","fangsong","ui-serif","ui-sans-serif","ui-monospace","ui-rounded","-webkit-body"]},zoom:{values:["normal"]},"max-width":{values:["min-content","max-content","-webkit-fill-available","fit-content"]},"-webkit-font-smoothing":{values:["antialiased","subpixel-antialiased"]},border:{values:["hidden","inset","groove","ridge","outset","dotted","dashed","solid","double","medium","thick","thin"]},"font-variant":{values:["small-caps","normal","common-ligatures","no-common-ligatures","discretionary-ligatures","no-discretionary-ligatures","historical-ligatures","no-historical-ligatures","contextual","no-contextual","all-small-caps","petite-caps","all-petite-caps","unicase","titling-caps","lining-nums","oldstyle-nums","proportional-nums","tabular-nums","diagonal-fractions","stacked-fractions","ordinal","slashed-zero","jis78","jis83","jis90","jis04","simplified","traditional","full-width","proportional-width","ruby"]},"vertical-align":{values:["top","bottom","-webkit-baseline-middle"]},"page-break-after":{values:["left","right","always","avoid"]},"-webkit-text-emphasis-style":{values:["circle","filled","open","dot","double-circle","triangle","sesame"]},transform:{values:["scale","scaleX","scaleY","scale3d","rotate","rotateX","rotateY","rotateZ","rotate3d","skew","skewX","skewY","translate","translateX","translateY","translateZ","translate3d","matrix","matrix3d","perspective"]},"align-content":{values:["normal","baseline","space-between","space-around","space-evenly","stretch","center","start","end","flex-start","flex-end"]},"justify-content":{values:["normal","space-between","space-around","space-evenly","stretch","center","start","end","flex-start","flex-end","left","right"]},"place-content":{values:["normal","space-between","space-around","space-evenly","stretch","center","start","end","flex-start","flex-end","baseline"]},"align-items":{values:["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end"]},"justify-items":{values:["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end","left","right","legacy"]},"place-items":{values:["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end"]},"align-self":{values:["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end"]},"justify-self":{values:["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end","left","right"]},"place-self":{values:["normal","stretch","baseline","center","start","end","self-start","self-end","flex-start","flex-end"]},"perspective-origin":{values:["left","center","right","top","bottom"]},"transform-origin":{values:["left","center","right","top","bottom"]},"transition-timing-function":{values:["cubic-bezier","steps"]},"animation-timing-function":{values:["cubic-bezier","steps"]},"-webkit-backface-visibility":{values:["visible","hidden"]},"-webkit-column-break-after":{values:["always","avoid"]},"-webkit-column-break-before":{values:["always","avoid"]},"-webkit-column-break-inside":{values:["avoid"]},"-webkit-column-span":{values:["all"]},"-webkit-column-gap":{values:["normal"]},filter:{values:["url","blur","brightness","contrast","drop-shadow","grayscale","hue-rotate","invert","opacity","saturate","sepia"]},"backdrop-filter":{values:["url","blur","brightness","contrast","drop-shadow","grayscale","hue-rotate","invert","opacity","saturate","sepia"]},"grid-template-columns":{values:["min-content","max-content"]},"grid-template-rows":{values:["min-content","max-content"]},"grid-auto-flow":{values:["dense"]},background:{values:["repeat","repeat-x","repeat-y","no-repeat","top","bottom","left","right","center","fixed","local","scroll","space","round","border-box","content-box","padding-box","linear-gradient","radial-gradient","repeating-linear-gradient","repeating-radial-gradient","url"]},"background-image":{values:["linear-gradient","radial-gradient","repeating-linear-gradient","repeating-radial-gradient","url"]},"background-position":{values:["top","bottom","left","right","center"]},"background-position-x":{values:["left","right","center"]},"background-position-y":{values:["top","bottom","center"]},"background-repeat-x":{values:["repeat","no-repeat"]},"background-repeat-y":{values:["repeat","no-repeat"]},"border-bottom":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"]},"border-left":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"]},"border-right":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"]},"border-top":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"]},"buffered-rendering":{values:["static","dynamic"]},"color-interpolation-filters":{values:["srgb","linearrgb"]},"column-rule":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"]},"flex-flow":{values:["nowrap","row","row-reverse","column","column-reverse","wrap","wrap-reverse"]},height:{values:["-webkit-fill-available"]},"inline-size":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"list-style":{values:["outside","inside","disc","circle","square","decimal","decimal-leading-zero","arabic-indic","bengali","cambodian","khmer","devanagari","gujarati","gurmukhi","kannada","lao","malayalam","mongolian","myanmar","oriya","persian","urdu","telugu","tibetan","thai","lower-roman","upper-roman","lower-greek","lower-alpha","lower-latin","upper-alpha","upper-latin","cjk-earthly-branch","cjk-heavenly-stem","ethiopic-halehame","ethiopic-halehame-am","ethiopic-halehame-ti-er","ethiopic-halehame-ti-et","hangul","hangul-consonant","korean-hangul-formal","korean-hanja-formal","korean-hanja-informal","hebrew","armenian","lower-armenian","upper-armenian","georgian","cjk-ideographic","simp-chinese-formal","simp-chinese-informal","trad-chinese-formal","trad-chinese-informal","hiragana","katakana","hiragana-iroha","katakana-iroha"]},"max-block-size":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"max-inline-size":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"min-block-size":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"min-height":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"min-inline-size":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"min-width":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"object-position":{values:["top","bottom","left","right","center"]},"shape-outside":{values:["border-box","content-box","padding-box","margin-box"]},"-webkit-appearance":{values:["checkbox","radio","push-button","square-button","button","inner-spin-button","listbox","media-slider","media-sliderthumb","media-volume-slider","media-volume-sliderthumb","menulist","menulist-button","meter","progress-bar","slider-horizontal","slider-vertical","sliderthumb-horizontal","sliderthumb-vertical","searchfield","searchfield-cancel-button","textfield","textarea"]},"-webkit-border-after":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"]},"-webkit-border-after-style":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"-webkit-border-after-width":{values:["medium","thick","thin"]},"-webkit-border-before":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"]},"-webkit-border-before-style":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"-webkit-border-before-width":{values:["medium","thick","thin"]},"-webkit-border-end":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"]},"-webkit-border-end-style":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"-webkit-border-end-width":{values:["medium","thick","thin"]},"-webkit-border-start":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double","medium","thick","thin"]},"-webkit-border-start-style":{values:["hidden","inset","groove","outset","ridge","dotted","dashed","solid","double"]},"-webkit-border-start-width":{values:["medium","thick","thin"]},"-webkit-logical-height":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"-webkit-logical-width":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"-webkit-mask-box-image":{values:["repeat","stretch","space","round"]},"-webkit-mask-box-image-repeat":{values:["repeat","stretch","space","round"]},"-webkit-mask-clip":{values:["text","border","border-box","content","content-box","padding","padding-box"]},"-webkit-mask-composite":{values:["clear","copy","source-over","source-in","source-out","source-atop","destination-over","destination-in","destination-out","destination-atop","xor","plus-lighter"]},"-webkit-mask-image":{values:["linear-gradient","radial-gradient","repeating-linear-gradient","repeating-radial-gradient","url"]},"-webkit-mask-origin":{values:["border","border-box","content","content-box","padding","padding-box"]},"-webkit-mask-position":{values:["top","bottom","left","right","center"]},"-webkit-mask-position-x":{values:["left","right","center"]},"-webkit-mask-position-y":{values:["top","bottom","center"]},"-webkit-mask-repeat":{values:["repeat","repeat-x","repeat-y","no-repeat","space","round"]},"-webkit-mask-size":{values:["contain","cover"]},"-webkit-max-logical-height":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"-webkit-max-logical-width":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"-webkit-min-logical-height":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"-webkit-min-logical-width":{values:["-webkit-fill-available","min-content","max-content","fit-content"]},"-webkit-perspective-origin-x":{values:["left","right","center"]},"-webkit-perspective-origin-y":{values:["top","bottom","center"]},"-webkit-text-decorations-in-effect":{values:["blink","line-through","overline","underline"]},"-webkit-text-stroke":{values:["medium","thick","thin"]},"-webkit-text-stroke-width":{values:["medium","thick","thin"]},"-webkit-transform-origin-x":{values:["left","right","center"]},"-webkit-transform-origin-y":{values:["top","bottom","center"]},width:{values:["-webkit-fill-available"]},"contain-intrinsic-width":{values:["auto none","auto 100px"]},"contain-intrinsic-height":{values:["auto none","auto 100px"]},"contain-intrinsic-size":{values:["auto none","auto 100px"]},"contain-intrinsic-inline-size":{values:["auto none","auto 100px"]},"contain-intrinsic-block-size":{values:["auto none","auto 100px"]},"white-space":{values:["normal","pre","pre-wrap","pre-line","nowrap","break-spaces"]}},L=new Map([["align-content",57],["align-items",129],["align-self",55],["animation",175],["animation-delay",114],["animation-direction",113],["animation-duration",137],["animation-fill-mode",132],["animation-iteration-count",124],["animation-name",139],["animation-play-state",104],["animation-timing-function",141],["backface-visibility",123],["background",260],["background-attachment",119],["background-clip",165],["background-color",259],["background-image",246],["background-origin",107],["background-position",237],["background-position-x",108],["background-position-y",93],["background-repeat",234],["background-size",203],["border",263],["border-bottom",233],["border-bottom-color",190],["border-bottom-left-radius",186],["border-bottom-right-radius",185],["border-bottom-style",150],["border-bottom-width",179],["border-collapse",209],["border-color",226],["border-image",89],["border-image-outset",50],["border-image-repeat",49],["border-image-slice",58],["border-image-source",32],["border-image-width",52],["border-left",221],["border-left-color",174],["border-left-style",142],["border-left-width",172],["border-radius",224],["border-right",223],["border-right-color",182],["border-right-style",130],["border-right-width",178],["border-spacing",198],["border-style",206],["border-top",231],["border-top-color",192],["border-top-left-radius",187],["border-top-right-radius",189],["border-top-style",152],["border-top-width",180],["border-width",214],["bottom",227],["box-shadow",213],["box-sizing",216],["caption-side",96],["clear",229],["clip",173],["clip-rule",5],["color",256],["content",219],["counter-increment",111],["counter-reset",110],["cursor",250],["direction",176],["display",262],["empty-cells",99],["fill",140],["fill-opacity",82],["fill-rule",22],["filter",160],["flex",133],["flex-basis",66],["flex-direction",85],["flex-flow",94],["flex-grow",112],["flex-shrink",61],["flex-wrap",68],["float",252],["font",211],["font-family",254],["font-kerning",18],["font-size",264],["font-stretch",77],["font-style",220],["font-variant",161],["font-weight",257],["height",266],["image-rendering",90],["justify-content",127],["left",248],["letter-spacing",188],["line-height",244],["list-style",215],["list-style-image",145],["list-style-position",149],["list-style-type",199],["margin",267],["margin-bottom",241],["margin-left",243],["margin-right",238],["margin-top",253],["mask",20],["max-height",205],["max-width",225],["min-height",217],["min-width",218],["object-fit",33],["opacity",251],["order",117],["orphans",146],["outline",222],["outline-color",153],["outline-offset",147],["outline-style",151],["outline-width",148],["overflow",255],["overflow-wrap",105],["overflow-x",184],["overflow-y",196],["padding",265],["padding-bottom",230],["padding-left",235],["padding-right",232],["padding-top",240],["page",8],["page-break-after",120],["page-break-before",69],["page-break-inside",121],["perspective",92],["perspective-origin",103],["pointer-events",183],["position",261],["quotes",158],["resize",168],["right",245],["shape-rendering",38],["size",64],["speak",118],["src",170],["stop-color",42],["stop-opacity",31],["stroke",98],["stroke-dasharray",36],["stroke-dashoffset",3],["stroke-linecap",30],["stroke-linejoin",21],["stroke-miterlimit",12],["stroke-opacity",34],["stroke-width",87],["table-layout",171],["tab-size",46],["text-align",260],["text-anchor",35],["text-decoration",247],["text-indent",207],["text-overflow",204],["text-rendering",155],["text-shadow",208],["text-transform",202],["top",258],["touch-action",80],["transform",181],["transform-origin",162],["transform-style",86],["transition",193],["transition-delay",134],["transition-duration",135],["transition-property",131],["transition-timing-function",122],["unicode-bidi",156],["unicode-range",136],["vertical-align",236],["visibility",242],["-webkit-appearance",191],["-webkit-backface-visibility",154],["-webkit-background-clip",164],["-webkit-background-origin",40],["-webkit-background-size",163],["-webkit-border-end",9],["-webkit-border-horizontal-spacing",81],["-webkit-border-image",75],["-webkit-border-radius",212],["-webkit-border-start",10],["-webkit-border-start-color",16],["-webkit-border-start-width",13],["-webkit-border-vertical-spacing",43],["-webkit-box-align",101],["-webkit-box-direction",51],["-webkit-box-flex",128],["-webkit-box-ordinal-group",91],["-webkit-box-orient",144],["-webkit-box-pack",106],["-webkit-box-reflect",39],["-webkit-box-shadow",210],["-webkit-column-break-inside",60],["-webkit-column-count",84],["-webkit-column-gap",76],["-webkit-column-rule",25],["-webkit-column-rule-color",23],["-webkit-columns",44],["-webkit-column-span",29],["-webkit-column-width",47],["-webkit-filter",159],["-webkit-font-feature-settings",59],["-webkit-font-smoothing",177],["-webkit-line-break",45],["-webkit-line-clamp",126],["-webkit-margin-after",67],["-webkit-margin-before",70],["-webkit-margin-collapse",14],["-webkit-margin-end",65],["-webkit-margin-start",100],["-webkit-mask",19],["-webkit-mask-box-image",72],["-webkit-mask-image",88],["-webkit-mask-position",54],["-webkit-mask-repeat",63],["-webkit-mask-size",79],["-webkit-padding-after",15],["-webkit-padding-before",28],["-webkit-padding-end",48],["-webkit-padding-start",73],["-webkit-print-color-adjust",83],["-webkit-rtl-ordering",7],["-webkit-tap-highlight-color",169],["-webkit-text-emphasis-color",11],["-webkit-text-fill-color",71],["-webkit-text-security",17],["-webkit-text-stroke",56],["-webkit-text-stroke-color",37],["-webkit-text-stroke-width",53],["-webkit-user-drag",95],["-webkit-user-modify",62],["-webkit-user-select",194],["-webkit-writing-mode",4],["white-space",228],["widows",115],["width",268],["will-change",74],["word-break",166],["word-spacing",157],["word-wrap",197],["writing-mode",41],["z-index",239],["zoom",200]]),A=["auto","none"];var E=Object.freeze({__proto__:null,CSSMetadata:m,CSSWideKeywords:f,VariableNameRegex:/(\s*--.*?)/gs,VariableRegex:b,CustomVariableRegex:/(var\(*--[\w\d]+-([\w]+-[\w]+)\))/g,URLRegex:/url\(\s*('.+?'|".+?"|[^)]+)\s*\)/g,GridAreaRowRegex:y,cssMetadata:I});class O extends n.ProfileTreeModel.ProfileTreeModel{}var N=Object.freeze({__proto__:null,ProfileTreeModel:O});const D="<opaque>";class F{#h;#u;#g;#p;#m;#f;#b;constructor(e,t,r,n){this.#h=e,this.#u=t,this.#g=r,this.#p=new Map,this.#m=0,this.#f=n||"Medium",this.#b=null}static fromProtocolCookie(e){const t=new F(e.name,e.value,null,e.priority);return t.addAttribute("domain",e.domain),t.addAttribute("path",e.path),e.expires&&t.addAttribute("expires",1e3*e.expires),e.httpOnly&&t.addAttribute("http-only"),e.secure&&t.addAttribute("secure"),e.sameSite&&t.addAttribute("same-site",e.sameSite),"sourcePort"in e&&t.addAttribute("source-port",e.sourcePort),"sourceScheme"in e&&t.addAttribute("source-scheme",e.sourceScheme),"partitionKey"in e&&t.addAttribute("partition-key",e.partitionKey),"partitionKeyOpaque"in e&&e.partitionKeyOpaque&&t.addAttribute("partition-key",D),t.setSize(e.size),t}isEqual(e){return this.name()===e.name()&&this.value()===e.value()&&this.size()===e.size()&&this.domain()===e.domain()&&this.path()===e.path()&&this.expires()===e.expires()&&this.httpOnly()===e.httpOnly()&&this.secure()===e.secure()&&this.sameSite()===e.sameSite()&&this.sourceScheme()===e.sourceScheme()&&this.sourcePort()===e.sourcePort()&&this.priority()===e.priority()&&this.partitionKey()===e.partitionKey()&&this.type()===e.type()&&this.getCookieLine()===e.getCookieLine()}key(){return(this.domain()||"-")+" "+this.name()+" "+(this.path()||"-")+" "+(this.partitionKey()||"-")}name(){return this.#h}value(){return this.#u}type(){return this.#g}httpOnly(){return this.#p.has("http-only")}secure(){return this.#p.has("secure")}partitioned(){return this.#p.has("partitioned")||Boolean(this.partitionKey())||this.partitionKeyOpaque()}sameSite(){return this.#p.get("same-site")}partitionKey(){return this.#p.get("partition-key")}setPartitionKey(e){this.addAttribute("partition-key",e)}partitionKeyOpaque(){return this.#p.get("partition-key")===D}setPartitionKeyOpaque(){this.addAttribute("partition-key",D)}priority(){return this.#f}session(){return!(this.#p.has("expires")||this.#p.has("max-age"))}path(){return this.#p.get("path")}domain(){return this.#p.get("domain")}expires(){return this.#p.get("expires")}maxAge(){return this.#p.get("max-age")}sourcePort(){return this.#p.get("source-port")}sourceScheme(){return this.#p.get("source-scheme")}size(){return this.#m}url(){if(!this.domain()||!this.path())return null;let e="";const t=this.sourcePort();return t&&80!==t&&443!==t&&(e=`:${this.sourcePort()}`),(this.secure()?"https://":"http://")+this.domain()+e+this.path()}setSize(e){this.#m=e}expiresDate(e){return this.maxAge()?new Date(e.getTime()+1e3*this.maxAge()):this.expires()?new Date(this.expires()):null}addAttribute(e,t){if(e)if("priority"===e)this.#f=t;else this.#p.set(e,t)}setCookieLine(e){this.#b=e}getCookieLine(){return this.#b}matchesSecurityOrigin(e){const t=new URL(e).hostname;return F.isDomainMatch(this.domain(),t)}static isDomainMatch(e,t){return t===e||!(!e||"."!==e[0])&&(e.substr(1)===t||t.length>e.length&&t.endsWith(e))}}var B=Object.freeze({__proto__:null,Cookie:F});class U{#y;#v;#I;#k;#w;constructor(e){this.#y=e.fontFamily,this.#v=e.fontVariationAxes||[],this.#I=new Map,this.#k=e.src,this.#w=e.fontDisplay;for(const e of this.#v)this.#I.set(e.tag,e)}getFontFamily(){return this.#y}getSrc(){return this.#k}getFontDisplay(){return this.#w}getVariationAxisByTag(e){return this.#I.get(e)}}var H=Object.freeze({__proto__:null,CSSFontFace:U});class q{text="";range;styleSheetId;cssModel;constructor(e){this.cssModel=e}rebase(e){this.styleSheetId===e.styleSheetId&&this.range&&(e.oldRange.equal(this.range)?this.reinitialize(e.payload):this.range=this.range.rebaseAfterTextEdit(e.oldRange,e.newRange))}equal(e){return!!(this.styleSheetId&&this.range&&e.range)&&(this.styleSheetId===e.styleSheetId&&this.range.equal(e.range))}lineNumberInSource(){if(this.range)return this.header()?.lineNumberInSource(this.range.startLine)}columnNumberInSource(){if(this.range)return this.header()?.columnNumberInSource(this.range.startLine,this.range.startColumn)}header(){return this.styleSheetId?this.cssModel.styleSheetHeaderForId(this.styleSheetId):null}rawLocation(){const e=this.header();if(!e||void 0===this.lineNumberInSource())return null;const t=Number(this.lineNumberInSource());return new Xt(e,t,this.columnNumberInSource())}}var _=Object.freeze({__proto__:null,CSSQuery:q});class z extends q{name;physicalAxes;logicalAxes;static parseContainerQueriesPayload(e,t){return t.map((t=>new z(e,t)))}constructor(e,t){super(e),this.reinitialize(t)}reinitialize(e){this.text=e.text,this.range=e.range?s.TextRange.TextRange.fromObject(e.range):null,this.styleSheetId=e.styleSheetId,this.name=e.name,this.physicalAxes=e.physicalAxes,this.logicalAxes=e.logicalAxes}active(){return!0}async getContainerForNode(e){const t=await this.cssModel.domModel().getContainerForNode(e,this.name,this.physicalAxes,this.logicalAxes);if(t)return new j(t)}}class j{containerNode;constructor(e){this.containerNode=e}async getContainerSizeDetails(){const e=await this.containerNode.domModel().cssModel().getComputedStyle(this.containerNode.id);if(!e)return;const t=e.get("container-type"),r=e.get("contain"),n=e.get("writing-mode");if(!t||!r||!n)return;const s=V(`${t} ${r}`),i=W(s,n);let a,o;return"Both"!==i&&"Horizontal"!==i||(a=e.get("width")),"Both"!==i&&"Vertical"!==i||(o=e.get("height")),{queryAxis:s,physicalAxis:i,width:a,height:o}}}const V=e=>{const t=e.split(" ");let r=!1,n=!1;for(const e of t){if("size"===e)return"size";r=r||"inline-size"===e,n=n||"block-size"===e}return r&&n?"size":r?"inline-size":n?"block-size":""},W=(e,t)=>{const r=t.startsWith("vertical");switch(e){case"":return"";case"size":return"Both";case"inline-size":return r?"Vertical":"Horizontal";case"block-size":return r?"Horizontal":"Vertical"}};var G=Object.freeze({__proto__:null,CSSContainerQuery:z,CSSContainerQueryContainer:j,getQueryAxis:V,getPhysicalAxisFromQueryAxis:W});class K extends q{static parseLayerPayload(e,t){return t.map((t=>new K(e,t)))}constructor(e,t){super(e),this.reinitialize(t)}reinitialize(e){this.text=e.text,this.range=e.range?s.TextRange.TextRange.fromObject(e.range):null,this.styleSheetId=e.styleSheetId}active(){return!0}}var Q=Object.freeze({__proto__:null,CSSLayer:K});class ${#S;#C;constructor(e){this.#S=e.active,this.#C=[];for(let t=0;t<e.expressions.length;++t)this.#C.push(X.parsePayload(e.expressions[t]))}static parsePayload(e){return new $(e)}active(){return this.#S}expressions(){return this.#C}}class X{#u;#R;#x;#T;#M;constructor(e){this.#u=e.value,this.#R=e.unit,this.#x=e.feature,this.#T=e.valueRange?s.TextRange.TextRange.fromObject(e.valueRange):null,this.#M=e.computedLength||null}static parsePayload(e){return new X(e)}value(){return this.#u}unit(){return this.#R}feature(){return this.#x}valueRange(){return this.#T}computedLength(){return this.#M}}class J extends q{source;sourceURL;mediaList;static parseMediaArrayPayload(e,t){return t.map((t=>new J(e,t)))}constructor(e,t){super(e),this.reinitialize(t)}reinitialize(e){if(this.text=e.text,this.source=e.source,this.sourceURL=e.sourceURL||"",this.range=e.range?s.TextRange.TextRange.fromObject(e.range):null,this.styleSheetId=e.styleSheetId,this.mediaList=null,e.mediaList){this.mediaList=[];for(let t=0;t<e.mediaList.length;++t)this.mediaList.push($.parsePayload(e.mediaList[t]))}}active(){if(!this.mediaList)return!0;for(let e=0;e<this.mediaList.length;++e)if(this.mediaList[e].active())return!0;return!1}}var Y=Object.freeze({__proto__:null,CSSMediaQuery:$,CSSMediaQueryExpression:X,CSSMedia:J,Source:{LINKED_SHEET:"linkedSheet",INLINE_SHEET:"inlineSheet",MEDIA_RULE:"mediaRule",IMPORT_RULE:"importRule"}});class Z extends q{static parseScopesPayload(e,t){return t.map((t=>new Z(e,t)))}constructor(e,t){super(e),this.reinitialize(t)}reinitialize(e){this.text=e.text,this.range=e.range?s.TextRange.TextRange.fromObject(e.range):null,this.styleSheetId=e.styleSheetId}active(){return!0}}var ee=Object.freeze({__proto__:null,CSSScope:Z});const te=new Set(["inherit","initial","unset"]),re=/[\x20-\x7E]{4}/,ne=new RegExp(`(?:'(${re.source})')|(?:"(${re.source})")\\s+(${/[+-]?(?:\d*\.)?\d+(?:[eE]\d+)?/.source})`);const se=/^"(.+)"|'(.+)'$/;function ie(e){return e.split(",").map((e=>e.trim()))}function ae(e){return e.replaceAll(/(\/\*(?:.|\s)*?\*\/)/g,"")}var oe=Object.freeze({__proto__:null,parseFontVariationSettings:function(e){if(te.has(e.trim())||"normal"===e.trim())return[];const t=[];for(const r of ie(ae(e))){const e=r.match(ne);e&&t.push({tag:e[1]||e[2],value:parseFloat(e[3])})}return t},parseFontFamily:function(e){if(te.has(e.trim()))return[];const t=[];for(const r of ie(ae(e))){const e=r.match(se);e?t.push(e[1]||e[2]):t.push(r)}return t},splitByComma:ie,stripComments:ae});class le{ownerStyle;index;name;value;important;disabled;parsedOk;implicit;text;range;#P;#L;#T;#A;#E=[];constructor(e,t,r,n,i,a,o,l,d,c,h){if(this.ownerStyle=e,this.index=t,this.name=r,this.value=n,this.important=i,this.disabled=a,this.parsedOk=o,this.implicit=l,this.text=d,this.range=c?s.TextRange.TextRange.fromObject(c):null,this.#P=!0,this.#L=null,this.#T=null,h&&h.length>0)for(const r of h)this.#E.push(new le(e,++t,r.name,r.value,i,a,o,!0));else{const n=I().getLonghands(r);for(const r of n||[])this.#E.push(new le(e,++t,r,"",i,a,o,!0))}}static parsePayload(e,t,r){return new le(e,t,r.name,r.value,r.important||!1,r.disabled||!1,!("parsedOk"in r)||Boolean(r.parsedOk),Boolean(r.implicit),r.text,r.range,r.longhandProperties)}ensureRanges(){if(this.#L&&this.#T)return;const e=this.range,t=this.text?new s.Text.Text(this.text):null;if(!e||!t)return;const r=t.value().indexOf(this.name),n=t.value().lastIndexOf(this.value);if(-1===r||-1===n||r>n)return;const i=new s.TextRange.SourceRange(r,this.name.length),a=new s.TextRange.SourceRange(n,this.value.length);function o(e,t,r){return 0===e.startLine&&(e.startColumn+=r,e.endColumn+=r),e.startLine+=t,e.endLine+=t,e}this.#L=o(t.toTextRange(i),e.startLine,e.startColumn),this.#T=o(t.toTextRange(a),e.startLine,e.startColumn)}nameRange(){return this.ensureRanges(),this.#L}valueRange(){return this.ensureRanges(),this.#T}rebase(e){this.ownerStyle.styleSheetId===e.styleSheetId&&this.range&&(this.range=this.range.rebaseAfterTextEdit(e.oldRange,e.newRange))}setActive(e){this.#P=e}get propertyText(){return void 0!==this.text?this.text:""===this.name?"":this.name+": "+this.value+(this.important?" !important":"")+";"}activeInStyle(){return this.#P}trimmedValueWithoutImportant(){const e="!important";return this.value.endsWith(e)?this.value.slice(0,-10).trim():this.value.trim()}async setText(r,n,i){if(!this.ownerStyle)throw new Error("No ownerStyle for property");if(!this.ownerStyle.styleSheetId)throw new Error("No owner style id");if(!this.range||!this.ownerStyle.range)throw new Error("Style not editable");if(n&&(o.userMetrics.actionTaken(o.UserMetrics.Action.StyleRuleEdited),this.ownerStyle.parentRule?.isKeyframeRule()&&o.userMetrics.actionTaken(o.UserMetrics.Action.StylePropertyInsideKeyframeEdited),this.name.startsWith("--")&&o.userMetrics.actionTaken(o.UserMetrics.Action.CustomPropertyEdited)),i&&r===this.propertyText)return this.ownerStyle.cssModel().domModel().markUndoableState(!n),!0;const a=this.range.relativeTo(this.ownerStyle.range.startLine,this.ownerStyle.range.startColumn),l=this.ownerStyle.cssText?this.detectIndentation(this.ownerStyle.cssText):e.Settings.Settings.instance().moduleSetting("text-editor-indent").get(),d=this.ownerStyle.cssText?l.substring(0,this.ownerStyle.range.endColumn):"",c=new s.Text.Text(this.ownerStyle.cssText||"").replaceRange(a,t.StringUtilities.sprintf(";%s;",r)),h=await le.formatStyle(c,l,d);return this.ownerStyle.setText(h,n)}static async formatStyle(e,t,r){const n=t.substring(r.length)+t;t&&(t="\n"+t);let i="",a="",o="",l=!1,d=!1;const c=s.CodeMirrorUtils.createCssTokenizer();return await c("*{"+e+"}",(function(e,r){if(!l){const n=r?.includes("comment")&&function(e){const t=e.indexOf(":");if(-1===t)return!1;const r=e.substring(2,t).trim();return I().isCSSPropertyName(r)}(e),s=r?.includes("def")||r?.includes("string")||r?.includes("meta")||r?.includes("property")||r?.includes("variableName")&&"variableName.function"!==r;return n?i=i.trimEnd()+t+e:s?(l=!0,o=e):(";"!==e||d)&&(i+=e,e.trim()&&!r?.includes("comment")&&(d=";"!==e)),void("{"!==e||r||(d=!1))}if("}"===e||";"===e){const r=o.trim();return i=i.trimEnd()+t+r+(r.endsWith(":")?" ":"")+e,d=!1,l=!1,void(a="")}if(I().isGridAreaDefiningProperty(a)){const t=y.exec(e);t&&0===t.index&&!o.trimEnd().endsWith("]")&&(o=o.trimEnd()+"\n"+n)}a||":"!==e||(a=o);o+=e})),l&&(i+=o),i=i.substring(2,i.length-1).trimEnd(),i+(t?"\n"+r:"")}detectIndentation(e){const t=e.split("\n");return t.length<2?"":s.TextUtils.Utils.lineIndent(t[1])}setValue(e,t,r,n){const s=this.name+": "+e+(this.important?" !important":"")+";";this.setText(s,t,r).then(n)}async setDisabled(e){if(!this.ownerStyle)return!1;if(e===this.disabled)return!0;if(!this.text)return!0;const t=this.text.trim(),r=e=>e+(e.endsWith(";")?"":";");let n;return n=e?"/* "+r(ae(t))+" */":r(this.text.substring(2,t.length-2).trim()),this.setText(n,!0,!0)}setDisplayedStringForInvalidProperty(e){this.#A=e}getInvalidStringForInvalidProperty(){return this.#A}getLonghandProperties(){return this.#E}}var de,ce=Object.freeze({__proto__:null,CSSProperty:le});class he{#O;parentRule;#N;styleSheetId;range;cssText;#D;#F;#B;#U;type;constructor(e,t,r,n){this.#O=e,this.parentRule=t,this.#H(r),this.type=n}rebase(e){if(this.styleSheetId===e.styleSheetId&&this.range)if(e.oldRange.equal(this.range))this.#H(e.payload);else{this.range=this.range.rebaseAfterTextEdit(e.oldRange,e.newRange);for(let t=0;t<this.#N.length;++t)this.#N[t].rebase(e)}}#H(e){this.styleSheetId=e.styleSheetId,this.range=e.range?s.TextRange.TextRange.fromObject(e.range):null;const t=e.shorthandEntries;this.#D=new Map,this.#F=new Set;for(let e=0;e<t.length;++e)this.#D.set(t[e].name,t[e].value),t[e].important&&this.#F.add(t[e].name);if(this.#N=[],e.cssText&&this.range){const t=[];for(const r of e.cssProperties){if(!r.range)continue;const e=le.parsePayload(this,this.#N.length,r);this.#N.push(e);for(const r of e.getLonghandProperties())t.push(r)}for(const e of t)e.index=this.#N.length,this.#N.push(e)}else for(const t of e.cssProperties)this.#N.push(le.parsePayload(this,this.#N.length,t));this.#q(),this.#_(),this.#B=new Map;for(const e of this.#N)e.activeInStyle()&&this.#B.set(e.name,e);this.cssText=e.cssText,this.#U=null}#q(){if(this.range)return;if(!this.#D.size)return;const e=new Set;for(const t of this.#N)e.add(t.name);const t=[];for(const r of this.#N){const n=I().getShorthands(r.name)||[];for(const r of n){if(e.has(r))continue;const n=this.#D.get(r);if(!n)continue;const s=Boolean(this.#F.has(r)),i=new le(this,this.allProperties().length,r,n,s,!1,!0,!1);t.push(i),e.add(r)}}this.#N=this.#N.concat(t)}#z(){if(this.range)return this.#N.filter((function(e){return Boolean(e.range)}));const e=[];for(const t of this.#N){const r=I().getShorthands(t.name)||[];let n=!1;for(const e of r)if(this.#D.get(e)){n=!0;break}n||e.push(t)}return e}leadingProperties(){return this.#U||(this.#U=this.#z()),this.#U}target(){return this.#O.target()}cssModel(){return this.#O}#_(){const e=new Map,t=new Set;for(const r of this.#N){const n=I().canonicalPropertyName(r.name);if(r.disabled||!r.parsedOk){r.name.startsWith("--")&&(e.get(n)?.setActive(!1),e.delete(n)),r.setActive(!1);continue}if(t.has(r))continue;for(const n of r.getLonghandProperties()){const r=e.get(n.name);r?!r.important||n.important?(r.setActive(!1),e.set(n.name,n)):n.setActive(!1):e.set(n.name,n),t.add(n)}const s=e.get(n);s?!s.important||r.important?(s.setActive(!1),e.set(n,r)):r.setActive(!1):e.set(n,r)}}allProperties(){return this.#N}hasActiveProperty(e){return this.#B.has(e)}getPropertyValue(e){const t=this.#B.get(e);return t?t.value:""}isPropertyImplicit(e){const t=this.#B.get(e);return!!t&&t.implicit}propertyAt(e){return e<this.allProperties().length?this.allProperties()[e]:null}pastLastSourcePropertyIndex(){for(let e=this.allProperties().length-1;e>=0;--e)if(this.allProperties()[e].range)return e+1;return 0}#j(e){const t=this.propertyAt(e);if(t&&t.range)return t.range.collapseToStart();if(!this.range)throw new Error("CSSStyleDeclaration.range is null");return this.range.collapseToEnd()}newBlankProperty(e){e=void 0===e?this.pastLastSourcePropertyIndex():e;return new le(this,e,"","",!1,!1,!0,!1,"",this.#j(e))}setText(e,t){return this.range&&this.styleSheetId?this.#O.setStyleText(this.styleSheetId,this.range,e,t):Promise.resolve(!1)}insertPropertyAt(e,t,r,n){this.newBlankProperty(e).setText(t+": "+r+";",!1,!0).then(n)}appendProperty(e,t,r){this.insertPropertyAt(this.allProperties().length,e,t,r)}}!function(e){e.Regular="Regular",e.Inline="Inline",e.Attributes="Attributes",e.Pseudo="Pseudo"}(de||(de={}));var ue=Object.freeze({__proto__:null,CSSStyleDeclaration:he,get Type(){return de}});class ge extends q{static parseSupportsPayload(e,t){return t.map((t=>new ge(e,t)))}#P=!0;constructor(e,t){super(e),this.reinitialize(t)}reinitialize(e){this.text=e.text,this.range=e.range?s.TextRange.TextRange.fromObject(e.range):null,this.styleSheetId=e.styleSheetId,this.#P=e.active}active(){return this.#P}}var pe=Object.freeze({__proto__:null,CSSSupports:ge});class me{cssModelInternal;styleSheetId;sourceURL;origin;style;constructor(e,t){if(this.cssModelInternal=e,this.styleSheetId=t.styleSheetId,this.styleSheetId){const e=this.getStyleSheetHeader(this.styleSheetId);this.sourceURL=e.sourceURL}this.origin=t.origin,this.style=new he(this.cssModelInternal,this,t.style,de.Regular)}rebase(e){this.styleSheetId===e.styleSheetId&&this.style.rebase(e)}resourceURL(){if(!this.styleSheetId)return t.DevToolsPath.EmptyUrlString;return this.getStyleSheetHeader(this.styleSheetId).resourceURL()}isUserAgent(){return"user-agent"===this.origin}isInjected(){return"injected"===this.origin}isViaInspector(){return"inspector"===this.origin}isRegular(){return"regular"===this.origin}isKeyframeRule(){return!1}cssModel(){return this.cssModelInternal}getStyleSheetHeader(e){const t=this.cssModelInternal.styleSheetHeaderForId(e);return console.assert(null!==t),t}}class fe{text;range;specificity;constructor(e){this.text=e.text,e.range&&(this.range=s.TextRange.TextRange.fromObject(e.range)),e.specificity&&(this.specificity=e.specificity)}rebase(e){this.range&&(this.range=this.range.rebaseAfterTextEdit(e.oldRange,e.newRange))}}class be extends me{selectors;nestingSelectors;media;containerQueries;supports;scopes;layers;ruleTypes;wasUsed;constructor(e,t,r){super(e,{origin:t.origin,style:t.style,styleSheetId:t.styleSheetId}),this.reinitializeSelectors(t.selectorList),this.nestingSelectors=t.nestingSelectors,this.media=t.media?J.parseMediaArrayPayload(e,t.media):[],this.containerQueries=t.containerQueries?z.parseContainerQueriesPayload(e,t.containerQueries):[],this.scopes=t.scopes?Z.parseScopesPayload(e,t.scopes):[],this.supports=t.supports?ge.parseSupportsPayload(e,t.supports):[],this.layers=t.layers?K.parseLayerPayload(e,t.layers):[],this.ruleTypes=t.ruleTypes||[],this.wasUsed=r||!1}static createDummyRule(e,t){const r={selectorList:{text:"",selectors:[{text:t,value:void 0}]},style:{styleSheetId:"0",range:new s.TextRange.TextRange(0,0,0,0),shorthandEntries:[],cssProperties:[]},origin:"inspector"};return new be(e,r)}reinitializeSelectors(e){this.selectors=[];for(let t=0;t<e.selectors.length;++t)this.selectors.push(new fe(e.selectors[t]))}setSelectorText(e){const t=this.styleSheetId;if(!t)throw"No rule stylesheet id";const r=this.selectorRange();if(!r)throw"Rule selector is not editable";return this.cssModelInternal.setSelectorText(t,r,e)}selectorText(){return this.selectors.map((e=>e.text)).join(", ")}selectorRange(){if(0===this.selectors.length)return null;const e=this.selectors[0].range,t=this.selectors[this.selectors.length-1].range;return e&&t?new s.TextRange.TextRange(e.startLine,e.startColumn,t.endLine,t.endColumn):null}lineNumberInSource(e){const t=this.selectors[e];if(!t||!t.range||!this.styleSheetId)return 0;return this.getStyleSheetHeader(this.styleSheetId).lineNumberInSource(t.range.startLine)}columnNumberInSource(e){const t=this.selectors[e];if(!t||!t.range||!this.styleSheetId)return;return this.getStyleSheetHeader(this.styleSheetId).columnNumberInSource(t.range.startLine,t.range.startColumn)}rebase(e){if(this.styleSheetId!==e.styleSheetId)return;const t=this.selectorRange();if(t&&t.equal(e.oldRange))this.reinitializeSelectors(e.payload);else for(let t=0;t<this.selectors.length;++t)this.selectors[t].rebase(e);this.media.forEach((t=>t.rebase(e))),this.containerQueries.forEach((t=>t.rebase(e))),this.scopes.forEach((t=>t.rebase(e))),this.supports.forEach((t=>t.rebase(e))),super.rebase(e)}}class ye extends me{#V;constructor(e,t){super(e,{origin:t.origin,style:t.style,styleSheetId:t.styleSheetId}),this.#V=new fe(t.propertyName)}propertyName(){return this.#V}initialValue(){return this.style.hasActiveProperty("initial-value")?this.style.getPropertyValue("initial-value"):null}syntax(){return this.style.getPropertyValue("syntax")}inherits(){return"true"===this.style.getPropertyValue("inherits")}setPropertyName(e){const t=this.styleSheetId;if(!t)throw new Error("No rule stylesheet id");const r=this.#V.range;if(!r)throw new Error("Property name is not editable");return this.cssModelInternal.setPropertyRulePropertyName(t,r,e)}}class ve extends me{#W;constructor(e,t){super(e,{origin:t.origin,style:t.style,styleSheetId:t.styleSheetId}),this.#W=new fe(t.fontPaletteName)}name(){return this.#W}}class Ie{#G;#K;constructor(e,t){this.#G=new fe(t.animationName),this.#K=t.keyframes.map((t=>new ke(e,t)))}name(){return this.#G}keyframes(){return this.#K}}class ke extends me{#Q;constructor(e,t){super(e,{origin:t.origin,style:t.style,styleSheetId:t.styleSheetId}),this.reinitializeKey(t.keyText)}key(){return this.#Q}reinitializeKey(e){this.#Q=new fe(e)}rebase(e){this.styleSheetId===e.styleSheetId&&this.#Q.range&&(e.oldRange.equal(this.#Q.range)?this.reinitializeKey(e.payload):this.#Q.rebase(e),super.rebase(e))}isKeyframeRule(){return!0}setKeyText(e){const t=this.styleSheetId;if(!t)throw"No rule stylesheet id";const r=this.#Q.range;if(!r)throw"Keyframe key is not editable";return this.cssModelInternal.setKeyframeKey(t,r,e)}}class we{#V;#$;constructor(e,t){this.#V=new fe(t.name),this.#$=t.tryRules.map((t=>new me(e,{origin:t.origin,style:t.style,styleSheetId:t.styleSheetId})))}name(){return this.#V}tryRules(){return this.#$}}var Se=Object.freeze({__proto__:null,CSSRule:me,CSSStyleRule:be,CSSPropertyRule:ye,CSSFontPaletteValuesRule:ve,CSSKeyframesRule:Ie,CSSKeyframeRule:ke,CSSPositionFallbackRule:we});function Ce(e){const t=e.match(/var\(\s*(--(?:[\s\w\P{ASCII}-]|\\.)+),?\s*(.*)\s*\)/u);return{variableName:t&&t[1].trim(),fallback:t&&t[2]}}function Re(e,t){if(!t.styleSheetId||!t.range)return!1;for(const r of e)if(t.styleSheetId===r.styleSheetId&&r.range&&t.range.equal(r.range))return!0;return!1}function xe(e){const t=e.allProperties();for(let e=0;e<t.length;++e){const r=t[e];if(r.activeInStyle()&&I().isPropertyInherited(r.name))return!0}return!1}function Te(e){for(const t of e)s(t);const t=[];for(const s of e){const e=t[t.length-1];e&&"user-agent"===s.rule.origin&&"user-agent"===e.rule.origin&&s.rule.selectorList.text===e.rule.selectorList.text&&n(s)===n(e)?r(s,e):t.push(s)}return t;function r(e,t){const r=new Map,n=new Map;for(const e of t.rule.style.shorthandEntries)r.set(e.name,e.value);for(const e of t.rule.style.cssProperties)n.set(e.name,e.value);for(const t of e.rule.style.shorthandEntries)r.set(t.name,t.value);for(const t of e.rule.style.cssProperties)n.set(t.name,t.value);t.rule.style.shorthandEntries=[...r.entries()].map((([e,t])=>({name:e,value:t}))),t.rule.style.cssProperties=[...n.entries()].map((([e,t])=>({name:e,value:t})))}function n(e){return e.rule.media?e.rule.media.map((e=>e.text)).join(", "):null}function s(e){const{matchingSelectors:t,rule:r}=e;"user-agent"===r.origin&&t.length&&(r.selectorList.selectors=r.selectorList.selectors.filter(((e,r)=>t.includes(r))),r.selectorList.text=r.selectorList.selectors.map((e=>e.text)).join(", "),e.matchingSelectors=t.map(((e,t)=>t)))}}function Me(e){const t=new Map;for(let r=0;r<e.matchingSelectors.length;r++){const n=e.matchingSelectors[r],s=e.rule.selectorList.selectors[n].text.match(/::highlight\((.*)\)/);if(s){const e=s[1],r=t.get(e);r?r.push(n):t.set(e,[n])}}return t}class Pe{#X;#J;#Y;constructor(e,t){this.#J=e,this.#X=t}isAtProperty(){return this.#X instanceof ye}propertyName(){return this.#X instanceof ye?this.#X.propertyName().text:this.#X.propertyName}initialValue(){return this.#X instanceof ye?this.#X.initialValue():this.#X.initialValue?.text??null}inherits(){return this.#X instanceof ye?this.#X.inherits():this.#X.inherits}syntax(){return this.#X instanceof ye?this.#X.syntax():`"${this.#X.syntax}"`}#Z(){if(this.#X instanceof ye)return[];const{inherits:e,initialValue:t,syntax:r}=this.#X,n=[{name:"inherits",value:`${e}`},{name:"syntax",value:`"${r}"`}];return void 0!==t&&n.push({name:"initial-value",value:t.text}),n}style(){return this.#Y||(this.#Y=this.#X instanceof ye?this.#X.style:new he(this.#J,null,{cssProperties:this.#Z(),shorthandEntries:[]},de.Pseudo)),this.#Y}}class Le{#O;#ee;#te;#re;#K;#ne;#se=new Map;#ie;#ae;#oe;#le;#de;#ce;#he;#ue;#ge;static async create(e){const t=new Le(e);return await t.init(e),t}constructor({cssModel:e,node:t,animationsPayload:r,parentLayoutNodeId:n,positionFallbackRules:s,propertyRules:i,cssPropertyRegistrations:a,fontPaletteValuesRule:o}){this.#O=e,this.#ee=t,this.#te=new Map,this.#re=new Map,this.#ne=[...i.map((t=>new ye(e,t))),...a].map((t=>new Pe(e,t))),this.#K=[],r&&(this.#K=r.map((t=>new Ie(e,t)))),this.#de=s.map((t=>new we(e,t))),this.#le=n,this.#ge=o?new ve(e,o):void 0,this.#ie=new Map,this.#ae=new Set,this.#oe=new Map,this.#se=new Map}async init({matchedPayload:e,inheritedPayload:t,inlinePayload:r,attributesPayload:n,pseudoPayload:s,inheritedPseudoPayload:i}){e=Te(e);for(const e of t)e.matchedCSSRules=Te(e.matchedCSSRules);this.#ce=await this.buildMainCascade(r,n,e,t),[this.#he,this.#ue]=this.buildPseudoCascades(s,i);for(const e of Array.from(this.#ue.values()).concat(Array.from(this.#he.values())).concat(this.#ce))for(const t of e.styles())this.#oe.set(t,e);for(const e of this.#ne)this.#se.set(e.propertyName(),e)}async buildMainCascade(e,t,r,n){const s=[],i=[];function a(){if(!t)return;const e=new he(this.#O,null,t,de.Attributes);this.#ie.set(e,this.#ee),i.push(e)}if(e&&this.#ee.nodeType()===Node.ELEMENT_NODE){const t=new he(this.#O,null,e,de.Inline);this.#ie.set(t,this.#ee),i.push(t)}let o;for(let e=r.length-1;e>=0;--e){const t=new be(this.#O,r[e].rule);!t.isInjected()&&!t.isUserAgent()||o||(o=!0,a.call(this)),this.#ie.set(t.style,this.#ee),i.push(t.style),this.addMatchingSelectors(this.#ee,t,r[e].matchingSelectors)}o||a.call(this),s.push(new Ae(this,i,!1));let l=this.#ee.parentNode;const d=async e=>e.hasAssignedSlot()?await(e.assignedSlot?.deferredNode.resolvePromise())??null:e.parentNode;for(let e=0;l&&n&&e<n.length;++e){const t=[],r=n[e],a=r.inlineStyle?new he(this.#O,null,r.inlineStyle,de.Inline):null;a&&xe(a)&&(this.#ie.set(a,l),t.push(a),this.#ae.add(a));const o=r.matchedCSSRules||[];for(let e=o.length-1;e>=0;--e){const r=new be(this.#O,o[e].rule);this.addMatchingSelectors(l,r,o[e].matchingSelectors),xe(r.style)&&(Re(i,r.style)||Re(this.#ae,r.style)||(this.#ie.set(r.style,l),t.push(r.style),this.#ae.add(r.style)))}l=await d(l),s.push(new Ae(this,t,!0))}return new Ee(s,this.#ne)}buildSplitCustomHighlightCascades(e,t,r,n){const s=new Map;for(let n=e.length-1;n>=0;--n){const i=Me(e[n]);for(const[a,o]of i){const i=new be(this.#O,e[n].rule);this.#ie.set(i.style,t),r&&this.#ae.add(i.style),this.addMatchingSelectors(t,i,o);const l=s.get(a);l?l.push(i.style):s.set(a,[i.style])}}for(const[e,t]of s){const s=new Ae(this,t,r,!0),i=n.get(e);i?i.push(s):n.set(e,[s])}}buildPseudoCascades(e,t){const r=new Map,n=new Map;if(!e)return[r,n];const s=new Map,i=new Map;for(let t=0;t<e.length;++t){const r=e[t],n=this.#ee.pseudoElements().get(r.pseudoType)?.at(-1)||null,a=[],o=r.matches||[];if("highlight"===r.pseudoType)this.buildSplitCustomHighlightCascades(o,this.#ee,!1,i);else{for(let e=o.length-1;e>=0;--e){const t=new be(this.#O,o[e].rule);a.push(t.style);const s=I().isHighlightPseudoType(r.pseudoType)?this.#ee:n;this.#ie.set(t.style,s),s&&this.addMatchingSelectors(s,t,o[e].matchingSelectors)}const e=I().isHighlightPseudoType(r.pseudoType),t=new Ae(this,a,!1,e);s.set(r.pseudoType,[t])}}if(t){let e=this.#ee.parentNode;for(let r=0;e&&r<t.length;++r){const n=t[r].pseudoElements;for(let t=0;t<n.length;++t){const r=n[t],a=r.matches||[];if("highlight"===r.pseudoType)this.buildSplitCustomHighlightCascades(a,e,!0,i);else{const t=[];for(let r=a.length-1;r>=0;--r){const n=new be(this.#O,a[r].rule);t.push(n.style),this.#ie.set(n.style,e),this.#ae.add(n.style),this.addMatchingSelectors(e,n,a[r].matchingSelectors)}const n=I().isHighlightPseudoType(r.pseudoType),i=new Ae(this,t,!0,n),o=s.get(r.pseudoType);o?o.push(i):s.set(r.pseudoType,[i])}}e=e.parentNode}}for(const[e,t]of s.entries())r.set(e,new Ee(t,this.#ne));for(const[e,t]of i.entries())n.set(e,new Ee(t,this.#ne));return[r,n]}addMatchingSelectors(e,t,r){for(const n of r){const r=t.selectors[n];r&&this.setSelectorMatches(e,r.text,!0)}}node(){return this.#ee}cssModel(){return this.#O}hasMatchingSelectors(e){return this.getMatchingSelectors(e).length>0&&function(e){if(!e.parentRule)return!0;const t=e.parentRule,r=[...t.media,...t.containerQueries,...t.supports,...t.scopes];for(const e of r)if(!e.active())return!1;return!0}(e.style)}getParentLayoutNodeId(){return this.#le}getMatchingSelectors(e){const t=this.nodeForStyle(e.style);if(!t||"number"!=typeof t.id)return[];const r=this.#re.get(t.id);if(!r)return[];const n=[];for(let t=0;t<e.selectors.length;++t)r.get(e.selectors[t].text)&&n.push(t);return n}async recomputeMatchingSelectors(e){const t=this.nodeForStyle(e.style);if(!t)return;const r=[];for(const s of e.selectors)r.push(n.call(this,t,s.text));async function n(e,t){const r=e.ownerDocument;if(!r)return;if("number"==typeof e.id){const r=this.#re.get(e.id);if(r&&r.has(t))return}if("number"!=typeof r.id)return;const n=await this.#ee.domModel().querySelectorAll(r.id,t);n&&("number"==typeof e.id?this.setSelectorMatches(e,t,-1!==n.indexOf(e.id)):this.setSelectorMatches(e,t,!1))}await Promise.all(r)}addNewRule(e,t){return this.#te.set(e.style,t),this.recomputeMatchingSelectors(e)}setSelectorMatches(e,t,r){if("number"!=typeof e.id)return;let n=this.#re.get(e.id);n||(n=new Map,this.#re.set(e.id,n)),n.set(t,r)}nodeStyles(){return t.assertNotNullOrUndefined(this.#ce),this.#ce.styles()}registeredProperties(){return this.#ne}getRegisteredProperty(e){return this.#se.get(e)}fontPaletteValuesRule(){return this.#ge}keyframes(){return this.#K}positionFallbackRules(){return this.#de}pseudoStyles(e){t.assertNotNullOrUndefined(this.#he);const r=this.#he.get(e);return r?r.styles():[]}pseudoTypes(){return t.assertNotNullOrUndefined(this.#he),new Set(this.#he.keys())}customHighlightPseudoStyles(e){t.assertNotNullOrUndefined(this.#ue);const r=this.#ue.get(e);return r?r.styles():[]}customHighlightPseudoNames(){return t.assertNotNullOrUndefined(this.#ue),new Set(this.#ue.keys())}nodeForStyle(e){return this.#te.get(e)||this.#ie.get(e)||null}availableCSSVariables(e){const t=this.#oe.get(e)||null;return t?t.findAvailableCSSVariables(e):[]}computeCSSVariable(e,t){const r=this.#oe.get(e)||null;return r?r.computeCSSVariable(e,t):null}computeValue(e,t){const r=this.#oe.get(e)||null;return r?r.computeValue(e,t):null}computeSingleVariableValue(e,t){const r=this.#oe.get(e)||null;return r?r.computeSingleVariableValue(e,t):null}isInherited(e){return this.#ae.has(e)}propertyState(e){const t=this.#oe.get(e.ownerStyle);return t?t.propertyState(e):null}resetActiveProperties(){t.assertNotNullOrUndefined(this.#ce),t.assertNotNullOrUndefined(this.#he),t.assertNotNullOrUndefined(this.#ue),this.#ce.reset();for(const e of this.#he.values())e.reset();for(const e of this.#ue.values())e.reset()}}class Ae{#pe;styles;#me;#fe;propertiesState;activeProperties;constructor(e,t,r,n=!1){this.#pe=e,this.styles=t,this.#me=r,this.#fe=n,this.propertiesState=new Map,this.activeProperties=new Map}computeActiveProperties(){this.propertiesState.clear(),this.activeProperties.clear();for(let e=this.styles.length-1;e>=0;e--){const t=this.styles[e],r=t.parentRule;if((!r||r instanceof be)&&(!r||this.#pe.hasMatchingSelectors(r)))for(const e of t.allProperties()){const r=I();if(this.#me&&!this.#fe&&!r.isPropertyInherited(e.name))continue;if(t.range&&!e.range)continue;if(!e.activeInStyle()){this.propertiesState.set(e,"Overloaded");continue}if(this.#me){const t=this.#pe.getRegisteredProperty(e.name);if(t&&!t.inherits()){this.propertiesState.set(e,"Overloaded");continue}}const n=r.canonicalPropertyName(e.name);this.updatePropertyState(e,n);for(const t of e.getLonghandProperties())r.isCSSPropertyName(t.name)&&this.updatePropertyState(t,t.name)}}}updatePropertyState(e,t){const r=this.activeProperties.get(t);!r?.important||e.important?(r&&this.propertiesState.set(r,"Overloaded"),this.propertiesState.set(e,"Active"),this.activeProperties.set(t,e)):this.propertiesState.set(e,"Overloaded")}}class Ee{#be;#ye;#ve;#Ie;#ke;#we;#ne;constructor(e,t){this.#be=e,this.#ye=new Map,this.#ve=new Map,this.#Ie=new Map,this.#ke=!1,this.#ne=t,this.#we=new Map;for(const t of e)for(const e of t.styles)this.#we.set(e,t)}findAvailableCSSVariables(e){const t=this.#we.get(e);if(!t)return[];this.ensureInitialized();const r=this.#ve.get(t);return r?Array.from(r.keys()):[]}computeCSSVariable(e,t){const r=this.#we.get(e);if(!r)return null;this.ensureInitialized();const n=this.#ve.get(r),s=this.#Ie.get(r);return n&&s?this.innerComputeCSSVariable(n,s,t):null}computeValue(e,t){const r=this.#we.get(e);if(!r)return null;this.ensureInitialized();const n=this.#ve.get(r),s=this.#Ie.get(r);return n&&s?this.innerComputeValue(n,s,t):null}computeSingleVariableValue(e,t){const r=this.#we.get(e);if(!r)return null;this.ensureInitialized();const n=this.#ve.get(r),s=this.#Ie.get(r);if(!n||!s)return null;const i=this.innerComputeValue(n,s,t),{variableName:a}=Ce(t);return{computedValue:i,fromFallback:null!==a&&!n.has(a)}}innerComputeCSSVariable(e,t,r){if(!e.has(r))return null;if(t.has(r))return t.get(r)||null;t.set(r,null);const n=e.get(r);if(null==n)return null;const s=this.innerComputeValue(e,t,n.value),i=s?{value:s,declaration:n.declaration}:null;return t.set(r,i),i}innerComputeValue(e,t,r){const n=s.TextUtils.Utils.splitStringByRegexes(r,[b]),i=[];for(const r of n){if(-1===r.regexIndex){i.push(r.value);continue}const{variableName:n,fallback:s}=Ce(r.value);if(!n)return null;const a=this.innerComputeCSSVariable(e,t,n);if(null===a&&!s)return null;null===a?i.push(s):i.push(a.value)}return i.map((e=>e?e.trim():"")).join(" ")}styles(){return Array.from(this.#we.keys())}propertyState(e){return this.ensureInitialized(),this.#ye.get(e)||null}reset(){this.#ke=!1,this.#ye.clear(),this.#ve.clear(),this.#Ie.clear()}ensureInitialized(){if(this.#ke)return;this.#ke=!0;const e=new Map;for(const t of this.#be){t.computeActiveProperties();for(const[r,n]of t.propertiesState){if("Overloaded"===n){this.#ye.set(r,"Overloaded");continue}const t=I().canonicalPropertyName(r.name);e.has(t)?this.#ye.set(r,"Overloaded"):(e.set(t,r),this.#ye.set(r,"Active"))}}for(const[t,r]of e){const n=r.ownerStyle,s=r.getLonghandProperties();if(!s.length)continue;let i=!1;for(const t of s){const r=I().canonicalPropertyName(t.name),s=e.get(r);if(s&&s.ownerStyle===n){i=!0;break}}i||(e.delete(t),this.#ye.set(r,"Overloaded"))}const t=new Map;for(const e of this.#ne){const r=e.initialValue();t.set(e.propertyName(),r?{value:r,declaration:e}:null)}for(let e=this.#be.length-1;e>=0;--e){const r=this.#be[e],n=[];for(const e of r.activeProperties.entries()){const r=e[0],s=e[1];r.startsWith("--")&&(t.set(r,{value:s.value,declaration:s}),n.push(r))}const s=new Map(t),i=new Map;this.#ve.set(r,s),this.#Ie.set(r,i);for(const e of n){const r=t.get(e);t.delete(e);const n=this.innerComputeCSSVariable(s,i,e);r&&n?.value===r.value&&(n.declaration=r.declaration),t.set(e,n)}}}}var Oe=Object.freeze({__proto__:null,parseCSSVariableNameAndFallback:Ce,CSSRegisteredProperty:Pe,CSSMatchedStyles:Le});const Ne={couldNotFindTheOriginalStyle:"Could not find the original style sheet.",thereWasAnErrorRetrievingThe:"There was an error retrieving the source styles."},De=i.i18n.registerUIStrings("core/sdk/CSSStyleSheetHeader.ts",Ne),Fe=i.i18n.getLocalizedString.bind(void 0,De);class Be{#O;id;frameId;sourceURL;hasSourceURL;origin;title;disabled;isInline;isMutable;isConstructed;startLine;startColumn;endLine;endColumn;contentLength;ownerNode;sourceMapURL;loadingFailed;#Se;constructor(e,t){this.#O=e,this.id=t.styleSheetId,this.frameId=t.frameId,this.sourceURL=t.sourceURL,this.hasSourceURL=Boolean(t.hasSourceURL),this.origin=t.origin,this.title=t.title,this.disabled=t.disabled,this.isInline=t.isInline,this.isMutable=t.isMutable,this.isConstructed=t.isConstructed,this.startLine=t.startLine,this.startColumn=t.startColumn,this.endLine=t.endLine,this.endColumn=t.endColumn,this.contentLength=t.length,t.ownerNode&&(this.ownerNode=new Yr(e.target(),t.ownerNode)),this.sourceMapURL=t.sourceMapURL,this.loadingFailed=t.loadingFailed??!1,this.#Se=null}originalContentProvider(){if(!this.#Se){const e=async()=>{const e=await this.#O.originalStyleSheetText(this);return null===e?{error:Fe(Ne.couldNotFindTheOriginalStyle)}:new s.ContentData.ContentData(e,!1,"text/css")};this.#Se=new s.StaticContentProvider.SafeStaticContentProvider(this.contentURL(),this.contentType(),e)}return this.#Se}setSourceMapURL(e){this.sourceMapURL=e}cssModel(){return this.#O}isAnonymousInlineStyleSheet(){return!this.resourceURL()&&!this.#O.sourceMapManager().sourceMapForClient(this)}isConstructedByNew(){return this.isConstructed&&0===this.sourceURL.length}resourceURL(){const e=this.isViaInspector()?this.viaInspectorResourceURL():this.sourceURL;return!e&&a.Runtime.experiments.isEnabled("styles-pane-css-changes")?this.dynamicStyleURL():e}getFrameURLPath(){const t=this.#O.target().model(mn);if(console.assert(Boolean(t)),!t)return"";const r=t.frameForId(this.frameId);if(!r)return"";console.assert(Boolean(r));const n=new e.ParsedURL.ParsedURL(r.url);let s=n.host+n.folderPathComponents;return s.endsWith("/")||(s+="/"),s}viaInspectorResourceURL(){return`inspector://${this.getFrameURLPath()}inspector-stylesheet`}dynamicStyleURL(){return`stylesheet://${this.getFrameURLPath()}style#${this.id}`}lineNumberInSource(e){return this.startLine+e}columnNumberInSource(e,t){return(e?0:this.startColumn)+t}containsLocation(e,t){const r=e===this.startLine&&t>=this.startColumn||e>this.startLine,n=e<this.endLine||e===this.endLine&&t<=this.endColumn;return r&&n}contentURL(){return this.resourceURL()}contentType(){return e.ResourceType.resourceTypes.Stylesheet}requestContent(){return this.requestContentData().then(s.ContentData.ContentData.asDeferredContent.bind(void 0))}async requestContentData(){const e=await this.#O.getStyleSheetText(this.id);return null===e?{error:Fe(Ne.thereWasAnErrorRetrievingThe)}:new s.ContentData.ContentData(e,!1,"text/css")}async searchInContent(e,t,r){const n=await this.requestContentData();return s.TextUtils.performSearchInContentData(n,e,t,r)}isViaInspector(){return"inspector"===this.origin}createPageResourceLoadInitiator(){return{target:this.#O.target(),frameId:this.frameId,initiatorUrl:this.hasSourceURL?t.DevToolsPath.EmptyUrlString:this.sourceURL}}}var Ue,He=Object.freeze({__proto__:null,CSSStyleSheetHeader:Be});class qe extends l.InspectorBackend.TargetBase{#Ce;#h;#Re;#xe;#Te;#g;#Me;#Pe;#Le;#Ae;#Ee;#Oe;constructor(r,n,s,i,a,o,l,d,c){switch(super(i===Ue.Node,a,o,d),this.#Ce=r,this.#h=s,this.#Re=t.DevToolsPath.EmptyUrlString,this.#xe="",this.#Te=0,i){case Ue.Frame:this.#Te=1027519,a?.type()!==Ue.Frame&&(this.#Te|=21056,e.ParsedURL.schemeIs(c?.url,"chrome-extension:")&&(this.#Te&=-513));break;case Ue.ServiceWorker:this.#Te=657468,a?.type()!==Ue.Frame&&(this.#Te|=1);break;case Ue.SharedWorker:this.#Te=919612;break;case Ue.SharedStorageWorklet:this.#Te=526348;break;case Ue.Worker:this.#Te=917820;break;case Ue.Worklet:this.#Te=524300;break;case Ue.Node:this.#Te=4;break;case Ue.AuctionWorklet:this.#Te=524292;break;case Ue.Browser:this.#Te=131104;break;case Ue.Tab:this.#Te=160}this.#g=i,this.#Me=a,this.#Pe=n,this.#Le=new Map,this.#Ae=l,this.#Ee=c}createModels(e){this.#Oe=!0;const t=Array.from(c.registeredModels.entries());for(const[e,r]of t)r.early&&this.model(e);for(const[r,n]of t)(n.autostart||e.has(r))&&this.model(r);this.#Oe=!1}id(){return this.#Pe}name(){return this.#h||this.#xe}setName(e){this.#h!==e&&(this.#h=e,this.#Ce.onNameChange(this))}type(){return this.#g}markAsNodeJSForTest(){super.markAsNodeJSForTest(),this.#g=Ue.Node}targetManager(){return this.#Ce}hasAllCapabilities(e){return(this.#Te&e)===e}decorateLabel(e){return this.#g===Ue.Worker||this.#g===Ue.ServiceWorker?"⚙ "+e:e}parentTarget(){return this.#Me}outermostTarget(){let e=null,t=this;do{t.type()!==Ue.Tab&&t.type()!==Ue.Browser&&(e=t),t=t.parentTarget()}while(t);return e}dispose(e){super.dispose(e),this.#Ce.removeTarget(this);for(const e of this.#Le.values())e.dispose()}model(e){if(!this.#Le.get(e)){const t=c.registeredModels.get(e);if(void 0===t)throw"Model class is not registered @"+(new Error).stack;if((this.#Te&t.capabilities)===t.capabilities){const t=new e(this);this.#Le.set(e,t),this.#Oe||this.#Ce.modelAdded(this,e,t,this.#Ce.isInScope(this))}}return this.#Le.get(e)||null}models(){return this.#Le}inspectedURL(){return this.#Re}setInspectedURL(t){this.#Re=t;const r=e.ParsedURL.ParsedURL.fromString(t);this.#xe=r?r.lastPathComponentWithFragment():"#"+this.#Pe,this.#Ce.onInspectedURLChange(this),this.#h||this.#Ce.onNameChange(this)}async suspend(e){this.#Ae||(this.#Ae=!0,await Promise.all(Array.from(this.models().values(),(t=>t.preSuspendModel(e)))),await Promise.all(Array.from(this.models().values(),(t=>t.suspendModel(e)))))}async resume(){this.#Ae&&(this.#Ae=!1,await Promise.all(Array.from(this.models().values(),(e=>e.resumeModel()))),await Promise.all(Array.from(this.models().values(),(e=>e.postResumeModel()))))}suspended(){return this.#Ae}updateTargetInfo(e){this.#Ee=e}targetInfo(){return this.#Ee}}!function(e){e.Frame="frame",e.ServiceWorker="service-worker",e.Worker="worker",e.SharedWorker="shared-worker",e.SharedStorageWorklet="shared-storage-worklet",e.Node="node",e.Browser="browser",e.AuctionWorklet="auction-worklet",e.Worklet="worklet",e.Tab="tab"}(Ue||(Ue={}));var _e=Object.freeze({__proto__:null,Target:qe,get Type(){return Ue}});let ze;class je extends e.ObjectWrapper.ObjectWrapper{#Ne;#De;#Fe;#Be;#Ue;#Ae;#He;#qe;#_e;#ze;constructor(){super(),this.#Ne=new Set,this.#De=new Set,this.#Fe=new t.MapUtilities.Multimap,this.#Be=new t.MapUtilities.Multimap,this.#Ae=!1,this.#He=null,this.#qe=null,this.#Ue=new WeakSet,this.#_e=!1,this.#ze=new Set}static instance({forceNew:e}={forceNew:!1}){return ze&&!e||(ze=new je),ze}static removeInstance(){ze=void 0}onInspectedURLChange(e){e===this.#qe&&(o.InspectorFrontendHost.InspectorFrontendHostInstance.inspectedURLChanged(e.inspectedURL()||t.DevToolsPath.EmptyUrlString),this.dispatchEventToListeners("InspectedURLChanged",e))}onNameChange(e){this.dispatchEventToListeners("NameChanged",e)}async suspendAllTargets(e){if(this.#Ae)return;this.#Ae=!0,this.dispatchEventToListeners("SuspendStateChanged");const t=Array.from(this.#Ne.values(),(t=>t.suspend(e)));await Promise.all(t)}async resumeAllTargets(){if(!this.#Ae)return;this.#Ae=!1,this.dispatchEventToListeners("SuspendStateChanged");const e=Array.from(this.#Ne.values(),(e=>e.resume()));await Promise.all(e)}allTargetsSuspended(){return this.#Ae}models(e,t){const r=[];for(const n of this.#Ne){if(t?.scoped&&!this.isInScope(n))continue;const s=n.model(e);s&&r.push(s)}return r}inspectedURL(){const e=this.primaryPageTarget();return e?e.inspectedURL():""}observeModels(e,t,r){const n=this.models(e,r);this.#Be.set(e,t),r?.scoped&&this.#Ue.add(t);for(const e of n)t.modelAdded(e)}unobserveModels(e,t){this.#Be.delete(e,t),this.#Ue.delete(t)}modelAdded(e,t,r,n){for(const e of this.#Be.get(t).values())this.#Ue.has(e)&&!n||e.modelAdded(r)}modelRemoved(e,t,r,n){for(const e of this.#Be.get(t).values())this.#Ue.has(e)&&!n||e.modelRemoved(r)}addModelListener(e,t,r,n,s){const i=e=>{s?.scoped&&!this.isInScope(e)||r.call(n,e)};for(const r of this.models(e))r.addEventListener(t,i);this.#Fe.set(t,{modelClass:e,thisObject:n,listener:r,wrappedListener:i})}removeModelListener(e,t,r,n){if(!this.#Fe.has(t))return;let s=null;for(const i of this.#Fe.get(t))i.modelClass===e&&i.listener===r&&i.thisObject===n&&(s=i.wrappedListener,this.#Fe.delete(t,i));if(s)for(const r of this.models(e))r.removeEventListener(t,s)}observeTargets(e,t){if(this.#De.has(e))throw new Error("Observer can only be registered once");t?.scoped&&this.#Ue.add(e);for(const r of this.#Ne)t?.scoped&&!this.isInScope(r)||e.targetAdded(r);this.#De.add(e)}unobserveTargets(e){this.#De.delete(e),this.#Ue.delete(e)}createTarget(e,t,r,n,s,i,a,o){const l=new qe(this,e,t,r,n,s||"",this.#Ae,a||null,o);i&&l.pageAgent().invoke_waitForDebugger(),l.createModels(new Set(this.#Be.keysArray())),this.#Ne.add(l);const d=this.isInScope(l);for(const e of[...this.#De])this.#Ue.has(e)&&!d||e.targetAdded(l);for(const[e,t]of l.models().entries())this.modelAdded(l,e,t,d);for(const e of this.#Fe.keysArray())for(const t of this.#Fe.get(e)){const r=l.model(t.modelClass);r&&r.addEventListener(e,t.wrappedListener)}return l!==l.outermostTarget()||l.type()===Ue.Frame&&l!==this.primaryPageTarget()||this.#_e||this.setScopeTarget(l),l}removeTarget(e){if(!this.#Ne.has(e))return;const t=this.isInScope(e);this.#Ne.delete(e);for(const n of e.models().keys()){const s=e.models().get(n);r(s),this.modelRemoved(e,n,s,t)}for(const r of[...this.#De])this.#Ue.has(r)&&!t||r.targetRemoved(e);for(const t of this.#Fe.keysArray())for(const r of this.#Fe.get(t)){const n=e.model(r.modelClass);n&&n.removeEventListener(t,r.wrappedListener)}}targets(){return[...this.#Ne]}targetById(e){return this.targets().find((t=>t.id()===e))||null}rootTarget(){return this.#Ne.size?this.#Ne.values().next().value:null}primaryPageTarget(){let e=this.rootTarget();return e?.type()===Ue.Tab&&(e=this.targets().find((t=>t.parentTarget()===e&&t.type()===Ue.Frame&&!t.targetInfo()?.subtype?.length))||null),e}browserTarget(){return this.#He}async maybeAttachInitialTarget(){if(!Boolean(a.Runtime.Runtime.queryParam("browserConnection")))return!1;this.#He||(this.#He=new qe(this,"main","browser",Ue.Browser,null,"",!1,null,void 0),this.#He.createModels(new Set(this.#Be.keysArray())));const e=await o.InspectorFrontendHost.InspectorFrontendHostInstance.initialTargetId();return this.#He.targetAgent().invoke_autoAttachRelated({targetId:e,waitForDebuggerOnStart:!0}),!0}clearAllTargetsForTest(){this.#Ne.clear()}isInScope(e){if(!e)return!1;for(function(e){return"source"in e&&e.source instanceof c}(e)&&(e=e.source),e instanceof c&&(e=e.target());e&&e!==this.#qe;)e=e.parentTarget();return Boolean(e)&&e===this.#qe}setScopeTarget(e){if(e!==this.#qe){for(const e of this.targets())if(this.isInScope(e)){for(const t of this.#Be.keysArray()){const r=e.models().get(t);if(r)for(const e of[...this.#Be.get(t)].filter((e=>this.#Ue.has(e))))e.modelRemoved(r)}for(const t of[...this.#De].filter((e=>this.#Ue.has(e))))t.targetRemoved(e)}this.#qe=e;for(const e of this.targets())if(this.isInScope(e)){for(const t of[...this.#De].filter((e=>this.#Ue.has(e))))t.targetAdded(e);for(const[t,r]of e.models().entries())for(const e of[...this.#Be.get(t)].filter((e=>this.#Ue.has(e))))e.modelAdded(r)}for(const e of this.#ze)e();e&&e.inspectedURL()&&this.onInspectedURLChange(e)}}addScopeChangeListener(e){this.#ze.add(e)}removeScopeChangeListener(e){this.#ze.delete(e)}scopeTarget(){return this.#qe}}var Ve=Object.freeze({__proto__:null,TargetManager:je,Observer:class{targetAdded(e){}targetRemoved(e){}},SDKModelObserver:class{modelAdded(e){}modelRemoved(e){}}});let We=null;class Ge extends e.ObjectWrapper.ObjectWrapper{#je=new WeakMap;#Ve=new Map;#We=new Map;#Ge=null;#Ke=new Map;#Qe=new Map;constructor(){super(),je.instance().observeModels(mn,this)}static instance({forceNew:e}={forceNew:!1}){return We&&!e||(We=new Ge),We}modelAdded(e){const t=e.addEventListener(gn.FrameAdded,this.frameAdded,this),r=e.addEventListener(gn.FrameDetached,this.frameDetached,this),n=e.addEventListener(gn.FrameNavigated,this.frameNavigated,this),s=e.addEventListener(gn.ResourceAdded,this.resourceAdded,this);this.#je.set(e,[t,r,n,s]),this.#We.set(e.target().id(),new Set)}modelRemoved(t){const r=this.#je.get(t);r&&e.EventTarget.removeEventListeners(r);const n=this.#We.get(t.target().id());if(n)for(const e of n)this.decreaseOrRemoveFrame(e);this.#We.delete(t.target().id())}frameAdded(e){const t=e.data,r=this.#Ve.get(t.id);if(r)t.setCreationStackTrace(r.frame.getCreationStackTraceData()),this.#Ve.set(t.id,{frame:t,count:r.count+1});else{const e=this.#Ke.get(t.id);e?.creationStackTrace&&e?.creationStackTraceTarget&&t.setCreationStackTrace({creationStackTrace:e.creationStackTrace,creationStackTraceTarget:e.creationStackTraceTarget}),this.#Ve.set(t.id,{frame:t,count:1}),this.#Ke.delete(t.id)}this.resetOutermostFrame();const n=this.#We.get(t.resourceTreeModel().target().id());n&&n.add(t.id),this.dispatchEventToListeners("FrameAddedToTarget",{frame:t}),this.resolveAwaitedFrame(t)}frameDetached(e){const{frame:t,isSwap:r}=e.data;if(this.decreaseOrRemoveFrame(t.id),r&&!this.#Ve.get(t.id)){const e=t.getCreationStackTraceData(),r={...e.creationStackTrace&&{creationStackTrace:e.creationStackTrace},...e.creationStackTrace&&{creationStackTraceTarget:e.creationStackTraceTarget}};this.#Ke.set(t.id,r)}const n=this.#We.get(t.resourceTreeModel().target().id());n&&n.delete(t.id)}frameNavigated(e){const t=e.data;this.dispatchEventToListeners("FrameNavigated",{frame:t}),t.isOutermostFrame()&&this.dispatchEventToListeners("OutermostFrameNavigated",{frame:t})}resourceAdded(e){this.dispatchEventToListeners("ResourceAdded",{resource:e.data})}decreaseOrRemoveFrame(e){const t=this.#Ve.get(e);t&&(1===t.count?(this.#Ve.delete(e),this.resetOutermostFrame(),this.dispatchEventToListeners("FrameRemoved",{frameId:e})):t.count--)}resetOutermostFrame(){const e=this.getAllFrames().filter((e=>e.isOutermostFrame()));this.#Ge=e.length>0?e[0]:null}getFrame(e){const t=this.#Ve.get(e);return t?t.frame:null}getAllFrames(){return Array.from(this.#Ve.values(),(e=>e.frame))}getOutermostFrame(){return this.#Ge}async getOrWaitForFrame(e,t){const r=this.getFrame(e);return!r||t&&t===r.resourceTreeModel().target()?new Promise((r=>{const n=this.#Qe.get(e);n?n.push({notInTarget:t,resolve:r}):this.#Qe.set(e,[{notInTarget:t,resolve:r}])})):r}resolveAwaitedFrame(e){const t=this.#Qe.get(e.id);if(!t)return;const r=t.filter((({notInTarget:t,resolve:r})=>!(!t||t!==e.resourceTreeModel().target())||(r(e),!1)));r.length>0?this.#Qe.set(e.id,r):this.#Qe.delete(e.id)}}var Ke=Object.freeze({__proto__:null,FrameManager:Ge});class Qe{static fromLocalObject(e){return new Ze(e)}static type(e){if(null===e)return"null";const t=typeof e;return"object"!==t&&"function"!==t?t:e.type}static isNullOrUndefined(e){if(void 0===e)return!0;switch(e.type){case"object":return"null"===e.subtype;case"undefined":return!0;default:return!1}}static arrayNameFromDescription(e){return e.replace(rt,"").replace(nt,"")}static arrayLength(e){if("array"!==e.subtype&&"typedarray"!==e.subtype)return 0;const t=e.description&&e.description.match(rt),r=e.description&&e.description.match(nt);return t?parseInt(t[1],10):r?parseInt(r[1],10):0}static arrayBufferByteLength(e){if("arraybuffer"!==e.subtype)return 0;const t=e.description&&e.description.match(rt);return t?parseInt(t[1],10):0}static unserializableDescription(e){if("number"==typeof e){const t=String(e);if(0===e&&1/e<0)return"-0";if("NaN"===t||"Infinity"===t||"-Infinity"===t)return t}return"bigint"==typeof e?e+"n":null}static toCallArgument(e){const t=typeof e;if("undefined"===t)return{};const r=Qe.unserializableDescription(e);if("number"===t)return null!==r?{unserializableValue:r}:{value:e};if("bigint"===t)return{unserializableValue:r};if("string"===t||"boolean"===t)return{value:e};if(!e)return{value:null};const n=e;if(e instanceof Qe){const t=e.unserializableValue();if(void 0!==t)return{unserializableValue:t}}else if(void 0!==n.unserializableValue)return{unserializableValue:n.unserializableValue};return void 0!==n.objectId?{objectId:n.objectId}:{value:n.value}}static async loadFromObjectPerProto(e,t,r=!1){const n=await Promise.all([e.getAllProperties(!0,t,r),e.getOwnProperties(t,r)]),s=n[0].properties,i=n[1].properties,a=n[1].internalProperties;if(!i||!s)return{properties:null,internalProperties:null};const o=new Map,l=[];for(let e=0;e<s.length;e++){const t=s[e];t.symbol?l.push(t):(t.isOwn||"__proto__"!==t.name)&&o.set(t.name,t)}for(let e=0;e<i.length;e++){const t=i[e];t.isAccessorProperty()||(t.private||t.symbol?l.push(t):o.set(t.name,t))}return{properties:[...o.values()].concat(l),internalProperties:a||null}}customPreview(){return null}get objectId(){return"Not implemented"}get type(){throw"Not implemented"}get subtype(){throw"Not implemented"}get value(){throw"Not implemented"}unserializableValue(){throw"Not implemented"}get description(){throw"Not implemented"}set description(e){throw"Not implemented"}get hasChildren(){throw"Not implemented"}get preview(){}get className(){return null}arrayLength(){throw"Not implemented"}arrayBufferByteLength(){throw"Not implemented"}getOwnProperties(e,t){throw"Not implemented"}getAllProperties(e,t,r){throw"Not implemented"}async deleteProperty(e){throw"Not implemented"}async setPropertyValue(e,t){throw"Not implemented"}callFunction(e,t){throw"Not implemented"}callFunctionJSON(e,t){throw"Not implemented"}release(){}debuggerModel(){throw new Error("DebuggerModel-less object")}runtimeModel(){throw new Error("RuntimeModel-less object")}isNode(){return!1}isLinearMemoryInspectable(){return!1}webIdl}class $e extends Qe{runtimeModelInternal;#$e;#g;#Xe;#Je;#Ye;hasChildrenInternal;#Ze;#et;#u;#tt;#rt;constructor(e,t,r,n,s,i,a,o,l,d){super(),this.runtimeModelInternal=e,this.#$e=e.target().runtimeAgent(),this.#g=r,this.#Xe=n,t?(this.#Je=t,this.#Ye=a,this.hasChildrenInternal="symbol"!==r,this.#Ze=o):(this.#Ye=a,!this.description&&i&&(this.#Ye=i),this.#Ye||"object"==typeof s&&null!==s||(this.#Ye=String(s)),this.hasChildrenInternal=!1,"string"==typeof i?(this.#et=i,"Infinity"===i||"-Infinity"===i||"-0"===i||"NaN"===i?this.#u=Number(i):"bigint"===r&&i.endsWith("n")?this.#u=BigInt(i.substring(0,i.length-1)):this.#u=i):this.#u=s),this.#tt=l||null,this.#rt="string"==typeof d?d:null}customPreview(){return this.#tt}get objectId(){return this.#Je}get type(){return this.#g}get subtype(){return this.#Xe}get value(){return this.#u}unserializableValue(){return this.#et}get description(){return this.#Ye}set description(e){this.#Ye=e}get hasChildren(){return this.hasChildrenInternal}get preview(){return this.#Ze}get className(){return this.#rt}getOwnProperties(e,t=!1){return this.doGetProperties(!0,!1,t,e)}getAllProperties(e,t,r=!1){return this.doGetProperties(!1,e,r,t)}async createRemoteObject(e){return this.runtimeModelInternal.createRemoteObject(e)}async doGetProperties(e,t,r,n){if(!this.#Je)return{properties:null,internalProperties:null};const s=await this.#$e.invoke_getProperties({objectId:this.#Je,ownProperties:e,accessorPropertiesOnly:t,nonIndexedPropertiesOnly:r,generatePreview:n});if(s.getError())return{properties:null,internalProperties:null};if(s.exceptionDetails)return this.runtimeModelInternal.exceptionThrown(Date.now(),s.exceptionDetails),{properties:null,internalProperties:null};const{result:i=[],internalProperties:a=[],privateProperties:o=[]}=s,l=[];for(const e of i){const t=e.value?await this.createRemoteObject(e.value):null,r=e.symbol?this.runtimeModelInternal.createRemoteObject(e.symbol):null,n=new Ye(e.name,t,Boolean(e.enumerable),Boolean(e.writable),Boolean(e.isOwn),Boolean(e.wasThrown),r);void 0===e.value&&(e.get&&"undefined"!==e.get.type&&(n.getter=this.runtimeModelInternal.createRemoteObject(e.get)),e.set&&"undefined"!==e.set.type&&(n.setter=this.runtimeModelInternal.createRemoteObject(e.set))),l.push(n)}for(const e of o){const t=e.value?this.runtimeModelInternal.createRemoteObject(e.value):null,r=new Ye(e.name,t,!0,!0,!0,!1,void 0,!1,void 0,!0);void 0===e.value&&(e.get&&"undefined"!==e.get.type&&(r.getter=this.runtimeModelInternal.createRemoteObject(e.get)),e.set&&"undefined"!==e.set.type&&(r.setter=this.runtimeModelInternal.createRemoteObject(e.set))),l.push(r)}const d=[];for(const e of a){if(!e.value)continue;if("[[StableObjectId]]"===e.name)continue;const t=this.runtimeModelInternal.createRemoteObject(e.value);d.push(new Ye(e.name,t,!0,!1,void 0,void 0,void 0,!0))}return{properties:l,internalProperties:d}}async setPropertyValue(e,t){if(!this.#Je)return"Can’t set a property of non-object.";const r=await this.#$e.invoke_evaluate({expression:t,silent:!0});if(r.getError()||r.exceptionDetails)return r.getError()||("string"!==r.result.type?r.result.description:r.result.value);"string"==typeof e&&(e=Qe.toCallArgument(e));const n=this.doSetObjectPropertyValue(r.result,e);return r.result.objectId&&this.#$e.invoke_releaseObject({objectId:r.result.objectId}),n}async doSetObjectPropertyValue(e,t){const r=[t,Qe.toCallArgument(e)],n=await this.#$e.invoke_callFunctionOn({objectId:this.#Je,functionDeclaration:"function(a, b) { this[a] = b; }",arguments:r,silent:!0}),s=n.getError();return s||n.exceptionDetails?s||n.result.description:void 0}async deleteProperty(e){if(!this.#Je)return"Can’t delete a property of non-object.";const t=await this.#$e.invoke_callFunctionOn({objectId:this.#Je,functionDeclaration:"function(a) { delete this[a]; return !(a in this); }",arguments:[e],silent:!0});return t.getError()||t.exceptionDetails?t.getError()||t.result.description:t.result.value?void 0:"Failed to delete property."}async callFunction(e,t){const r=await this.#$e.invoke_callFunctionOn({objectId:this.#Je,functionDeclaration:e.toString(),arguments:t,silent:!0});return r.getError()?{object:null,wasThrown:!1}:{object:this.runtimeModelInternal.createRemoteObject(r.result),wasThrown:Boolean(r.exceptionDetails)}}async callFunctionJSON(e,t){const r=await this.#$e.invoke_callFunctionOn({objectId:this.#Je,functionDeclaration:e.toString(),arguments:t,silent:!0,returnByValue:!0});return r.getError()||r.exceptionDetails?null:r.result.value}release(){this.#Je&&this.#$e.invoke_releaseObject({objectId:this.#Je})}arrayLength(){return Qe.arrayLength(this)}arrayBufferByteLength(){return Qe.arrayBufferByteLength(this)}debuggerModel(){return this.runtimeModelInternal.debuggerModel()}runtimeModel(){return this.runtimeModelInternal}isNode(){return Boolean(this.#Je)&&"object"===this.type&&"node"===this.subtype}isLinearMemoryInspectable(){return"object"===this.type&&void 0!==this.subtype&&["webassemblymemory","typedarray","dataview","arraybuffer"].includes(this.subtype)}}class Xe extends $e{#nt;#st;constructor(e,t,r,n,s,i,a,o,l){super(e,t,n,s,i,a,o,l),this.#nt=r,this.#st=void 0}async doGetProperties(e,t,r){if(t)return{properties:[],internalProperties:[]};if(this.#st)return{properties:this.#st.slice(),internalProperties:null};const n=await super.doGetProperties(e,t,!1,!0);if(this.#nt&&Array.isArray(n.properties)&&(this.#st=n.properties.slice(),!this.#nt.callFrameId))for(const e of this.#st)e.writable=!1;return n}async doSetObjectPropertyValue(e,t){const r=t.value,n=await this.debuggerModel().setVariableValue(this.#nt.number,r,Qe.toCallArgument(e),this.#nt.callFrameId);if(n)return n;if(this.#st)for(const t of this.#st)t.name===r&&(t.value=this.runtimeModel().createRemoteObject(e))}}class Je{number;callFrameId;constructor(e,t){this.number=e,this.callFrameId=t}}class Ye{name;value;enumerable;writable;isOwn;wasThrown;symbol;synthetic;syntheticSetter;private;getter;setter;webIdl;constructor(e,t,r,n,s,i,a,o,l,d){this.name=e,this.value=null!==t?t:void 0,this.enumerable=void 0===r||r;const c=!o||Boolean(l);this.writable=void 0!==n?n:c,this.isOwn=Boolean(s),this.wasThrown=Boolean(i),a&&(this.symbol=a),this.synthetic=Boolean(o),l&&(this.syntheticSetter=l),this.private=Boolean(d)}async setSyntheticValue(e){if(!this.syntheticSetter)return!1;const t=await this.syntheticSetter(e);return t&&(this.value=t),Boolean(t)}isAccessorProperty(){return Boolean(this.getter||this.setter)}match({includeNullOrUndefinedValues:e,regex:t}){return!(null!==t&&!t.test(this.name)&&!t.test(this.value?.description??""))&&!(!e&&!this.isAccessorProperty()&&Qe.isNullOrUndefined(this.value))}cloneWithNewName(e){const t=new Ye(e,this.value??null,this.enumerable,this.writable,this.isOwn,this.wasThrown,this.symbol,this.synthetic,this.syntheticSetter,this.private);return t.getter=this.getter,t.setter=this.setter,t}}class Ze extends Qe{valueInternal;#it;#at;constructor(e){super(),this.valueInternal=e}get objectId(){}get value(){return this.valueInternal}unserializableValue(){return Qe.unserializableDescription(this.valueInternal)||void 0}get description(){if(this.#it)return this.#it;if("object"===this.type)switch(this.subtype){case"array":this.#it=this.concatenate("[","]",function(e){return this.formatValue(e.value||null)}.bind(this));break;case"date":this.#it=String(this.valueInternal);break;case"null":this.#it="null";break;default:this.#it=this.concatenate("{","}",function(e){let t=e.name;return/^\s|\s$|^$|\n/.test(t)&&(t='"'+t.replace(/\n/g,"↵")+'"'),t+": "+this.formatValue(e.value||null)}.bind(this))}else this.#it=String(this.valueInternal);return this.#it}formatValue(e){if(!e)return"undefined";const t=e.description||"";return"string"===e.type?'"'+t.replace(/\n/g,"↵")+'"':t}concatenate(e,t,r){let n=e;const s=this.children();for(let e=0;e<s.length;++e){const t=r(s[e]);if(n.length+t.length>100){n+=",…";break}e&&(n+=", "),n+=t}return n+=t,n}get type(){return typeof this.valueInternal}get subtype(){return null===this.valueInternal?"null":Array.isArray(this.valueInternal)?"array":this.valueInternal instanceof Date?"date":void 0}get hasChildren(){return"object"==typeof this.valueInternal&&null!==this.valueInternal&&Boolean(Object.keys(this.valueInternal).length)}async getOwnProperties(e,t=!1){let r=this.children();return t&&(r=r.filter((e=>!function(e){const t=Number(e)>>>0;return String(t)===e}(e.name)))),{properties:r,internalProperties:null}}async getAllProperties(e,t,r=!1){return e?{properties:[],internalProperties:null}:await this.getOwnProperties(t,r)}children(){return this.hasChildren?(this.#at||(this.#at=Object.entries(this.valueInternal).map((([e,t])=>new Ye(e,t instanceof Qe?t:Qe.fromLocalObject(t))))),this.#at):[]}arrayLength(){return Array.isArray(this.valueInternal)?this.valueInternal.length:0}async callFunction(e,t){const r=this.valueInternal,n=t?t.map((e=>e.value)):[];let s,i=!1;try{s=e.apply(r,n)}catch(e){i=!0}return{object:Qe.fromLocalObject(s),wasThrown:i}}async callFunctionJSON(e,t){const r=this.valueInternal,n=t?t.map((e=>e.value)):[];let s;try{s=e.apply(r,n)}catch(e){s=null}return s}}class et{#ot;constructor(e){this.#ot=e}static objectAsArray(e){if(!e||"object"!==e.type||"array"!==e.subtype&&"typedarray"!==e.subtype)throw new Error("Object is empty or not an array");return new et(e)}static async createFromRemoteObjects(e){if(!e.length)throw new Error("Input array is empty");const t=await e[0].callFunction((function(...e){return e}),e.map(Qe.toCallArgument));if(t.wasThrown||!t.object)throw new Error("Call function throws exceptions or returns empty value");return et.objectAsArray(t.object)}async at(e){if(e<0||e>this.#ot.arrayLength())throw new Error("Out of range");const t=await this.#ot.callFunction((function(e){return this[e]}),[Qe.toCallArgument(e)]);if(t.wasThrown||!t.object)throw new Error("Exception in callFunction or result value is empty");return t.object}length(){return this.#ot.arrayLength()}map(e){const t=[];for(let r=0;r<this.length();++r)t.push(this.at(r).then(e));return Promise.all(t)}object(){return this.#ot}}class tt{#ot;constructor(e){this.#ot=e}static objectAsFunction(e){if(!e||"function"!==e.type)throw new Error("Object is empty or not a function");return new tt(e)}targetFunction(){return this.#ot.getOwnProperties(!1).then(function(e){if(!e.internalProperties)return this.#ot;const t=e.internalProperties;for(const e of t)if("[[TargetFunction]]"===e.name)return e.value;return this.#ot}.bind(this))}targetFunctionDetails(){return this.targetFunction().then(function(t){const r=e.bind(null,this.#ot!==t?t:null);return t.debuggerModel().functionDetailsPromise(t).then(r)}.bind(this));function e(e,t){return e&&e.release(),t}}object(){return this.#ot}}const rt=/\(([0-9]+)\)/,nt=/\[([0-9]+)\]/;var st=Object.freeze({__proto__:null,RemoteObject:Qe,RemoteObjectImpl:$e,ScopeRemoteObject:Xe,ScopeRef:Je,RemoteObjectProperty:Ye,LocalJSONObject:Ze,RemoteArrayBuffer:class{#ot;constructor(e){if("object"!==e.type||"arraybuffer"!==e.subtype)throw new Error("Object is not an arraybuffer");this.#ot=e}byteLength(){return this.#ot.arrayBufferByteLength()}async bytes(e=0,t=this.byteLength()){if(e<0||e>=this.byteLength())throw new RangeError("start is out of range");if(t<e||t>this.byteLength())throw new RangeError("end is out of range");return await this.#ot.callFunctionJSON((function(e,t){return[...new Uint8Array(this,e,t)]}),[{value:e},{value:t-e}])}object(){return this.#ot}},RemoteArray:et,RemoteFunction:tt,LinearMemoryInspectable:class{object;expression;constructor(e,t){if(!e.isLinearMemoryInspectable())throw new Error("object must be linear memory inspectable");this.object=e,this.expression=t}}});class it extends c{constructor(e){super(e)}async read(t,r,n){const s=await this.target().ioAgent().invoke_read({handle:t,offset:n,size:r});if(s.getError())throw new Error(s.getError());return s.eof?null:s.base64Encoded?e.Base64.decode(s.data):s.data}async close(e){(await this.target().ioAgent().invoke_close({handle:e})).getError()&&console.error("Could not close stream.")}async resolveBlob(e){const t=e instanceof Qe?e.objectId:e;if(!t)throw new Error("Remote object has undefined objectId");const r=await this.target().ioAgent().invoke_resolveBlob({objectId:t});if(r.getError())throw new Error(r.getError());return`blob:${r.uuid}`}async readToString(e){const t=[],r=new TextDecoder;for(;;){const n=await this.read(e,4194304);if(null===n){t.push(r.decode());break}n instanceof ArrayBuffer?t.push(r.decode(n,{stream:!0})):t.push(n)}return t.join("")}}c.register(it,{capabilities:131072,autostart:!0});var at=Object.freeze({__proto__:null,IOModel:it});const ot={noContentForWebSocket:"Content for WebSockets is currently not supported",noContentForRedirect:"No content available because this request was redirected",noContentForPreflight:"No content available for preflight request",noThrottling:"No throttling",offline:"Offline",slowG:"Slow 3G",fastG:"Fast 3G",requestWasBlockedByDevtoolsS:'Request was blocked by DevTools: "{PH1}"',sFailedLoadingSS:'{PH1} failed loading: {PH2} "{PH3}".',sFinishedLoadingSS:'{PH1} finished loading: {PH2} "{PH3}".'},lt=i.i18n.registerUIStrings("core/sdk/NetworkManager.ts",ot),dt=i.i18n.getLocalizedString.bind(void 0,lt),ct=i.i18n.getLazilyComputedLocalizedString.bind(void 0,lt),ht=new WeakMap,ut=new Map([["2g","cellular2g"],["3g","cellular3g"],["4g","cellular4g"],["bluetooth","bluetooth"],["wifi","wifi"],["wimax","wimax"]]);class gt extends c{dispatcher;fetchDispatcher;#lt;#dt;constructor(t){super(t),this.dispatcher=new kt(this),this.fetchDispatcher=new It(t.fetchAgent(),this),this.#lt=t.networkAgent(),t.registerNetworkDispatcher(this.dispatcher),t.registerFetchDispatcher(this.fetchDispatcher),e.Settings.Settings.instance().moduleSetting("cache-disabled").get()&&this.#lt.invoke_setCacheDisabled({cacheDisabled:!0}),this.#lt.invoke_enable({maxPostDataSize:vt}),this.#lt.invoke_setAttachDebugStack({enabled:!0}),this.#dt=e.Settings.Settings.instance().createSetting("bypass-service-worker",!1),this.#dt.get()&&this.bypassServiceWorkerChanged(),this.#dt.addChangeListener(this.bypassServiceWorkerChanged,this),e.Settings.Settings.instance().moduleSetting("cache-disabled").addChangeListener(this.cacheDisabledSettingChanged,this)}static forRequest(e){return ht.get(e)||null}static canReplayRequest(t){return Boolean(ht.get(t))&&Boolean(t.backendRequestId())&&!t.isRedirect()&&t.resourceType()===e.ResourceType.resourceTypes.XHR}static replayRequest(e){const t=ht.get(e),r=e.backendRequestId();t&&r&&!e.isRedirect()&&t.#lt.invoke_replayXHR({requestId:r})}static async searchInRequest(e,t,r,n){const i=gt.forRequest(e),a=e.backendRequestId();if(!i||!a||e.isRedirect())return[];const o=await i.#lt.invoke_searchInResponseBody({requestId:a,query:t,caseSensitive:r,isRegex:n});return s.TextUtils.performSearchInSearchMatches(o.result||[],t,r,n)}static async requestContentData(t){if(t.resourceType()===e.ResourceType.resourceTypes.WebSocket)return{error:dt(ot.noContentForWebSocket)};if(t.finished||await t.once(Bn.FinishedLoading),t.isRedirect())return{error:dt(ot.noContentForRedirect)};if(t.isPreflightRequest())return{error:dt(ot.noContentForPreflight)};const r=gt.forRequest(t);if(!r)return{error:"No network manager for request"};const n=t.backendRequestId();if(!n)return{error:"No backend request id for request"};const i=await r.#lt.invoke_getResponseBody({requestId:n}),a=i.getError();return a?{error:a}:new s.ContentData.ContentData(i.body,i.base64Encoded,t.mimeType,t.charset()??void 0)}static async streamResponseBody(e){if(e.finished)return{error:"Streaming the response body is only available for in-flight requests."};const t=gt.forRequest(e);if(!t)return{error:"No network manager for request"};const r=e.backendRequestId();if(!r)return{error:"No backend request id for request"};const n=await t.#lt.invoke_streamResourceContent({requestId:r}),i=n.getError();return i?{error:i}:new s.ContentData.ContentData(n.bufferedData,!0,e.mimeType,e.charset()??void 0)}static async requestPostData(e){const t=gt.forRequest(e);if(!t)return console.error("No network manager for request"),null;const r=e.backendRequestId();if(!r)return console.error("No backend request id for request"),null;try{const{postData:e}=await t.#lt.invoke_getRequestPostData({requestId:r});return e}catch(e){return e.message}}static connectionType(e){if(!e.download&&!e.upload)return"none";const t="function"==typeof e.title?e.title().toLowerCase():e.title.toLowerCase();for(const[e,r]of ut)if(t.includes(e))return r;return"other"}static lowercaseHeaders(e){const t={};for(const r in e)t[r.toLowerCase()]=e[r];return t}requestForURL(e){return this.dispatcher.requestForURL(e)}requestForId(e){return this.dispatcher.requestForId(e)}requestForLoaderId(e){return this.dispatcher.requestForLoaderId(e)}cacheDisabledSettingChanged({data:e}){this.#lt.invoke_setCacheDisabled({cacheDisabled:e})}dispose(){e.Settings.Settings.instance().moduleSetting("cache-disabled").removeChangeListener(this.cacheDisabledSettingChanged,this)}bypassServiceWorkerChanged(){this.#lt.invoke_setBypassServiceWorker({bypass:this.#dt.get()})}async getSecurityIsolationStatus(e){const t=await this.#lt.invoke_getSecurityIsolationStatus({frameId:e??void 0});return t.getError()?null:t.status}async enableReportingApi(e=!0){return this.#lt.invoke_enableReportingApi({enable:e})}async loadNetworkResource(e,t,r){const n=await this.#lt.invoke_loadNetworkResource({frameId:e??void 0,url:t,options:r});if(n.getError())throw new Error(n.getError());return n.resource}clearRequests(){this.dispatcher.clearRequests()}}var pt;!function(e){e.RequestStarted="RequestStarted",e.RequestUpdated="RequestUpdated",e.RequestFinished="RequestFinished",e.RequestUpdateDropped="RequestUpdateDropped",e.ResponseReceived="ResponseReceived",e.MessageGenerated="MessageGenerated",e.RequestRedirected="RequestRedirected",e.LoadingFinished="LoadingFinished",e.ReportingApiReportAdded="ReportingApiReportAdded",e.ReportingApiReportUpdated="ReportingApiReportUpdated",e.ReportingApiEndpointsChangedForOrigin="ReportingApiEndpointsChangedForOrigin"}(pt||(pt={}));const mt={title:ct(ot.noThrottling),i18nTitleKey:ot.noThrottling,download:-1,upload:-1,latency:0},ft={title:ct(ot.offline),i18nTitleKey:ot.offline,download:0,upload:0,latency:0},bt={title:ct(ot.slowG),i18nTitleKey:ot.slowG,download:5e4,upload:5e4,latency:2e3},yt={title:ct(ot.fastG),i18nTitleKey:ot.fastG,download:18e4,upload:84375,latency:562.5},vt=65536;class It{#ct;#ht;constructor(e,t){this.#ct=e,this.#ht=t}requestPaused({requestId:e,request:t,resourceType:r,responseStatusCode:n,responseHeaders:s,networkId:i}){const a=i?this.#ht.requestForId(i):null;0===a?.originalResponseHeaders.length&&s&&(a.originalResponseHeaders=s),St.instance().requestIntercepted(new Ct(this.#ct,t,r,e,a,n,s))}authRequired({}){}}class kt{#ht;#ut;#gt;#pt;#mt;#ft;constructor(e){this.#ht=e,this.#ut=new Map,this.#gt=new Map,this.#pt=new Map,this.#mt=new Map,this.#ft=new Map,St.instance().addEventListener("RequestIntercepted",this.#bt.bind(this))}#bt(e){const t=this.requestForId(e.data);t&&t.setWasIntercepted(!0)}headersMapToHeadersArray(e){const t=[];for(const r in e){const n=e[r].split("\n");for(let e=0;e<n.length;++e)t.push({name:r,value:n[e]})}return t}updateNetworkRequestWithRequest(e,t){e.requestMethod=t.method,e.setRequestHeaders(this.headersMapToHeadersArray(t.headers)),e.setRequestFormData(Boolean(t.hasPostData),t.postData||null),e.setInitialPriority(t.initialPriority),e.mixedContentType=t.mixedContentType||"none",e.setReferrerPolicy(t.referrerPolicy),e.setIsSameSite(t.isSameSite||!1)}updateNetworkRequestWithResponse(t,r){r.url&&t.url()!==r.url&&t.setUrl(r.url),t.mimeType=r.mimeType,t.setCharset(r.charset),t.statusCode&&!t.wasIntercepted()||(t.statusCode=r.status),t.statusText&&!t.wasIntercepted()||(t.statusText=r.statusText),t.hasExtraResponseInfo()&&!t.wasIntercepted()||(t.responseHeaders=this.headersMapToHeadersArray(r.headers)),r.encodedDataLength>=0&&t.setTransferSize(r.encodedDataLength),r.requestHeaders&&!t.hasExtraRequestInfo()&&(t.setRequestHeaders(this.headersMapToHeadersArray(r.requestHeaders)),t.setRequestHeadersText(r.requestHeadersText||"")),t.connectionReused=r.connectionReused,t.connectionId=String(r.connectionId),r.remoteIPAddress&&t.setRemoteAddress(r.remoteIPAddress,r.remotePort||-1),r.fromServiceWorker&&(t.fetchedViaServiceWorker=!0),r.fromDiskCache&&t.setFromDiskCache(),r.fromPrefetchCache&&t.setFromPrefetchCache(),r.cacheStorageCacheName&&t.setResponseCacheStorageCacheName(r.cacheStorageCacheName),r.serviceWorkerRouterInfo&&(t.serviceWorkerRouterInfo=r.serviceWorkerRouterInfo),r.responseTime&&t.setResponseRetrievalTime(new Date(r.responseTime)),t.timing=r.timing,t.protocol=r.protocol||"",t.alternateProtocolUsage=r.alternateProtocolUsage,r.serviceWorkerResponseSource&&t.setServiceWorkerResponseSource(r.serviceWorkerResponseSource),t.setSecurityState(r.securityState),r.securityDetails&&t.setSecurityDetails(r.securityDetails);const n=e.ResourceType.ResourceType.fromMimeTypeOverride(t.mimeType);n&&t.setResourceType(n)}requestForId(e){return this.#ut.get(e)||null}requestForURL(e){return this.#gt.get(e)||null}requestForLoaderId(e){return this.#pt.get(e)||null}resourceChangedPriority({requestId:e,newPriority:t}){const r=this.#ut.get(e);r&&r.setPriority(t)}signedExchangeReceived({requestId:t,info:r}){let n=this.#ut.get(t);(n||(n=this.#gt.get(r.outerResponse.url),n))&&(n.setSignedExchangeInfo(r),n.setResourceType(e.ResourceType.resourceTypes.SignedExchange),this.updateNetworkRequestWithResponse(n,r.outerResponse),this.updateNetworkRequest(n),this.#ht.dispatchEventToListeners(pt.ResponseReceived,{request:n,response:r.outerResponse}))}requestWillBeSent({requestId:t,loaderId:r,documentURL:n,request:s,timestamp:i,wallTime:a,initiator:o,redirectResponse:l,type:d,frameId:c,hasUserGesture:h}){let u=this.#ut.get(t);if(u){if(!l)return;u.signedExchangeInfo()||this.responseReceived({requestId:t,loaderId:r,timestamp:i,type:d||"Other",response:l,hasExtraInfo:!1,frameId:c}),u=this.appendRedirect(t,i,s.url),this.#ht.dispatchEventToListeners(pt.RequestRedirected,u)}else u=Fn.create(t,s.url,n,c??null,r,o,h),ht.set(u,this.#ht);u.hasNetworkData=!0,this.updateNetworkRequestWithRequest(u,s),u.setIssueTime(i,a),u.setResourceType(d?e.ResourceType.resourceTypes[d]:e.ResourceType.resourceTypes.Other),s.trustTokenParams&&u.setTrustTokenParams(s.trustTokenParams);const g=this.#ft.get(t);g&&(u.setTrustTokenOperationDoneEvent(g),this.#ft.delete(t)),this.getExtraInfoBuilder(t).addRequest(u),this.startNetworkRequest(u,s)}requestServedFromCache({requestId:e}){const t=this.#ut.get(e);t&&t.setFromMemoryCache()}responseReceived({requestId:t,loaderId:r,timestamp:n,type:s,response:i,frameId:a}){const o=this.#ut.get(t),l=gt.lowercaseHeaders(i.headers);if(o)o.responseReceivedTime=n,o.setResourceType(e.ResourceType.resourceTypes[s]),this.updateNetworkRequestWithResponse(o,i),this.updateNetworkRequest(o),this.#ht.dispatchEventToListeners(pt.ResponseReceived,{request:o,response:i});else{const e=l["last-modified"],t={url:i.url,frameId:a??null,loaderId:r,resourceType:s,mimeType:i.mimeType,lastModified:e?new Date(e):null};this.#ht.dispatchEventToListeners(pt.RequestUpdateDropped,t)}}dataReceived(e){let t=this.#ut.get(e.requestId);t||(t=this.maybeAdoptMainResourceRequest(e.requestId)),t&&(t.addDataReceivedEvent(e),this.updateNetworkRequest(t))}loadingFinished({requestId:e,timestamp:t,encodedDataLength:r}){let n=this.#ut.get(e);n||(n=this.maybeAdoptMainResourceRequest(e)),n&&(this.getExtraInfoBuilder(e).finished(),this.finishNetworkRequest(n,t,r),this.#ht.dispatchEventToListeners(pt.LoadingFinished,n))}loadingFailed({requestId:t,timestamp:r,type:n,errorText:s,canceled:i,blockedReason:a,corsErrorStatus:o}){const l=this.#ut.get(t);if(l){if(l.failed=!0,l.setResourceType(e.ResourceType.resourceTypes[n]),l.canceled=Boolean(i),a&&(l.setBlockedReason(a),"inspector"===a)){const e=dt(ot.requestWasBlockedByDevtoolsS,{PH1:l.url()});this.#ht.dispatchEventToListeners(pt.MessageGenerated,{message:e,requestId:t,warning:!0})}o&&l.setCorsErrorStatus(o),l.localizedFailDescription=s,this.getExtraInfoBuilder(t).finished(),this.finishNetworkRequest(l,r,-1)}}webSocketCreated({requestId:t,url:r,initiator:n}){const s=Fn.createForWebSocket(t,r,n);ht.set(s,this.#ht),s.setResourceType(e.ResourceType.resourceTypes.WebSocket),this.startNetworkRequest(s,null)}webSocketWillSendHandshakeRequest({requestId:e,timestamp:t,wallTime:r,request:n}){const s=this.#ut.get(e);s&&(s.requestMethod="GET",s.setRequestHeaders(this.headersMapToHeadersArray(n.headers)),s.setIssueTime(t,r),this.updateNetworkRequest(s))}webSocketHandshakeResponseReceived({requestId:e,timestamp:t,response:r}){const n=this.#ut.get(e);n&&(n.statusCode=r.status,n.statusText=r.statusText,n.responseHeaders=this.headersMapToHeadersArray(r.headers),n.responseHeadersText=r.headersText||"",r.requestHeaders&&n.setRequestHeaders(this.headersMapToHeadersArray(r.requestHeaders)),r.requestHeadersText&&n.setRequestHeadersText(r.requestHeadersText),n.responseReceivedTime=t,n.protocol="websocket",this.updateNetworkRequest(n))}webSocketFrameReceived({requestId:e,timestamp:t,response:r}){const n=this.#ut.get(e);n&&(n.addProtocolFrame(r,t,!1),n.responseReceivedTime=t,this.updateNetworkRequest(n))}webSocketFrameSent({requestId:e,timestamp:t,response:r}){const n=this.#ut.get(e);n&&(n.addProtocolFrame(r,t,!0),n.responseReceivedTime=t,this.updateNetworkRequest(n))}webSocketFrameError({requestId:e,timestamp:t,errorMessage:r}){const n=this.#ut.get(e);n&&(n.addProtocolFrameError(r,t),n.responseReceivedTime=t,this.updateNetworkRequest(n))}webSocketClosed({requestId:e,timestamp:t}){const r=this.#ut.get(e);r&&this.finishNetworkRequest(r,t,-1)}eventSourceMessageReceived({requestId:e,timestamp:t,eventName:r,eventId:n,data:s}){const i=this.#ut.get(e);i&&i.addEventSourceMessage(t,r,n,s)}requestIntercepted({}){}requestWillBeSentExtraInfo({requestId:e,associatedCookies:t,headers:r,clientSecurityState:n,connectTiming:s,siteHasCookieInOtherPartition:i}){const a=[],o=[];for(const{blockedReasons:e,exemptionReason:r,cookie:n}of t)0===e.length?o.push({exemptionReason:r,cookie:F.fromProtocolCookie(n)}):a.push({blockedReasons:e,cookie:F.fromProtocolCookie(n)});const l={blockedRequestCookies:a,includedRequestCookies:o,requestHeaders:this.headersMapToHeadersArray(r),clientSecurityState:n,connectTiming:s,siteHasCookieInOtherPartition:i};this.getExtraInfoBuilder(e).addRequestExtraInfo(l)}responseReceivedExtraInfo({requestId:e,blockedCookies:t,headers:r,headersText:n,resourceIPAddressSpace:s,statusCode:i,cookiePartitionKey:a,cookiePartitionKeyOpaque:o,exemptedCookies:l}){const d={blockedResponseCookies:t.map((e=>({blockedReasons:e.blockedReasons,cookieLine:e.cookieLine,cookie:e.cookie?F.fromProtocolCookie(e.cookie):null}))),responseHeaders:this.headersMapToHeadersArray(r),responseHeadersText:n,resourceIPAddressSpace:s,statusCode:i,cookiePartitionKey:a,cookiePartitionKeyOpaque:o,exemptedResponseCookies:l?.map((e=>({cookie:F.fromProtocolCookie(e.cookie),exemptionReason:e.exemptionReason})))};this.getExtraInfoBuilder(e).addResponseExtraInfo(d)}getExtraInfoBuilder(e){let t;return this.#mt.has(e)?t=this.#mt.get(e):(t=new Rt,this.#mt.set(e,t)),t}appendRedirect(e,t,r){const n=this.#ut.get(e);if(!n)throw new Error(`Could not find original network request for ${e}`);let s=0;for(let e=n.redirectSource();e;e=e.redirectSource())s++;n.markAsRedirect(s),this.finishNetworkRequest(n,t,-1);const i=Fn.create(e,r,n.documentURL,n.frameId,n.loaderId,n.initiator(),n.hasUserGesture()??void 0);return ht.set(i,this.#ht),i.setRedirectSource(n),n.setRedirectDestination(i),i}maybeAdoptMainResourceRequest(e){const t=St.instance().inflightMainResourceRequests.get(e);if(!t)return null;const r=gt.forRequest(t).dispatcher;r.#ut.delete(e),r.#gt.delete(t.url());const n=t.loaderId;n&&r.#pt.delete(n);const s=r.#mt.get(e);return r.#mt.delete(e),this.#ut.set(e,t),this.#gt.set(t.url(),t),n&&this.#pt.set(n,t),s&&this.#mt.set(e,s),ht.set(t,this.#ht),t}startNetworkRequest(e,t){this.#ut.set(e.requestId(),e),this.#gt.set(e.url(),e);const r=e.loaderId;r&&this.#pt.set(r,e),e.loaderId===e.requestId()&&St.instance().inflightMainResourceRequests.set(e.requestId(),e),this.#ht.dispatchEventToListeners(pt.RequestStarted,{request:e,originalRequest:t})}updateNetworkRequest(e){this.#ht.dispatchEventToListeners(pt.RequestUpdated,e)}finishNetworkRequest(t,r,n){if(t.endTime=r,t.finished=!0,n>=0){const e=t.redirectSource();e&&e.signedExchangeInfo()?(t.setTransferSize(0),e.setTransferSize(n),this.updateNetworkRequest(e)):t.setTransferSize(n)}if(t.addBlockedRequestCookiesToModel(),this.#ht.dispatchEventToListeners(pt.RequestFinished,t),St.instance().inflightMainResourceRequests.delete(t.requestId()),e.Settings.Settings.instance().moduleSetting("monitoring-xhr-enabled").get()&&t.resourceType().category()===e.ResourceType.resourceCategories.XHR){let e;const r=t.failed||t.hasErrorStatusCode();e=dt(r?ot.sFailedLoadingSS:ot.sFinishedLoadingSS,{PH1:t.resourceType().title(),PH2:t.requestMethod,PH3:t.url()}),this.#ht.dispatchEventToListeners(pt.MessageGenerated,{message:e,requestId:t.requestId(),warning:!1})}}clearRequests(){for(const[e,t]of this.#ut)t.finished&&this.#ut.delete(e);for(const[e,t]of this.#gt)t.finished&&this.#gt.delete(e);for(const[e,t]of this.#pt)t.finished&&this.#pt.delete(e);for(const[e,t]of this.#mt)t.isFinished()&&this.#mt.delete(e)}webTransportCreated({transportId:t,url:r,timestamp:n,initiator:s}){const i=Fn.createForWebSocket(t,r,s);i.hasNetworkData=!0,ht.set(i,this.#ht),i.setResourceType(e.ResourceType.resourceTypes.WebTransport),i.setIssueTime(n,0),this.startNetworkRequest(i,null)}webTransportConnectionEstablished({transportId:e,timestamp:t}){const r=this.#ut.get(e);r&&(r.responseReceivedTime=t,r.endTime=t+.001,this.updateNetworkRequest(r))}webTransportClosed({transportId:e,timestamp:t}){const r=this.#ut.get(e);r&&(r.endTime=t,this.finishNetworkRequest(r,t,0))}trustTokenOperationDone(e){const t=this.#ut.get(e.requestId);t?t.setTrustTokenOperationDoneEvent(e):this.#ft.set(e.requestId,e)}subresourceWebBundleMetadataReceived({requestId:e,urls:t}){const r=this.getExtraInfoBuilder(e);r.setWebBundleInfo({resourceUrls:t});const n=r.finalRequest();n&&this.updateNetworkRequest(n)}subresourceWebBundleMetadataError({requestId:e,errorMessage:t}){const r=this.getExtraInfoBuilder(e);r.setWebBundleInfo({errorMessage:t});const n=r.finalRequest();n&&this.updateNetworkRequest(n)}subresourceWebBundleInnerResponseParsed({innerRequestId:e,bundleRequestId:t}){const r=this.getExtraInfoBuilder(e);r.setWebBundleInnerRequestInfo({bundleRequestId:t});const n=r.finalRequest();n&&this.updateNetworkRequest(n)}subresourceWebBundleInnerResponseError({innerRequestId:e,errorMessage:t}){const r=this.getExtraInfoBuilder(e);r.setWebBundleInnerRequestInfo({errorMessage:t});const n=r.finalRequest();n&&this.updateNetworkRequest(n)}reportingApiReportAdded(e){this.#ht.dispatchEventToListeners(pt.ReportingApiReportAdded,e.report)}reportingApiReportUpdated(e){this.#ht.dispatchEventToListeners(pt.ReportingApiReportUpdated,e.report)}reportingApiEndpointsChangedForOrigin(e){this.#ht.dispatchEventToListeners(pt.ReportingApiEndpointsChangedForOrigin,e)}createNetworkRequest(e,t,r,n,s,i){const a=Fn.create(e,n,s,t,r,i);return ht.set(a,this.#ht),a}}let wt;class St extends e.ObjectWrapper.ObjectWrapper{#yt;#vt;#It;#kt;#wt;inflightMainResourceRequests;#St;#Ct;#Rt;#xt;#Tt;#Mt;#Pt;#Lt;constructor(){super(),this.#yt="",this.#vt=null,this.#It=null,this.#kt=new Set,this.#wt=new Set,this.inflightMainResourceRequests=new Map,this.#St=mt,this.#Ct=null,this.#Rt=e.Settings.Settings.instance().moduleSetting("request-blocking-enabled"),this.#xt=e.Settings.Settings.instance().createSetting("network-blocked-patterns",[]),this.#Tt=[],this.updateBlockedPatterns(),this.#Mt=new t.MapUtilities.Multimap,je.instance().observeModels(gt,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return wt&&!t||(wt=new St),wt}static dispose(){wt=null}static getChromeVersion(){const e=navigator.userAgent.match(/(?:^|\W)(?:Chrome|HeadlessChrome)\/(\S+)/);return e&&e.length>1?e[1]:""}static patchUserAgentWithChromeVersion(e){const r=St.getChromeVersion();if(r.length>0){const n=r.split(".",1)[0]+".0.100.0";return t.StringUtilities.sprintf(e,r,n)}return e}static patchUserAgentMetadataWithChromeVersion(e){if(!e.brands)return;const r=St.getChromeVersion();if(0===r.length)return;const n=r.split(".",1)[0];for(const r of e.brands)r.version.includes("%s")&&(r.version=t.StringUtilities.sprintf(r.version,n));e.fullVersion&&e.fullVersion.includes("%s")&&(e.fullVersion=t.StringUtilities.sprintf(e.fullVersion,r))}modelAdded(e){const t=e.target().networkAgent(),r=e.target().fetchAgent();this.#Pt&&t.invoke_setExtraHTTPHeaders({headers:this.#Pt}),this.currentUserAgent()&&t.invoke_setUserAgentOverride({userAgent:this.currentUserAgent(),userAgentMetadata:this.#vt||void 0}),this.#Tt.length&&t.invoke_setBlockedURLs({urls:this.#Tt}),this.isIntercepting()&&r.invoke_enable({patterns:this.#Mt.valuesArray()}),null===this.#It?t.invoke_clearAcceptedEncodingsOverride():t.invoke_setAcceptedEncodings({encodings:this.#It}),this.#kt.add(t),this.#wt.add(r),this.isThrottling()&&this.updateNetworkConditions(t)}modelRemoved(e){for(const t of this.inflightMainResourceRequests){gt.forRequest(t[1])===e&&this.inflightMainResourceRequests.delete(t[0])}this.#kt.delete(e.target().networkAgent()),this.#wt.delete(e.target().fetchAgent())}isThrottling(){return this.#St.download>=0||this.#St.upload>=0||this.#St.latency>0}isOffline(){return!this.#St.download&&!this.#St.upload}setNetworkConditions(e){this.#St=e;for(const e of this.#kt)this.updateNetworkConditions(e);this.dispatchEventToListeners("ConditionsChanged")}networkConditions(){return this.#St}updateNetworkConditions(e){const t=this.#St;this.isThrottling()?e.invoke_emulateNetworkConditions({offline:this.isOffline(),latency:t.latency,downloadThroughput:t.download<0?0:t.download,uploadThroughput:t.upload<0?0:t.upload,connectionType:gt.connectionType(t)}):e.invoke_emulateNetworkConditions({offline:!1,latency:0,downloadThroughput:0,uploadThroughput:0})}setExtraHTTPHeaders(e){this.#Pt=e;for(const e of this.#kt)e.invoke_setExtraHTTPHeaders({headers:this.#Pt})}currentUserAgent(){return this.#Lt?this.#Lt:this.#yt}updateUserAgentOverride(){const e=this.currentUserAgent();for(const t of this.#kt)t.invoke_setUserAgentOverride({userAgent:e,userAgentMetadata:this.#vt||void 0})}setUserAgentOverride(e,t){const r=this.#yt!==e;this.#yt=e,this.#Lt?this.#vt=null:(this.#vt=t,this.updateUserAgentOverride()),r&&this.dispatchEventToListeners("UserAgentChanged")}userAgentOverride(){return this.#yt}setCustomUserAgentOverride(e,t=null){this.#Lt=e,this.#vt=t,this.updateUserAgentOverride()}setCustomAcceptedEncodingsOverride(e){this.#It=e,this.updateAcceptedEncodingsOverride(),this.dispatchEventToListeners("AcceptedEncodingsChanged")}clearCustomAcceptedEncodingsOverride(){this.#It=null,this.updateAcceptedEncodingsOverride(),this.dispatchEventToListeners("AcceptedEncodingsChanged")}isAcceptedEncodingOverrideSet(){return null!==this.#It}updateAcceptedEncodingsOverride(){const e=this.#It;for(const t of this.#kt)null===e?t.invoke_clearAcceptedEncodingsOverride():t.invoke_setAcceptedEncodings({encodings:e})}blockedPatterns(){return this.#xt.get().slice()}blockingEnabled(){return this.#Rt.get()}isBlocking(){return Boolean(this.#Tt.length)}setBlockedPatterns(e){this.#xt.set(e),this.updateBlockedPatterns(),this.dispatchEventToListeners("BlockedPatternsChanged")}setBlockingEnabled(e){this.#Rt.get()!==e&&(this.#Rt.set(e),this.updateBlockedPatterns(),this.dispatchEventToListeners("BlockedPatternsChanged"))}updateBlockedPatterns(){const e=[];if(this.#Rt.get())for(const t of this.#xt.get())t.enabled&&e.push(t.url);if(e.length||this.#Tt.length){this.#Tt=e;for(const e of this.#kt)e.invoke_setBlockedURLs({urls:this.#Tt})}}isIntercepting(){return Boolean(this.#Mt.size)}setInterceptionHandlerForPatterns(e,t){this.#Mt.deleteAll(t);for(const r of e)this.#Mt.set(t,r);return this.updateInterceptionPatternsOnNextTick()}updateInterceptionPatternsOnNextTick(){return this.#Ct||(this.#Ct=Promise.resolve().then(this.updateInterceptionPatterns.bind(this))),this.#Ct}async updateInterceptionPatterns(){e.Settings.Settings.instance().moduleSetting("cache-disabled").get()||e.Settings.Settings.instance().moduleSetting("cache-disabled").set(!0),this.#Ct=null;const t=[];for(const e of this.#wt)t.push(e.invoke_enable({patterns:this.#Mt.valuesArray()}));this.dispatchEventToListeners("InterceptorsChanged"),await Promise.all(t)}async requestIntercepted(e){for(const t of this.#Mt.keysArray())if(await t(e),e.hasResponded()&&e.networkRequest)return void this.dispatchEventToListeners("RequestIntercepted",e.networkRequest.requestId());e.hasResponded()||e.continueRequestWithoutChange()}clearBrowserCache(){for(const e of this.#kt)e.invoke_clearBrowserCache()}clearBrowserCookies(){for(const e of this.#kt)e.invoke_clearBrowserCookies()}async getCertificate(e){const t=je.instance().primaryPageTarget();if(!t)return[];const r=await t.networkAgent().invoke_getCertificate({origin:e});return r?r.tableNames:[]}async loadResource(t){const r={},n=this.currentUserAgent();n&&(r["User-Agent"]=n),e.Settings.Settings.instance().moduleSetting("cache-disabled").get()&&(r["Cache-Control"]="no-cache");const s=e.Settings.Settings.instance().moduleSetting("network.enable-remote-file-loading").get();return new Promise((e=>o.ResourceLoader.load(t,r,((t,r,n,s)=>{e({success:t,content:n,errorDescription:s})}),s)))}}class Ct{#ct;#At;request;resourceType;responseStatusCode;responseHeaders;requestId;networkRequest;constructor(e,t,r,n,s,i,a){this.#ct=e,this.#At=!1,this.request=t,this.resourceType=r,this.responseStatusCode=i,this.responseHeaders=a,this.requestId=n,this.networkRequest=s}hasResponded(){return this.#At}static mergeSetCookieHeaders(e,t){const r=e=>{const t=new Map;for(const r of e){const e=r.value.match(/^([a-zA-Z0-9!#$%&'*+.^_`|~-]+=)(.*)$/);e?t.has(e[1])?t.get(e[1])?.push(r.value):t.set(e[1],[r.value]):t.has(r.value)?t.get(r.value)?.push(r.value):t.set(r.value,[r.value])}return t},n=r(e),s=r(t),i=[];for(const[e,t]of n)if(s.has(e))for(const t of s.get(e)||[])i.push({name:"set-cookie",value:t});else for(const e of t)i.push({name:"set-cookie",value:e});for(const[e,t]of s)if(!n.has(e))for(const e of t)i.push({name:"set-cookie",value:e});return i}async continueRequestWithContent(e,t,r,n){this.#At=!0;const s=t?await e.text():await async function(e){const t=new FileReader,r=new Promise((e=>{t.onloadend=e}));if(t.readAsDataURL(e),await r,t.error)return console.error("Could not convert blob to base64.",t.error),"";const n=t.result;if(null==n||"string"!=typeof n)return console.error("Could not convert blob to base64."),"";return n.substring(n.indexOf(",")+1)}(e),i=n?200:this.responseStatusCode||200;if(this.networkRequest){const e=this.networkRequest?.originalResponseHeaders.filter((e=>"set-cookie"===e.name))||[],t=r.filter((e=>"set-cookie"===e.name));this.networkRequest.setCookieHeaders=Ct.mergeSetCookieHeaders(e,t),this.networkRequest.hasOverriddenContent=n}this.#ct.invoke_fulfillRequest({requestId:this.requestId,responseCode:i,body:s,responseHeaders:r}),St.instance().dispatchEventToListeners("RequestFulfilled",this.request.url)}continueRequestWithoutChange(){console.assert(!this.#At),this.#At=!0,this.#ct.invoke_continueRequest({requestId:this.requestId})}continueRequestWithError(e){console.assert(!this.#At),this.#At=!0,this.#ct.invoke_failRequest({requestId:this.requestId,errorReason:e})}async responseBody(){const e=await this.#ct.invoke_getResponseBody({requestId:this.requestId}),t=e.getError();if(t)return{error:t};const{mimeType:r,charset:n}=this.getMimeTypeAndCharset();return new s.ContentData.ContentData(e.body,e.base64Encoded,r??"application/octet-stream",n??void 0)}isRedirect(){return void 0!==this.responseStatusCode&&this.responseStatusCode>=300&&this.responseStatusCode<400}getMimeTypeAndCharset(){for(const e of this.responseHeaders??[])if("content-type"===e.name.toLowerCase())return t.MimeType.parseContentType(e.value);return{mimeType:this.networkRequest?.mimeType??null,charset:this.networkRequest?.charset()??null}}}class Rt{#Et;#Ot;#Nt;#Dt;#Ft;#Bt;constructor(){this.#Et=[],this.#Ot=[],this.#Nt=[],this.#Dt=!1,this.#Ft=null,this.#Bt=null}addRequest(e){this.#Et.push(e),this.sync(this.#Et.length-1)}addRequestExtraInfo(e){this.#Ot.push(e),this.sync(this.#Ot.length-1)}addResponseExtraInfo(e){this.#Nt.push(e),this.sync(this.#Nt.length-1)}setWebBundleInfo(e){this.#Ft=e,this.updateFinalRequest()}setWebBundleInnerRequestInfo(e){this.#Bt=e,this.updateFinalRequest()}finished(){this.#Dt=!0,this.updateFinalRequest()}isFinished(){return this.#Dt}sync(e){const t=this.#Et[e];if(!t)return;const r=this.#Ot[e];r&&(t.addExtraRequestInfo(r),this.#Ot[e]=null);const n=this.#Nt[e];n&&(t.addExtraResponseInfo(n),this.#Nt[e]=null)}finalRequest(){return this.#Dt&&this.#Et[this.#Et.length-1]||null}updateFinalRequest(){if(!this.#Dt)return;const e=this.finalRequest();e?.setWebBundleInfo(this.#Ft),e?.setWebBundleInnerRequestInfo(this.#Bt)}}c.register(gt,{capabilities:16,autostart:!0});var xt=Object.freeze({__proto__:null,NetworkManager:gt,get Events(){return pt},NoThrottlingConditions:mt,OfflineConditions:ft,Slow3GConditions:bt,Fast3GConditions:yt,FetchDispatcher:It,NetworkDispatcher:kt,MultitargetNetworkManager:St,InterceptedRequest:Ct,ConditionsSerializer:class{stringify(e){const t=e;return JSON.stringify({...t,title:"function"==typeof t.title?t.title():t.title})}parse(e){const t=JSON.parse(e);return{...t,title:t.i18nTitleKey?ct(t.i18nTitleKey):t.title}}},networkConditionsEqual:function(e,t){const r="function"==typeof e.title?e.title():e.title,n="function"==typeof t.title?t.title():t.title;return t.download===e.download&&t.upload===e.upload&&t.latency===e.latency&&n===r}});const Tt={loadCanceledDueToReloadOf:"Load canceled due to reload of inspected page"},Mt=i.i18n.registerUIStrings("core/sdk/PageResourceLoader.ts",Tt),Pt=i.i18n.getLocalizedString.bind(void 0,Mt);function Lt(e){return"extensionId"in e}let At=null;class Et extends e.ObjectWrapper.ObjectWrapper{#Ut;#Ht;#qt;#_t;#zt;#jt;constructor(e,t){super(),this.#Ut=0,this.#Ht=new Map,this.#qt=t,this.#_t=new Map,this.#zt=[],je.instance().addModelListener(mn,gn.PrimaryPageChanged,this.onPrimaryPageChanged,this),this.#jt=e}static instance({forceNew:e,loadOverride:t,maxConcurrentLoads:r}={forceNew:!1,loadOverride:null,maxConcurrentLoads:500}){return At&&!e||(At=new Et(t,r)),At}static removeInstance(){At=null}onPrimaryPageChanged(e){const{frame:t,type:r}=e.data;if(!t.isOutermostFrame())return;for(const{reject:e}of this.#zt)e(new Error(Pt(Tt.loadCanceledDueToReloadOf)));this.#zt=[];const n=t.resourceTreeModel().target(),s=new Map;for(const[e,t]of this.#_t.entries())"Activation"===r&&n===t.initiator.target&&s.set(e,t);this.#_t=s,this.dispatchEventToListeners("Update")}getResourcesLoaded(){return this.#_t}getScopedResourcesLoaded(){return new Map([...this.#_t].filter((([e,t])=>je.instance().isInScope(t.initiator.target)||Lt(t.initiator))))}getNumberOfResources(){return{loading:this.#Ut,queued:this.#zt.length,resources:this.#_t.size}}getScopedNumberOfResources(){const e=je.instance();let t=0;for(const[r,n]of this.#Ht){const s=e.targetById(r);e.isInScope(s)&&(t+=n)}return{loading:t,resources:this.getScopedResourcesLoaded().size}}async acquireLoadSlot(e){if(this.#Ut++,e){const t=this.#Ht.get(e.id())||0;this.#Ht.set(e.id(),t+1)}if(this.#Ut>this.#qt){const e={resolve:()=>{},reject:()=>{}},t=new Promise(((t,r)=>{e.resolve=t,e.reject=r}));this.#zt.push(e),await t}}releaseLoadSlot(e){if(this.#Ut--,e){const t=this.#Ht.get(e.id());t&&this.#Ht.set(e.id(),t-1)}const t=this.#zt.shift();t&&t.resolve()}static makeExtensionKey(e,t){if(Lt(t)&&t.extensionId)return`${e}-${t.extensionId}`;throw new Error("Invalid initiator")}static makeKey(e,t){if(t.frameId)return`${e}-${t.frameId}`;if(t.target)return`${e}-${t.target.id()}`;throw new Error("Invalid initiator")}resourceLoadedThroughExtension(e){const t=Et.makeExtensionKey(e.url,e.initiator);this.#_t.set(t,e),this.dispatchEventToListeners("Update")}async loadResource(e,t){if(Lt(t))throw new Error("Invalid initiator");const r=Et.makeKey(e,t),n={success:null,size:null,errorMessage:void 0,url:e,initiator:t};this.#_t.set(r,n),this.dispatchEventToListeners("Update");try{await this.acquireLoadSlot(t.target);const r=this.dispatchLoad(e,t),s=await r;if(n.errorMessage=s.errorDescription.message,n.success=s.success,s.success)return n.size=s.content.length,{content:s.content};throw new Error(s.errorDescription.message)}catch(e){throw void 0===n.errorMessage&&(n.errorMessage=e.message),null===n.success&&(n.success=!1),e}finally{this.releaseLoadSlot(t.target),this.dispatchEventToListeners("Update")}}async dispatchLoad(t,r){if(Lt(r))throw new Error("Invalid initiator");let n=null;if(this.#jt)return this.#jt(t);const s=new e.ParsedURL.ParsedURL(t),i=Ot().get()&&s&&"file"!==s.scheme&&"data"!==s.scheme&&"devtools"!==s.scheme;if(o.userMetrics.developerResourceScheme(this.getDeveloperResourceScheme(s)),i){try{if(r.target){o.userMetrics.developerResourceLoaded(0),o.rnPerfMetrics.developerResourceLoadingStarted(s,0);const e=await this.loadFromTarget(r.target,r.frameId,t);return o.rnPerfMetrics.developerResourceLoadingFinished(s,0,e),e}const e=Ge.instance().getFrame(r.frameId);if(e){o.userMetrics.developerResourceLoaded(1),o.rnPerfMetrics.developerResourceLoadingStarted(s,1);const n=await this.loadFromTarget(e.resourceTreeModel().target(),r.frameId,t);return o.rnPerfMetrics.developerResourceLoadingFinished(s,0,n),n}}catch(e){e instanceof Error&&(o.userMetrics.developerResourceLoaded(2),n=e.message),o.rnPerfMetrics.developerResourceLoadingFinished(s,2,{success:!1,errorDescription:{message:n}})}o.userMetrics.developerResourceLoaded(3),o.rnPerfMetrics.developerResourceLoadingStarted(s,3)}else{const e=Ot().get()?6:5;o.userMetrics.developerResourceLoaded(e),o.rnPerfMetrics.developerResourceLoadingStarted(s,e)}const a=await St.instance().loadResource(t);return i&&!a.success&&o.userMetrics.developerResourceLoaded(7),n&&(a.errorDescription.message=`Fetch through target failed: ${n}; Fallback: ${a.errorDescription.message}`),o.rnPerfMetrics.developerResourceLoadingFinished(s,4,a),a}getDeveloperResourceScheme(e){if(!e||""===e.scheme)return 1;const t="localhost"===e.host||e.host.endsWith(".localhost");switch(e.scheme){case"file":return 7;case"data":return 6;case"blob":return 8;case"http":return t?4:2;case"https":return t?5:3}return 0}async loadFromTarget(t,r,n){const s=t.model(gt),i=t.model(it),a=e.Settings.Settings.instance().moduleSetting("cache-disabled").get(),l=await s.loadNetworkResource(r,n,{disableCache:a,includeCredentials:!0});try{const e=l.stream?await i.readToString(l.stream):"";return{success:l.success,content:e,errorDescription:{statusCode:l.httpStatusCode||0,netError:l.netError,netErrorName:l.netErrorName,message:o.ResourceLoader.netErrorToMessage(l.netError,l.httpStatusCode,l.netErrorName)||"",urlValid:void 0}}}finally{l.stream&&i.close(l.stream)}}}function Ot(){return e.Settings.Settings.instance().createSetting("load-through-target",!0)}var Nt=Object.freeze({__proto__:null,PageResourceLoader:Et,getLoadThroughTargetSetting:Ot});function Dt(e){return e.startsWith(")]}")&&(e=e.substring(e.indexOf("\n"))),65279===e.charCodeAt(0)&&(e=e.slice(1)),JSON.parse(e)}class Ft{lineNumber;columnNumber;sourceURL;sourceLineNumber;sourceColumnNumber;name;constructor(e,t,r,n,s,i){this.lineNumber=e,this.columnNumber=t,this.sourceURL=r,this.sourceLineNumber=n,this.sourceColumnNumber=s,this.name=i}static compare(e,t){return e.lineNumber!==t.lineNumber?e.lineNumber-t.lineNumber:e.columnNumber-t.columnNumber}}function Bt(e,t){return e.lineNumber-t.lineNumber||e.columnNumber-t.columnNumber}class Ut{startLineNumber;startColumnNumber;endLineNumber;endColumnNumber;name;children=[];constructor(e,t,r,n,s){this.startLineNumber=e,this.startColumnNumber=t,this.endLineNumber=r,this.endColumnNumber=n,this.name=s}scopeName(){return this.name}start(){return{lineNumber:this.startLineNumber,columnNumber:this.startColumnNumber}}end(){return{lineNumber:this.endLineNumber,columnNumber:this.endColumnNumber}}}const Ht=new WeakMap;class qt{#Vt;#Wt;#Gt;#Kt;#Qt;#$t;constructor(t,r,n){this.#Vt=n,this.#Wt=t,this.#Gt=r,this.#Kt=e.ParsedURL.schemeIs(r,"data:")?t:r,this.#Qt=null,this.#$t=new Map,"sections"in this.#Vt&&this.#Vt.sections.find((e=>"url"in e))&&e.Console.Console.instance().warn(`SourceMap "${r}" contains unsupported "URL" field in one of its sections.`),this.eachSection(this.parseSources.bind(this))}compiledURL(){return this.#Wt}url(){return this.#Gt}sourceURLs(){return[...this.#$t.keys()]}embeddedContentByURL(e){const t=this.#$t.get(e);return t?t.content:null}findEntry(e,r){const n=this.mappings(),s=t.ArrayUtilities.upperBound(n,void 0,((t,n)=>e-n.lineNumber||r-n.columnNumber));return s?n[s-1]:null}findEntryRanges(e,r){const n=this.mappings(),i=t.ArrayUtilities.upperBound(n,void 0,((t,n)=>e-n.lineNumber||r-n.columnNumber));if(!i)return null;const a=i-1,o=n[a].sourceURL;if(!o)return null;const l=i<n.length?n[i].lineNumber:2**31-1,d=i<n.length?n[i].columnNumber:2**31-1,c=new s.TextRange.TextRange(n[a].lineNumber,n[a].columnNumber,l,d),h=this.reversedMappings(o),u=n[a].sourceLineNumber,g=n[a].sourceColumnNumber,p=t.ArrayUtilities.upperBound(h,void 0,((e,t)=>u-n[t].sourceLineNumber||g-n[t].sourceColumnNumber));if(!p)return null;const m=p<h.length?n[h[p]].sourceLineNumber:2**31-1,f=p<h.length?n[h[p]].sourceColumnNumber:2**31-1;return{range:c,sourceRange:new s.TextRange.TextRange(u,g,m,f),sourceURL:o}}sourceLineMapping(e,r,n){const s=this.mappings(),i=this.reversedMappings(e),a=t.ArrayUtilities.lowerBound(i,r,c),o=t.ArrayUtilities.upperBound(i,r,c);if(a>=i.length||s[i[a]].sourceLineNumber!==r)return null;const l=i.slice(a,o);if(!l.length)return null;const d=t.ArrayUtilities.lowerBound(l,n,((e,t)=>e-s[t].sourceColumnNumber));return d>=l.length?s[l[l.length-1]]:s[l[d]];function c(e,t){return e-s[t].sourceLineNumber}}findReverseIndices(e,r,n){const s=this.mappings(),i=this.reversedMappings(e),a=t.ArrayUtilities.upperBound(i,void 0,((e,t)=>r-s[t].sourceLineNumber||n-s[t].sourceColumnNumber));let o=a;for(;o>0&&s[i[o-1]].sourceLineNumber===s[i[a-1]].sourceLineNumber&&s[i[o-1]].sourceColumnNumber===s[i[a-1]].sourceColumnNumber;)--o;return i.slice(o,a)}findReverseEntries(e,t,r){const n=this.mappings();return this.findReverseIndices(e,t,r).map((e=>n[e]))}findReverseRanges(e,t,r){const n=this.mappings(),i=this.findReverseIndices(e,t,r),a=[];for(let e=0;e<i.length;++e){const t=i[e];let r=t+1;for(;e+1<i.length&&r===i[e+1];)++r,++e;const o=n[t].lineNumber,l=n[t].columnNumber,d=r<n.length?n[r].lineNumber:2**31-1,c=r<n.length?n[r].columnNumber:2**31-1;a.push(new s.TextRange.TextRange(o,l,d,c))}return a}mappings(){return this.#Xt(),this.#Qt??[]}reversedMappings(e){return this.#Xt(),this.#$t.get(e)?.reverseMappings??[]}#Xt(){if(null===this.#Qt){this.#Qt=[];try{this.eachSection(this.parseMap.bind(this))}catch(e){console.error("Failed to parse source map",e),this.#Qt=[]}this.mappings().sort(Ft.compare),this.#Jt(this.#Qt),this.#Vt=null}}#Jt(e){const t=new Map;for(let r=0;r<e.length;r++){const n=e[r].sourceURL;if(!n)continue;let s=t.get(n);s||(s=[],t.set(n,s)),s.push(r)}for(const[e,n]of t.entries()){const t=this.#$t.get(e);t&&(n.sort(r),t.reverseMappings=n)}function r(t,r){const n=e[t],s=e[r];return n.sourceLineNumber-s.sourceLineNumber||n.sourceColumnNumber-s.sourceColumnNumber||n.lineNumber-s.lineNumber||n.columnNumber-s.columnNumber}}eachSection(e){if(this.#Vt)if("sections"in this.#Vt)for(const t of this.#Vt.sections)"map"in t&&e(t.map,t.offset.line,t.offset.column);else e(this.#Vt,0,0)}parseSources(t){const r=[],n=t.sourceRoot??"",s=new Set(t.ignoreList??t.x_google_ignoreList);for(let i=0;i<t.sources.length;++i){let a=t.sources[i];e.ParsedURL.ParsedURL.isRelativeURL(a)&&(a=n&&!n.endsWith("/")&&a&&!a.startsWith("/")?n.concat("/",a):n.concat(a));const o=e.ParsedURL.ParsedURL.completeURL(this.#Kt,a)||a,l=t.sourcesContent&&t.sourcesContent[i];if(r.push(o),!this.#$t.has(o)){const e=l??null,t=s.has(i);this.#$t.set(o,{content:e,ignoreListHint:t,reverseMappings:null,scopeTree:null})}}Ht.set(t,r)}parseMap(e,t,r){let n=0,s=0,i=0,o=0;const l=Ht.get(e),d=e.names??[],c=new _t(e.mappings);let h=l&&l[n];for(;;){if(","===c.peek())c.next();else{for(;";"===c.peek();)t+=1,r=0,c.next();if(!c.hasNext())break}if(r+=c.nextVLQ(),!c.hasNext()||this.isSeparator(c.peek())){this.mappings().push(new Ft(t,r));continue}const e=c.nextVLQ();e&&(n+=e,l&&(h=l[n])),s+=c.nextVLQ(),i+=c.nextVLQ(),c.hasNext()&&!this.isSeparator(c.peek())?(o+=c.nextVLQ(),this.mappings().push(new Ft(t,r,h,s,i,d[o]))):this.mappings().push(new Ft(t,r,h,s,i))}a.Runtime.experiments.isEnabled("use-source-map-scopes")&&this.parseScopes(e)}parseScopes(e){if(!e.x_com_bloomberg_sourcesFunctionMappings)return;const t=Ht.get(e);if(!t)return;const r=e.names??[],n=e.x_com_bloomberg_sourcesFunctionMappings;for(let e=0;e<t?.length;e++){if(!n[e]||!t[e])continue;const s=this.#$t.get(t[e]);if(!s)continue;const i=n[e];let a=0,o=0,l=0,d=0,c=0;const h=new _t(i),u=[];let g=!0;for(;h.hasNext();){if(g)g=!1;else{if(","!==h.peek())return;h.next()}a+=h.nextVLQ(),o+=h.nextVLQ(),l+=h.nextVLQ(),d+=h.nextVLQ(),c+=h.nextVLQ(),u.push(new Ut(o,l,d,c,r[a]??"<invalid>"))}s.scopeTree=this.buildScopeTree(u)}}buildScopeTree(e){const t=[];e.sort(((e,t)=>Bt(e.start(),t.start())));const r=[];for(const n of e){const e=n.start();for(;r.length>0;){if(!(Bt(r[r.length-1].end(),e)<0))break;r.pop()}r.length>0?r[r.length-1].children.push(n):t.push(n),r.push(n)}return t}findScopeEntry(e,t,r){const n=this.#$t.get(e);if(!n||!n.scopeTree)return null;const s={lineNumber:t,columnNumber:r};let i=null;for(;;){const e=(i?.children??n.scopeTree).find((e=>Bt(e.start(),s)<=0&&Bt(s,e.end())<=0));if(!e)return i;i=e}}isSeparator(e){return","===e||";"===e}reverseMapTextRanges(e,r){const n=this.reversedMappings(e),i=this.mappings();if(0===n.length)return[];let a=t.ArrayUtilities.lowerBound(n,r,(({startLine:e,startColumn:t},r)=>{const{sourceLineNumber:n,sourceColumnNumber:s}=i[r];return e-n||t-s}));for(;a===n.length||a>0&&(i[n[a]].sourceLineNumber>r.startLine||i[n[a]].sourceColumnNumber>r.startColumn);)a--;let o=a+1;for(;o<n.length;++o){const{sourceLineNumber:e,sourceColumnNumber:t}=i[n[o]];if(!(e<r.endLine||e===r.endLine&&t<r.endColumn))break}const l=[];for(let e=a;e<o;++e){const t=n[e],r=t+1,a=s.TextRange.TextRange.createUnboundedFromLocation(i[t].lineNumber,i[t].columnNumber);r<i.length&&(a.endLine=i[r].lineNumber,a.endColumn=i[r].columnNumber),l.push(a)}l.sort(s.TextRange.TextRange.comparator);let d=0;for(let e=1;e<l.length;++e)l[d].immediatelyPrecedes(l[e])?(l[d].endLine=l[e].endLine,l[d].endColumn=l[e].endColumn):l[++d]=l[e];return l.length=d+1,l}mapsOrigin(){const e=this.mappings();if(e.length>0){const t=e[0];return 0===t?.lineNumber||0===t.columnNumber}return!1}hasIgnoreListHint(e){return this.#$t.get(e)?.ignoreListHint??!1}findRanges(e,t){const r=this.mappings(),n=[];if(!r.length)return[];let i=null;0===r[0].lineNumber&&0===r[0].columnNumber||!t?.isStartMatching||(i=s.TextRange.TextRange.createUnboundedFromLocation(0,0),n.push(i));for(const{sourceURL:t,lineNumber:a,columnNumber:o}of r){const r=t&&e(t);i||!r?i&&!r&&(i.endLine=a,i.endColumn=o,i=null):(i=s.TextRange.TextRange.createUnboundedFromLocation(a,o),n.push(i))}return n}compatibleForURL(e,t){return this.embeddedContentByURL(e)===t.embeddedContentByURL(e)&&this.hasIgnoreListHint(e)===t.hasIgnoreListHint(e)}}class _t{#Yt;#Zt;constructor(e){this.#Yt=e,this.#Zt=0}next(){return this.#Yt.charAt(this.#Zt++)}nextCharCode(){return this.#Yt.charCodeAt(this.#Zt++)}peek(){return this.#Yt.charAt(this.#Zt)}hasNext(){return this.#Zt<this.#Yt.length}nextVLQ(){let t=0,r=0,n=32;for(;32&n;){if(!this.hasNext())throw new Error("Unexpected end of input while decodling VLQ number!");const s=this.nextCharCode();if(n=e.Base64.BASE64_CODES[s],65!==s&&0===n)throw new Error(`Unexpected char '${String.fromCharCode(s)}' encountered while decoding`);t+=(31&n)<<r,r+=5}const s=1&t;return t>>=1,s?-t:t}peekVLQ(){const e=this.#Zt;try{return this.nextVLQ()}catch{return null}finally{this.#Zt=e}}}var zt,jt=Object.freeze({__proto__:null,parseSourceMap:Dt,SourceMapEntry:Ft,SourceMap:qt,TokenIterator:_t});class Vt extends e.ObjectWrapper.ObjectWrapper{#er;#tr;#rr;#nr;#sr;constructor(e){super(),this.#er=e,this.#tr=!0,this.#sr=null,this.#rr=new Map,this.#nr=new Map,je.instance().addEventListener("InspectedURLChanged",this.inspectedURLChanged,this)}setEnabled(e){if(e===this.#tr)return;const t=[...this.#rr.entries()];for(const[e]of t)this.detachSourceMap(e);this.#tr=e;for(const[e,{relativeSourceURL:r,relativeSourceMapURL:n}]of t)this.attachSourceMap(e,r,n)}static getBaseUrl(e){for(;e&&e.type()!==Ue.Frame;)e=e.parentTarget();return e?.inspectedURL()??t.DevToolsPath.EmptyUrlString}static resolveRelativeSourceURL(t,r){return r=e.ParsedURL.ParsedURL.completeURL(Vt.getBaseUrl(t),r)??r}inspectedURLChanged(e){if(e.data!==this.#er)return;const t=[...this.#rr.entries()];for(const[e,{relativeSourceURL:r,relativeSourceMapURL:n}]of t)this.detachSourceMap(e),this.attachSourceMap(e,r,n)}sourceMapForClient(e){return this.#rr.get(e)?.sourceMap}sourceMapForClientPromise(e){const t=this.#rr.get(e);return t?t.sourceMapPromise:Promise.resolve(void 0)}clientForSourceMap(e){return this.#nr.get(e)}attachSourceMap(t,r,n){if(this.#rr.has(t))throw new Error("SourceMap is already attached or being attached to client");if(!n)return;let s={relativeSourceURL:r,relativeSourceMapURL:n,sourceMap:void 0,sourceMapPromise:Promise.resolve(void 0)};if(this.#tr){const i=Vt.resolveRelativeSourceURL(this.#er,r),a=e.ParsedURL.ParsedURL.completeURL(i,n);if(a)if(this.#sr&&console.error("Attaching source map may cancel previously attaching source map"),this.#sr=t,this.dispatchEventToListeners(zt.SourceMapWillAttach,{client:t}),this.#sr===t){this.#sr=null;const e=t.createPageResourceLoadInitiator();s.sourceMapPromise=async function(e,t){try{const{content:r}=await Et.instance().loadResource(e,t);return Dt(r)}catch(t){throw new Error(`Could not load content for ${e}: ${t.message}`,{cause:t})}}(a,e).then((e=>{const r=new qt(i,a,e);return this.#rr.get(t)===s&&(s.sourceMap=r,this.#nr.set(r,t),this.dispatchEventToListeners(zt.SourceMapAttached,{client:t,sourceMap:r})),r}),(()=>{this.#rr.get(t)===s&&this.dispatchEventToListeners(zt.SourceMapFailedToAttach,{client:t})}))}else this.#sr&&console.error("Cancelling source map attach because another source map is attaching"),s=null,this.dispatchEventToListeners(zt.SourceMapFailedToAttach,{client:t})}s&&this.#rr.set(t,s)}cancelAttachSourceMap(e){e===this.#sr?this.#sr=null:this.#sr?console.error("cancel attach source map requested but a different source map was being attached"):console.error("cancel attach source map requested but no source map was being attached")}detachSourceMap(e){const t=this.#rr.get(e);if(!t)return;if(this.#rr.delete(e),!this.#tr)return;const{sourceMap:r}=t;r?(this.#nr.delete(r),this.dispatchEventToListeners(zt.SourceMapDetached,{client:e,sourceMap:r})):this.dispatchEventToListeners(zt.SourceMapFailedToAttach,{client:e})}dispose(){je.instance().removeEventListener("InspectedURLChanged",this.inspectedURLChanged,this)}}!function(e){e.SourceMapWillAttach="SourceMapWillAttach",e.SourceMapFailedToAttach="SourceMapFailedToAttach",e.SourceMapAttached="SourceMapAttached",e.SourceMapDetached="SourceMapDetached"}(zt||(zt={}));var Wt,Gt=Object.freeze({__proto__:null,SourceMapManager:Vt,get Events(){return zt}});class Kt extends c{agent;#ir;#ar;#or;#lr;#dr;#cr;#hr;#ur;#gr;#pr;#mr;#fr;#br;#tr;#yr;#vr;constructor(t){super(t),this.#tr=!1,this.#pr=null,this.#mr=null,this.#ir=t.model(tn),this.#dr=new Vt(t),this.agent=t.cssAgent(),this.#cr=new Yt(this),this.#lr=t.model(mn),this.#lr&&this.#lr.addEventListener(gn.PrimaryPageChanged,this.onPrimaryPageChanged,this),t.registerCSSDispatcher(new Jt(this)),t.suspended()||this.enable(),this.#gr=new Map,this.#ur=new Map,this.#or=new Map,this.#yr=!1,this.#ar=new Map,this.#fr=null,this.#br=!1,this.#vr=!1,this.#hr=new e.Throttler.Throttler(tr),this.#dr.setEnabled(e.Settings.Settings.instance().moduleSetting("css-source-maps-enabled").get()),e.Settings.Settings.instance().moduleSetting("css-source-maps-enabled").addChangeListener((e=>this.#dr.setEnabled(e.data)))}headersForSourceURL(e){const t=[];for(const r of this.getStyleSheetIdsForURL(e)){const e=this.styleSheetHeaderForId(r);e&&t.push(e)}return t}createRawLocationsByURL(e,r,n=0){const s=this.headersForSourceURL(e);s.sort((function(e,t){return e.startLine-t.startLine||e.startColumn-t.startColumn||e.id.localeCompare(t.id)}));const i=t.ArrayUtilities.upperBound(s,void 0,((e,t)=>r-t.startLine||n-t.startColumn));if(!i)return[];const a=[],o=s[i-1];for(let e=i-1;e>=0&&s[e].startLine===o.startLine&&s[e].startColumn===o.startColumn;--e)s[e].containsLocation(r,n)&&a.push(new Xt(s[e],r,n));return a}sourceMapManager(){return this.#dr}static readableLayerName(e){return e||"<anonymous>"}static trimSourceURL(e){let t=e.lastIndexOf("/*# sourceURL=");if(-1===t&&(t=e.lastIndexOf("/*@ sourceURL="),-1===t))return e;const r=e.lastIndexOf("\n",t);if(-1===r)return e;const n=e.substr(r+1).split("\n",1)[0];return-1===n.search(/[\040\t]*\/\*[#@] sourceURL=[\040\t]*([^\s]*)[\040\t]*\*\/[\040\t]*$/)?e:e.substr(0,r)+e.substr(r+n.length+1)}domModel(){return this.#ir}async setStyleText(e,t,r,n){try{await this.ensureOriginalStyleSheetText(e);const{styles:s}=await this.agent.invoke_setStyleTexts({edits:[{styleSheetId:e,range:t.serializeToObject(),text:r}]});if(!s||1!==s.length)return!1;this.#ir.markUndoableState(!n);const i=new $t(e,t,r,s[0]);return this.fireStyleSheetChanged(e,i),!0}catch(e){return console.error(e),!1}}async setSelectorText(e,t,r){o.userMetrics.actionTaken(o.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{selectorList:n}=await this.agent.invoke_setRuleSelector({styleSheetId:e,range:t,selector:r});if(!n)return!1;this.#ir.markUndoableState();const s=new $t(e,t,r,n);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async setPropertyRulePropertyName(e,t,r){o.userMetrics.actionTaken(o.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{propertyName:n}=await this.agent.invoke_setPropertyRulePropertyName({styleSheetId:e,range:t,propertyName:r});if(!n)return!1;this.#ir.markUndoableState();const s=new $t(e,t,r,n);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async setKeyframeKey(e,t,r){o.userMetrics.actionTaken(o.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{keyText:n}=await this.agent.invoke_setKeyframeKey({styleSheetId:e,range:t,keyText:r});if(!n)return!1;this.#ir.markUndoableState();const s=new $t(e,t,r,n);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}startCoverage(){return this.#yr=!0,this.agent.invoke_startRuleUsageTracking()}async takeCoverageDelta(){const e=await this.agent.invoke_takeCoverageDelta();return{timestamp:e&&e.timestamp||0,coverage:e&&e.coverage||[]}}setLocalFontsEnabled(e){return this.agent.invoke_setLocalFontsEnabled({enabled:e})}async stopCoverage(){this.#yr=!1,await this.agent.invoke_stopRuleUsageTracking()}async getMediaQueries(){const{medias:e}=await this.agent.invoke_getMediaQueries();return e?J.parseMediaArrayPayload(this,e):[]}async getRootLayer(e){const{rootLayer:t}=await this.agent.invoke_getLayersForNode({nodeId:e});return t}isEnabled(){return this.#tr}async enable(){await this.agent.invoke_enable(),this.#tr=!0,this.#yr&&await this.startCoverage(),this.dispatchEventToListeners(Wt.ModelWasEnabled)}async getMatchedStyles(e){const t=await this.agent.invoke_getMatchedStylesForNode({nodeId:e});if(t.getError())return null;const r=this.#ir.nodeForId(e);return r?await Le.create({cssModel:this,node:r,inlinePayload:t.inlineStyle||null,attributesPayload:t.attributesStyle||null,matchedPayload:t.matchedCSSRules||[],pseudoPayload:t.pseudoElements||[],inheritedPayload:t.inherited||[],inheritedPseudoPayload:t.inheritedPseudoElements||[],animationsPayload:t.cssKeyframesRules||[],parentLayoutNodeId:t.parentLayoutNodeId,positionFallbackRules:t.cssPositionFallbackRules||[],propertyRules:t.cssPropertyRules??[],cssPropertyRegistrations:t.cssPropertyRegistrations??[],fontPaletteValuesRule:t.cssFontPaletteValuesRule}):null}async getClassNames(e){const{classNames:t}=await this.agent.invoke_collectClassNames({styleSheetId:e});return t||[]}async getComputedStyle(e){return this.isEnabled()||await this.enable(),this.#cr.computedStylePromise(e)}async getBackgroundColors(e){const t=await this.agent.invoke_getBackgroundColors({nodeId:e});return t.getError()?null:{backgroundColors:t.backgroundColors||null,computedFontSize:t.computedFontSize||"",computedFontWeight:t.computedFontWeight||""}}async getPlatformFonts(e){const{fonts:t}=await this.agent.invoke_getPlatformFontsForNode({nodeId:e});return t}allStyleSheets(){const e=[...this.#gr.values()];return e.sort((function(e,t){return e.sourceURL<t.sourceURL?-1:e.sourceURL>t.sourceURL?1:e.startLine-t.startLine||e.startColumn-t.startColumn})),e}async getInlineStyles(e){const t=await this.agent.invoke_getInlineStylesForNode({nodeId:e});if(t.getError()||!t.inlineStyle)return null;const r=new he(this,null,t.inlineStyle,de.Inline),n=t.attributesStyle?new he(this,null,t.attributesStyle,de.Attributes):null;return new Zt(r,n)}forcePseudoState(e,r,n){const s=e.marker(Qt)||[],i=s.includes(r);if(n){if(i)return!1;s.push(r),e.setMarker(Qt,s)}else{if(!i)return!1;t.ArrayUtilities.removeElement(s,r),s.length?e.setMarker(Qt,s):e.setMarker(Qt,null)}return void 0!==e.id&&(this.agent.invoke_forcePseudoState({nodeId:e.id,forcedPseudoClasses:s}),this.dispatchEventToListeners(Wt.PseudoStateForced,{node:e,pseudoClass:r,enable:n}),!0)}pseudoState(e){return e.marker(Qt)||[]}async setMediaText(e,t,r){o.userMetrics.actionTaken(o.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{media:n}=await this.agent.invoke_setMediaText({styleSheetId:e,range:t,text:r});if(!n)return!1;this.#ir.markUndoableState();const s=new $t(e,t,r,n);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async setContainerQueryText(e,t,r){o.userMetrics.actionTaken(o.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{containerQuery:n}=await this.agent.invoke_setContainerQueryText({styleSheetId:e,range:t,text:r});if(!n)return!1;this.#ir.markUndoableState();const s=new $t(e,t,r,n);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async setSupportsText(e,t,r){o.userMetrics.actionTaken(o.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{supports:n}=await this.agent.invoke_setSupportsText({styleSheetId:e,range:t,text:r});if(!n)return!1;this.#ir.markUndoableState();const s=new $t(e,t,r,n);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async setScopeText(e,t,r){o.userMetrics.actionTaken(o.UserMetrics.Action.StyleRuleEdited);try{await this.ensureOriginalStyleSheetText(e);const{scope:n}=await this.agent.invoke_setScopeText({styleSheetId:e,range:t,text:r});if(!n)return!1;this.#ir.markUndoableState();const s=new $t(e,t,r,n);return this.fireStyleSheetChanged(e,s),!0}catch(e){return console.error(e),!1}}async addRule(e,t,r){try{await this.ensureOriginalStyleSheetText(e);const{rule:n}=await this.agent.invoke_addRule({styleSheetId:e,ruleText:t,location:r});if(!n)return null;this.#ir.markUndoableState();const s=new $t(e,r,t,n);return this.fireStyleSheetChanged(e,s),new be(this,n)}catch(e){return console.error(e),null}}async requestViaInspectorStylesheet(e){const t=e.frameId()||(this.#lr&&this.#lr.mainFrame?this.#lr.mainFrame.id:null),r=[...this.#gr.values()].find((e=>e.frameId===t&&e.isViaInspector()));if(r)return r;if(!t)return null;try{const{styleSheetId:e}=await this.agent.invoke_createStyleSheet({frameId:t});return e&&this.#gr.get(e)||null}catch(e){return console.error(e),null}}mediaQueryResultChanged(){this.dispatchEventToListeners(Wt.MediaQueryResultChanged)}fontsUpdated(e){e&&this.#ar.set(e.src,new U(e)),this.dispatchEventToListeners(Wt.FontsUpdated)}fontFaces(){return[...this.#ar.values()]}fontFaceForSource(e){return this.#ar.get(e)}styleSheetHeaderForId(e){return this.#gr.get(e)||null}styleSheetHeaders(){return[...this.#gr.values()]}fireStyleSheetChanged(e,t){this.dispatchEventToListeners(Wt.StyleSheetChanged,{styleSheetId:e,edit:t})}ensureOriginalStyleSheetText(e){const t=this.styleSheetHeaderForId(e);if(!t)return Promise.resolve(null);let r=this.#or.get(t);return r||(r=this.getStyleSheetText(t.id),this.#or.set(t,r),this.originalContentRequestedForTest(t)),r}originalContentRequestedForTest(e){}originalStyleSheetText(e){return this.ensureOriginalStyleSheetText(e.id)}getAllStyleSheetHeaders(){return this.#gr.values()}styleSheetAdded(e){console.assert(!this.#gr.get(e.styleSheetId)),e.loadingFailed&&(e.hasSourceURL=!1,e.isConstructed=!0,e.isInline=!1,e.isMutable=!1,e.sourceURL="",e.sourceMapURL=void 0);const t=new Be(this,e);this.#gr.set(e.styleSheetId,t);const r=t.resourceURL();let n=this.#ur.get(r);if(n||(n=new Map,this.#ur.set(r,n)),n){let e=n.get(t.frameId);e||(e=new Set,n.set(t.frameId,e)),e.add(t.id)}this.#dr.attachSourceMap(t,t.sourceURL,t.sourceMapURL),this.dispatchEventToListeners(Wt.StyleSheetAdded,t)}styleSheetRemoved(e){const t=this.#gr.get(e);if(console.assert(Boolean(t)),!t)return;this.#gr.delete(e);const r=t.resourceURL(),n=this.#ur.get(r);if(console.assert(Boolean(n),"No frameId to styleSheetId map is available for given style sheet URL."),n){const s=n.get(t.frameId);s&&(s.delete(e),s.size||(n.delete(t.frameId),n.size||this.#ur.delete(r)))}this.#or.delete(t),this.#dr.detachSourceMap(t),this.dispatchEventToListeners(Wt.StyleSheetRemoved,t)}getStyleSheetIdsForURL(e){const t=this.#ur.get(e);if(!t)return[];const r=[];for(const e of t.values())r.push(...e);return r}async setStyleSheetText(e,t,r){const n=this.#gr.get(e);if(!n)return"Unknown stylesheet in CSS.setStyleSheetText";t=Kt.trimSourceURL(t),n.hasSourceURL&&(t+="\n/*# sourceURL="+n.sourceURL+" */"),await this.ensureOriginalStyleSheetText(e);const s=(await this.agent.invoke_setStyleSheetText({styleSheetId:n.id,text:t})).sourceMapURL;return this.#dr.detachSourceMap(n),n.setSourceMapURL(s),this.#dr.attachSourceMap(n,n.sourceURL,n.sourceMapURL),null===s?"Error in CSS.setStyleSheetText":(this.#ir.markUndoableState(!r),this.fireStyleSheetChanged(e),null)}async getStyleSheetText(e){try{const{text:t}=await this.agent.invoke_getStyleSheetText({styleSheetId:e});return t&&Kt.trimSourceURL(t)}catch(e){return null}}async onPrimaryPageChanged(e){e.data.frame.backForwardCacheDetails.restoredFromCache?(await this.suspendModel(),await this.resumeModel()):"Activation"!==e.data.type&&(this.resetStyleSheets(),this.resetFontFaces())}resetStyleSheets(){const e=[...this.#gr.values()];this.#ur.clear(),this.#gr.clear();for(const t of e)this.#dr.detachSourceMap(t),this.dispatchEventToListeners(Wt.StyleSheetRemoved,t)}resetFontFaces(){this.#ar.clear()}async suspendModel(){this.#tr=!1,await this.agent.invoke_disable(),this.resetStyleSheets(),this.resetFontFaces()}async resumeModel(){return this.enable()}setEffectivePropertyValueForNode(e,t,r){this.agent.invoke_setEffectivePropertyValueForNode({nodeId:e,propertyName:t,value:r})}cachedMatchedCascadeForNode(e){if(this.#pr!==e&&this.discardCachedMatchedCascade(),this.#pr=e,!this.#mr){if(!e.id)return Promise.resolve(null);this.#mr=this.getMatchedStyles(e.id)}return this.#mr}discardCachedMatchedCascade(){this.#pr=null,this.#mr=null}createCSSPropertyTracker(e){return new er(this,e)}enableCSSPropertyTracker(e){const t=e.getTrackedProperties();0!==t.length&&(this.agent.invoke_trackComputedStyleUpdates({propertiesToTrack:t}),this.#br=!0,this.#fr=e,this.pollComputedStyleUpdates())}disableCSSPropertyTracker(){this.#br=!1,this.#fr=null,this.agent.invoke_trackComputedStyleUpdates({propertiesToTrack:[]})}async pollComputedStyleUpdates(){if(!this.#vr){if(this.#br){this.#vr=!0;const e=await this.agent.invoke_takeComputedStyleUpdates();if(this.#vr=!1,e.getError()||!e.nodeIds||!this.#br)return;this.#fr&&this.#fr.dispatchEventToListeners("TrackedCSSPropertiesUpdated",e.nodeIds.map((e=>this.#ir.nodeForId(e))))}this.#br&&this.#hr.schedule(this.pollComputedStyleUpdates.bind(this))}}dispose(){this.disableCSSPropertyTracker(),super.dispose(),this.#dr.dispose()}getAgent(){return this.agent}}!function(e){e.FontsUpdated="FontsUpdated",e.MediaQueryResultChanged="MediaQueryResultChanged",e.ModelWasEnabled="ModelWasEnabled",e.PseudoStateForced="PseudoStateForced",e.StyleSheetAdded="StyleSheetAdded",e.StyleSheetChanged="StyleSheetChanged",e.StyleSheetRemoved="StyleSheetRemoved"}(Wt||(Wt={}));const Qt="pseudo-state-marker";class $t{styleSheetId;oldRange;newRange;newText;payload;constructor(e,t,r,n){this.styleSheetId=e,this.oldRange=t,this.newRange=s.TextRange.TextRange.fromEdit(t,r),this.newText=r,this.payload=n}}class Xt{#O;styleSheetId;url;lineNumber;columnNumber;constructor(e,t,r){this.#O=e.cssModel(),this.styleSheetId=e.id,this.url=e.resourceURL(),this.lineNumber=t,this.columnNumber=r||0}cssModel(){return this.#O}header(){return this.#O.styleSheetHeaderForId(this.styleSheetId)}}class Jt{#J;constructor(e){this.#J=e}mediaQueryResultChanged(){this.#J.mediaQueryResultChanged()}fontsUpdated({font:e}){this.#J.fontsUpdated(e)}styleSheetChanged({styleSheetId:e}){this.#J.fireStyleSheetChanged(e)}styleSheetAdded({header:e}){this.#J.styleSheetAdded(e)}styleSheetRemoved({styleSheetId:e}){this.#J.styleSheetRemoved(e)}}class Yt{#J;#Ir;constructor(e){this.#J=e,this.#Ir=new Map}computedStylePromise(e){let t=this.#Ir.get(e);return t||(t=this.#J.getAgent().invoke_getComputedStyleForNode({nodeId:e}).then((({computedStyle:t})=>{if(this.#Ir.delete(e),!t||!t.length)return null;const r=new Map;for(const e of t)r.set(e.name,e.value);return r})),this.#Ir.set(e,t),t)}}class Zt{inlineStyle;attributesStyle;constructor(e,t){this.inlineStyle=e,this.attributesStyle=t}}class er extends e.ObjectWrapper.ObjectWrapper{#J;#kr;constructor(e,t){super(),this.#J=e,this.#kr=t}start(){this.#J.enableCSSPropertyTracker(this)}stop(){this.#J.disableCSSPropertyTracker()}getTrackedProperties(){return this.#kr}}const tr=1e3;c.register(Kt,{capabilities:2,autostart:!0});var rr=Object.freeze({__proto__:null,CSSModel:Kt,get Events(){return Wt},Edit:$t,CSSLocation:Xt,InlineStyleResult:Zt,CSSPropertyTracker:er});class nr extends c{#wr;#Sr;#Cr;#Rr;constructor(e){super(e),e.registerHeapProfilerDispatcher(new sr(this)),this.#wr=!1,this.#Sr=e.heapProfilerAgent(),this.#Cr=e.model(ar),this.#Rr=0}debuggerModel(){return this.#Cr.debuggerModel()}runtimeModel(){return this.#Cr}async enable(){this.#wr||(this.#wr=!0,await this.#Sr.invoke_enable())}async startSampling(e){if(this.#Rr++)return!1;const t=await this.#Sr.invoke_startSampling({samplingInterval:e||16384});return Boolean(t.getError())}async stopSampling(){if(!this.#Rr)throw new Error("Sampling profiler is not running.");if(--this.#Rr)return this.getSamplingProfile();const e=await this.#Sr.invoke_stopSampling();return e.getError()?null:e.profile}async getSamplingProfile(){const e=await this.#Sr.invoke_getSamplingProfile();return e.getError()?null:e.profile}async collectGarbage(){const e=await this.#Sr.invoke_collectGarbage();return Boolean(e.getError())}async snapshotObjectIdForObjectId(e){const t=await this.#Sr.invoke_getHeapObjectId({objectId:e});return t.getError()?null:t.heapSnapshotObjectId}async objectForSnapshotObjectId(e,t){const r=await this.#Sr.invoke_getObjectByHeapObjectId({objectId:e,objectGroup:t});return r.getError()?null:this.#Cr.createRemoteObject(r.result)}async addInspectedHeapObject(e){const t=await this.#Sr.invoke_addInspectedHeapObject({heapObjectId:e});return Boolean(t.getError())}async takeHeapSnapshot(e){const t=await this.#Sr.invoke_takeHeapSnapshot(e);return Boolean(t.getError())}async startTrackingHeapObjects(e){const t=await this.#Sr.invoke_startTrackingHeapObjects({trackAllocations:e});return Boolean(t.getError())}async stopTrackingHeapObjects(e){const t=await this.#Sr.invoke_stopTrackingHeapObjects({reportProgress:e});return Boolean(t.getError())}heapStatsUpdate(e){this.dispatchEventToListeners("HeapStatsUpdate",e)}lastSeenObjectId(e,t){this.dispatchEventToListeners("LastSeenObjectId",{lastSeenObjectId:e,timestamp:t})}addHeapSnapshotChunk(e){this.dispatchEventToListeners("AddHeapSnapshotChunk",e)}reportHeapSnapshotProgress(e,t,r){this.dispatchEventToListeners("ReportHeapSnapshotProgress",{done:e,total:t,finished:r})}resetProfiles(){this.dispatchEventToListeners("ResetProfiles",this)}}class sr{#xr;constructor(e){this.#xr=e}heapStatsUpdate({statsUpdate:e}){this.#xr.heapStatsUpdate(e)}lastSeenObjectId({lastSeenObjectId:e,timestamp:t}){this.#xr.lastSeenObjectId(e,t)}addHeapSnapshotChunk({chunk:e}){this.#xr.addHeapSnapshotChunk(e)}reportHeapSnapshotProgress({done:e,total:t,finished:r}){this.#xr.reportHeapSnapshotProgress(e,t,r)}resetProfiles(){this.#xr.resetProfiles()}}c.register(nr,{capabilities:4,autostart:!1});var ir=Object.freeze({__proto__:null,HeapProfilerModel:nr});class ar extends c{agent;#Tr;#Mr;#Pr;constructor(t){super(t),this.agent=t.runtimeAgent(),this.target().registerRuntimeDispatcher(new dr(this)),this.agent.invoke_enable(),this.#Tr=new Map,this.#Mr=cr.comparator,this.#Pr=null,e.Settings.Settings.instance().moduleSetting("custom-formatters").get()&&this.agent.invoke_setCustomObjectFormatterEnabled({enabled:!0}),e.Settings.Settings.instance().moduleSetting("custom-formatters").addChangeListener(this.customFormattersStateChanged.bind(this))}static isSideEffectFailure(e){const t="exceptionDetails"in e&&e.exceptionDetails;return Boolean(t&&t.exception&&t.exception.description&&t.exception.description.startsWith("EvalError: Possible side-effect in debug-evaluate"))}debuggerModel(){return this.target().model(Cr)}heapProfilerModel(){return this.target().model(nr)}executionContexts(){return[...this.#Tr.values()].sort(this.executionContextComparator())}setExecutionContextComparator(e){this.#Mr=e}executionContextComparator(){return this.#Mr}defaultExecutionContext(){for(const e of this.executionContexts())if(e.isDefault)return e;return null}executionContext(e){return this.#Tr.get(e)||null}executionContextCreated(e){const t=e.auxData||{isDefault:!0},r=new cr(this,e.id,e.uniqueId,e.name,e.origin,t.isDefault,t.frameId);this.#Tr.set(r.id,r),this.dispatchEventToListeners(lr.ExecutionContextCreated,r)}executionContextDestroyed(e){const t=this.#Tr.get(e);t&&(this.debuggerModel().executionContextDestroyed(t),this.#Tr.delete(e),this.dispatchEventToListeners(lr.ExecutionContextDestroyed,t))}fireExecutionContextOrderChanged(){this.dispatchEventToListeners(lr.ExecutionContextOrderChanged,this)}executionContextsCleared(){this.debuggerModel().globalObjectCleared();const e=this.executionContexts();this.#Tr.clear();for(let t=0;t<e.length;++t)this.dispatchEventToListeners(lr.ExecutionContextDestroyed,e[t])}createRemoteObject(e){return console.assert("object"==typeof e,"Remote object payload should only be an object"),new $e(this,e.objectId,e.type,e.subtype,e.value,e.unserializableValue,e.description,e.preview,e.customPreview,e.className)}createScopeRemoteObject(e,t){return new Xe(this,e.objectId,t,e.type,e.subtype,e.value,e.unserializableValue,e.description,e.preview)}createRemoteObjectFromPrimitiveValue(e){const t=typeof e;let r;const n=Qe.unserializableDescription(e);return null!==n&&(r=n),void 0!==r&&(e=void 0),new $e(this,void 0,t,void 0,e,r)}createRemotePropertyFromPrimitiveValue(e,t){return new Ye(e,this.createRemoteObjectFromPrimitiveValue(t))}discardConsoleEntries(){this.agent.invoke_discardConsoleEntries()}releaseObjectGroup(e){this.agent.invoke_releaseObjectGroup({objectGroup:e})}releaseEvaluationResult(e){if("object"in e&&e.object&&e.object.release(),"exceptionDetails"in e&&e.exceptionDetails&&e.exceptionDetails.exception){const t=e.exceptionDetails.exception;this.createRemoteObject({type:t.type,objectId:t.objectId}).release()}}runIfWaitingForDebugger(){this.agent.invoke_runIfWaitingForDebugger()}customFormattersStateChanged({data:e}){this.agent.invoke_setCustomObjectFormatterEnabled({enabled:e})}async compileScript(e,t,r,n){const s=await this.agent.invoke_compileScript({expression:e,sourceURL:t,persistScript:r,executionContextId:n});return s.getError()?(console.error(s.getError()),null):{scriptId:s.scriptId,exceptionDetails:s.exceptionDetails}}async runScript(e,t,r,n,s,i,a,o){const l=await this.agent.invoke_runScript({scriptId:e,executionContextId:t,objectGroup:r,silent:n,includeCommandLineAPI:s,returnByValue:i,generatePreview:a,awaitPromise:o}),d=l.getError();return d?(console.error(d),{error:d}):{object:this.createRemoteObject(l.result),exceptionDetails:l.exceptionDetails}}async queryObjects(e){if(!e.objectId)return{error:"Prototype should be an Object."};const t=await this.agent.invoke_queryObjects({prototypeObjectId:e.objectId,objectGroup:"console"}),r=t.getError();return r?(console.error(r),{error:r}):{objects:this.createRemoteObject(t.objects)}}async isolateId(){const e=await this.agent.invoke_getIsolateId();return e.getError()||!e.id?this.target().id():e.id}async heapUsage(){const e=await this.agent.invoke_getHeapUsage();return e.getError()?null:e}inspectRequested(t,r,n){const s=this.createRemoteObject(t);r&&"copyToClipboard"in r&&Boolean(r.copyToClipboard)?this.copyRequested(s):r&&"queryObjects"in r&&r.queryObjects?this.queryObjectsRequested(s,n):s.isNode()?e.Revealer.reveal(s).then(s.release.bind(s)):"function"!==s.type?s.release():tt.objectAsFunction(s).targetFunctionDetails().then((function(t){if(s.release(),!t||!t.location)return;e.Revealer.reveal(t.location)}))}async addBinding(e){return await this.agent.invoke_addBinding(e)}async removeBinding(e){return await this.agent.invoke_removeBinding(e)}bindingCalled(e){this.dispatchEventToListeners(lr.BindingCalled,e)}copyRequested(t){if(!t.objectId)return void o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(t.unserializableValue()||t.value);const r=e.Settings.Settings.instance().moduleSetting("text-editor-indent").get();t.callFunctionJSON((function(e){const t=e.subtype,r=e.indent;if("node"===t)return this instanceof Element?this.outerHTML:void 0;if(t&&void 0===this)return String(t);try{return JSON.stringify(this,null,r)}catch(e){return String(this)}}),[{value:{subtype:t.subtype,indent:r}}]).then(o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText.bind(o.InspectorFrontendHost.InspectorFrontendHostInstance))}async queryObjectsRequested(t,r){const n=await this.queryObjects(t);t.release(),"error"in n?e.Console.Console.instance().error(n.error):this.dispatchEventToListeners(lr.QueryObjectRequested,{objects:n.objects,executionContextId:r})}static simpleTextFromException(e){let t=e.text;if(e.exception&&e.exception.description){let r=e.exception.description;-1!==r.indexOf("\n")&&(r=r.substring(0,r.indexOf("\n"))),t+=" "+r}return t}exceptionThrown(e,t){const r={timestamp:e,details:t};this.dispatchEventToListeners(lr.ExceptionThrown,r)}exceptionRevoked(e){this.dispatchEventToListeners(lr.ExceptionRevoked,e)}consoleAPICalled(e,t,r,n,s,i){const a={type:e,args:t,executionContextId:r,timestamp:n,stackTrace:s,context:i};this.dispatchEventToListeners(lr.ConsoleAPICalled,a)}executionContextIdForScriptId(e){const t=this.debuggerModel().scriptForId(e);return t?t.executionContextId:0}executionContextForStackTrace(e){let t=e;for(;t&&!t.callFrames.length;)t=t.parent||null;return t&&t.callFrames.length?this.executionContextIdForScriptId(t.callFrames[0].scriptId):0}hasSideEffectSupport(){return this.#Pr}async checkSideEffectSupport(){const e=this.executionContexts(),t=e[e.length-1];if(!t)return!1;const r=await this.agent.invoke_evaluate({expression:or,contextId:t.id,throwOnSideEffect:!0});return this.#Pr=!r.getError()&&ar.isSideEffectFailure(r),this.#Pr}terminateExecution(){return this.agent.invoke_terminateExecution()}async getExceptionDetails(e){const t=await this.agent.invoke_getExceptionDetails({errorObjectId:e});if(!t.getError())return t.exceptionDetails}}const or="(async function(){ await 1; })()";var lr;!function(e){e.BindingCalled="BindingCalled",e.ExecutionContextCreated="ExecutionContextCreated",e.ExecutionContextDestroyed="ExecutionContextDestroyed",e.ExecutionContextChanged="ExecutionContextChanged",e.ExecutionContextOrderChanged="ExecutionContextOrderChanged",e.ExceptionThrown="ExceptionThrown",e.ExceptionRevoked="ExceptionRevoked",e.ConsoleAPICalled="ConsoleAPICalled",e.QueryObjectRequested="QueryObjectRequested"}(lr||(lr={}));class dr{#Lr;constructor(e){this.#Lr=e}executionContextCreated({context:e}){this.#Lr.executionContextCreated(e)}executionContextDestroyed({executionContextId:e}){this.#Lr.executionContextDestroyed(e)}executionContextsCleared(){this.#Lr.executionContextsCleared()}exceptionThrown({timestamp:e,exceptionDetails:t}){this.#Lr.exceptionThrown(e,t)}exceptionRevoked({exceptionId:e}){this.#Lr.exceptionRevoked(e)}consoleAPICalled({type:e,args:t,executionContextId:r,timestamp:n,stackTrace:s,context:i}){this.#Lr.consoleAPICalled(e,t,r,n,s,i)}inspectRequested({object:e,hints:t,executionContextId:r}){this.#Lr.inspectRequested(e,t,r)}bindingCalled(e){this.#Lr.bindingCalled(e)}}class cr{id;uniqueId;name;#Ar;origin;isDefault;runtimeModel;debuggerModel;frameId;constructor(e,t,r,n,s,i,a){this.id=t,this.uniqueId=r,this.name=n,this.#Ar=null,this.origin=s,this.isDefault=i,this.runtimeModel=e,this.debuggerModel=e.debuggerModel(),this.frameId=a,this.setLabelInternal("")}target(){return this.runtimeModel.target()}static comparator(e,t){function r(e){return e.parentTarget()?.type()!==Ue.Frame?5:e.type()===Ue.Frame?4:e.type()===Ue.ServiceWorker?3:e.type()===Ue.Worker||e.type()===Ue.SharedWorker?2:1}function n(e){let t=e;const r=[];for(;t;)r.push(t),t=t.parentTarget();return r.reverse()}const s=n(e.target()),i=n(t.target());let a,o;for(let e=0;;e++)if(!s[e]||!i[e]||s[e]!==i[e]){a=s[e],o=i[e];break}if(!a&&o)return-1;if(!o&&a)return 1;if(a&&o){const e=r(a)-r(o);return e?-e:a.id().localeCompare(o.id())}return e.isDefault?-1:t.isDefault?1:e.name.localeCompare(t.name)}async evaluate(e,t,r){if(this.debuggerModel.selectedCallFrame())return this.debuggerModel.evaluateOnSelectedCallFrame(e);return!(Boolean(e.throwOnSideEffect)||void 0!==e.timeout)||this.runtimeModel.hasSideEffectSupport()||!1!==this.runtimeModel.hasSideEffectSupport()&&(await this.runtimeModel.checkSideEffectSupport(),this.runtimeModel.hasSideEffectSupport())?this.evaluateGlobal(e,t,r):{error:"Side-effect checks not supported by backend."}}globalObject(e,t){const r={expression:"this",objectGroup:e,includeCommandLineAPI:!1,silent:!0,returnByValue:!1,generatePreview:t};return this.evaluateGlobal(r,!1,!1)}async evaluateGlobal(e,t,r){e.expression||(e.expression="this");const n=await this.runtimeModel.agent.invoke_evaluate({expression:e.expression,objectGroup:e.objectGroup,includeCommandLineAPI:e.includeCommandLineAPI,silent:e.silent,returnByValue:e.returnByValue,generatePreview:e.generatePreview,userGesture:t,awaitPromise:r,throwOnSideEffect:e.throwOnSideEffect,timeout:e.timeout,disableBreaks:e.disableBreaks,replMode:e.replMode,allowUnsafeEvalBlockedByCSP:e.allowUnsafeEvalBlockedByCSP,...this.uniqueId?{uniqueContextId:this.uniqueId}:{contextId:this.id}}),s=n.getError();return s?(console.error(s),{error:s}):{object:this.runtimeModel.createRemoteObject(n.result),exceptionDetails:n.exceptionDetails}}async globalLexicalScopeNames(){const e=await this.runtimeModel.agent.invoke_globalLexicalScopeNames({executionContextId:this.id});return e.getError()?[]:e.names}label(){return this.#Ar}setLabel(e){this.setLabelInternal(e),this.runtimeModel.dispatchEventToListeners(lr.ExecutionContextChanged,this)}setLabelInternal(t){if(t)return void(this.#Ar=t);if(this.name)return void(this.#Ar=this.name);const r=e.ParsedURL.ParsedURL.fromString(this.origin);this.#Ar=r?r.lastPathComponentWithFragment():""}}c.register(ar,{capabilities:4,autostart:!0});var hr=Object.freeze({__proto__:null,RuntimeModel:ar,get Events(){return lr},ExecutionContext:cr});const ur={scriptRemovedOrDeleted:"Script removed or deleted.",unableToFetchScriptSource:"Unable to fetch script source."},gr=i.i18n.registerUIStrings("core/sdk/Script.ts",ur),pr=i.i18n.getLocalizedString.bind(void 0,gr);let mr=null;class fr{debuggerModel;scriptId;sourceURL;lineOffset;columnOffset;endLine;endColumn;executionContextId;hash;#Er;#Or;sourceMapURL;debugSymbols;hasSourceURL;contentLength;originStackTrace;#Nr;#Dr;#Fr;#Br;isModule;constructor(e,t,r,n,s,i,a,o,l,d,c,h,u,g,p,m,f,b,y,v){this.debuggerModel=e,this.scriptId=t,this.sourceURL=r,this.lineOffset=n,this.columnOffset=s,this.endLine=i,this.endColumn=a,this.isModule=p,this.executionContextId=o,this.hash=l,this.#Er=d,this.#Or=c,this.sourceMapURL=h,this.debugSymbols=y,this.hasSourceURL=u,this.contentLength=g,this.originStackTrace=m,this.#Nr=f,this.#Dr=b,this.#Fr=null,this.#Br=v}embedderName(){return this.#Br}target(){return this.debuggerModel.target()}static trimSourceURLComment(e){let t=e.lastIndexOf("//# sourceURL=");if(-1===t&&(t=e.lastIndexOf("//@ sourceURL="),-1===t))return e;const r=e.lastIndexOf("\n",t);if(-1===r)return e;return e.substr(r+1).match(yr)?e.substr(0,r):e}isContentScript(){return this.#Er}codeOffset(){return this.#Nr}isJavaScript(){return"JavaScript"===this.#Dr}isWasm(){return"WebAssembly"===this.#Dr}scriptLanguage(){return this.#Dr}executionContext(){return this.debuggerModel.runtimeModel().executionContext(this.executionContextId)}isLiveEdit(){return this.#Or}contentURL(){return this.sourceURL}contentType(){return e.ResourceType.resourceTypes.Script}async loadTextContent(){const t=await this.debuggerModel.target().debuggerAgent().invoke_getScriptSource({scriptId:this.scriptId});if(t.getError())throw new Error(t.getError());const{scriptSource:r,bytecode:n}=t;if(n)return{content:n,isEncoded:!0};let s=r||"";return this.hasSourceURL&&e.ParsedURL.schemeIs(this.sourceURL,"snippet:")&&(s=fr.trimSourceURLComment(s)),{content:s,isEncoded:!1}}async loadWasmContent(){if(!this.isWasm())throw new Error("Not a wasm script");const t=await this.debuggerModel.target().debuggerAgent().invoke_disassembleWasmModule({scriptId:this.scriptId});if(t.getError())return this.loadTextContent();const{streamId:r,functionBodyOffsets:n,chunk:{lines:s,bytecodeOffsets:i}}=t,a=[],o=[];let l=s.reduce(((e,t)=>e+t.length+1),0);const d="<truncated>";if(r)for(;;){const e=await this.debuggerModel.target().debuggerAgent().invoke_nextWasmDisassemblyChunk({streamId:r});if(e.getError())throw new Error(e.getError());const{chunk:{lines:t,bytecodeOffsets:n}}=e;if(l+=t.reduce(((e,t)=>e+t.length+1),0),0===t.length)break;if(l>=999999989){a.push([d]),o.push([0]);break}a.push(t),o.push(n)}const c=[];for(let e=0;e<n.length;e+=2)c.push({start:n[e],end:n[e+1]});return{content:"",isEncoded:!1,wasmDisassemblyInfo:new e.WasmDisassembly.WasmDisassembly(s.concat(...a),i.concat(...o),c)}}requestContent(){if(!this.#Fr){const e=65535;if(this.hash&&!this.#Or&&this.contentLength>e){mr||(mr={cache:new Map,registry:new FinalizationRegistry((e=>mr?.cache.delete(e)))});const e=[this.#Dr,this.contentLength,this.lineOffset,this.columnOffset,this.endLine,this.endColumn,this.#Nr,this.hash].join(":"),t=mr.cache.get(e)?.deref();t?this.#Fr=t:(this.#Fr=this.requestContentInternal(),mr.cache.set(e,new WeakRef(this.#Fr)),mr.registry.register(this.#Fr,e))}else this.#Fr=this.requestContentInternal()}return this.#Fr}async requestContentInternal(){if(!this.scriptId)return{content:null,error:pr(ur.scriptRemovedOrDeleted),isEncoded:!1};try{return this.isWasm()?await this.loadWasmContent():await this.loadTextContent()}catch(e){return{content:null,error:pr(ur.unableToFetchScriptSource),isEncoded:!1}}}async getWasmBytecode(){const e=await this.debuggerModel.target().debuggerAgent().invoke_getWasmBytecode({scriptId:this.scriptId});return(await fetch(`data:application/wasm;base64,${e.bytecode}`)).arrayBuffer()}originalContentProvider(){return new s.StaticContentProvider.StaticContentProvider(this.contentURL(),this.contentType(),(()=>this.requestContent()))}async searchInContent(e,t,r){if(!this.scriptId)return[];const n=await this.debuggerModel.target().debuggerAgent().invoke_searchInContent({scriptId:this.scriptId,query:e,caseSensitive:t,isRegex:r});return s.TextUtils.performSearchInSearchMatches(n.result||[],e,t,r)}appendSourceURLCommentIfNeeded(e){return this.hasSourceURL?e+"\n //# sourceURL="+this.sourceURL:e}async editSource(e){e=fr.trimSourceURLComment(e),e=this.appendSourceURLCommentIfNeeded(e);const{content:t}=await this.requestContent();if(t===e)return{changed:!1,status:"Ok"};const r=await this.debuggerModel.target().debuggerAgent().invoke_setScriptSource({scriptId:this.scriptId,scriptSource:e,allowTopFrameEditing:!0});if(r.getError())throw new Error(`Script#editSource failed for script with id ${this.scriptId}: ${r.getError()}`);return r.getError()||"Ok"!==r.status||(this.#Fr=Promise.resolve({content:e,isEncoded:!1})),this.debuggerModel.dispatchEventToListeners(Tr.ScriptSourceWasEdited,{script:this,status:r.status}),{changed:!0,status:r.status,exceptionDetails:r.exceptionDetails}}rawLocation(e,t){return this.containsLocation(e,t)?new Pr(this.debuggerModel,this.scriptId,e,t):null}isInlineScript(){const e=!this.lineOffset&&!this.columnOffset;return!this.isWasm()&&Boolean(this.sourceURL)&&!e}isAnonymousScript(){return!this.sourceURL}async setBlackboxedRanges(e){return!(await this.debuggerModel.target().debuggerAgent().invoke_setBlackboxedRanges({scriptId:this.scriptId,positions:e})).getError()}containsLocation(e,t){const r=e===this.lineOffset&&t>=this.columnOffset||e>this.lineOffset,n=e<this.endLine||e===this.endLine&&t<=this.endColumn;return r&&n}get frameId(){return"string"!=typeof this[br]&&(this[br]=function(e){const t=e.executionContext();if(t)return t.frameId||null;const r=e.debuggerModel.target().model(mn);if(!r||!r.mainFrame)return null;return r.mainFrame.id}(this)),this[br]}get isBreakpointCondition(){return[Dr,Nr].includes(this.sourceURL)}createPageResourceLoadInitiator(){return{target:this.target(),frameId:this.frameId,initiatorUrl:this.embedderName()}}rawLocationToRelativeLocation(e){let{lineNumber:t,columnNumber:r}=e;return!this.hasSourceURL&&this.isInlineScript()&&(t-=this.lineOffset,0===t&&void 0!==r&&(r-=this.columnOffset)),{lineNumber:t,columnNumber:r}}relativeLocationToRawLocation(e){let{lineNumber:t,columnNumber:r}=e;return!this.hasSourceURL&&this.isInlineScript()&&(0===t&&void 0!==r&&(r+=this.columnOffset),t+=this.lineOffset),{lineNumber:t,columnNumber:r}}}const br=Symbol("frameid");const yr=/^[\040\t]*\/\/[@#] sourceURL=\s*(\S*?)\s*$/;var vr=Object.freeze({__proto__:null,Script:fr,sourceURLRegex:yr});const Ir={local:"Local",closure:"Closure",block:"Block",script:"Script",withBlock:"`With` block",catchBlock:"`Catch` block",global:"Global",module:"Module",expression:"Expression"},kr=i.i18n.registerUIStrings("core/sdk/DebuggerModel.ts",Ir),wr=i.i18n.getLocalizedString.bind(void 0,kr);function Sr(e){function t(e,t){return e.lineNumber-t.lineNumber||e.columnNumber-t.columnNumber}function r(e,r){if(e.scriptId!==r.scriptId)return!1;const n=t(e.start,r.start);return n<0?t(e.end,r.start)>=0:!(n>0)||t(e.start,r.end)<=0}if(0===e.length)return[];e.sort(((e,r)=>e.scriptId<r.scriptId?-1:e.scriptId>r.scriptId?1:t(e.start,r.start)||t(e.end,r.end)));let n=e[0];const s=[];for(let i=1;i<e.length;++i){const a=e[i];r(n,a)?t(n.end,a.end)<=0&&(n={...n,end:a.end}):(s.push(n),n=a)}return s.push(n),s}class Cr extends c{agent;runtimeModelInternal;#Ur;#Hr;#qr;#_r;#zr;continueToLocationCallback;#jr;#Vr;#Wr;#Gr;#Kr;#Qr;#$r;evaluateOnCallFrameCallback;#Xr;#Jr=new e.ObjectWrapper.ObjectWrapper;#Yr;#Zr;constructor(t){super(t),t.registerDebuggerDispatcher(new Mr(this)),this.agent=t.debuggerAgent(),this.runtimeModelInternal=t.model(ar),this.#Ur=new Vt(t),this.#Hr=null,this.#qr=new Map,this.#_r=new Map,this.#zr=[],this.continueToLocationCallback=null,this.#jr=null,this.#Vr=!1,this.#Wr=null,this.#Gr=0,this.#Kr=null,this.#Qr=null,this.#$r=null,this.evaluateOnCallFrameCallback=null,this.#Xr=null,this.#Yr=null,this.#Zr=!1,e.Settings.Settings.instance().moduleSetting("pause-on-exception-enabled").addChangeListener(this.pauseOnExceptionStateChanged,this),e.Settings.Settings.instance().moduleSetting("pause-on-caught-exception").addChangeListener(this.pauseOnExceptionStateChanged,this),e.Settings.Settings.instance().moduleSetting("pause-on-uncaught-exception").addChangeListener(this.pauseOnExceptionStateChanged,this),e.Settings.Settings.instance().moduleSetting("disable-async-stack-traces").addChangeListener(this.asyncStackTracesStateChanged,this),e.Settings.Settings.instance().moduleSetting("breakpoints-active").addChangeListener(this.breakpointsActiveChanged,this),t.suspended()||this.enableDebugger(),this.#Ur.setEnabled(e.Settings.Settings.instance().moduleSetting("js-source-maps-enabled").get()),e.Settings.Settings.instance().moduleSetting("js-source-maps-enabled").addChangeListener((e=>this.#Ur.setEnabled(e.data)));const r=t.model(mn);r&&r.addEventListener(gn.FrameNavigated,this.onFrameNavigated,this)}sourceMapManager(){return this.#Ur}runtimeModel(){return this.runtimeModelInternal}debuggerEnabled(){return Boolean(this.#Vr)}debuggerId(){return this.#Wr}async enableDebugger(){if(this.#Vr)return;this.#Vr=!0;const t=a.Runtime.Runtime.queryParam("remoteFrontend")||a.Runtime.Runtime.queryParam("ws")?1e7:1e8,r=this.agent.invoke_enable({maxScriptsCacheSize:t});let n;a.Runtime.experiments.isEnabled("instrumentation-breakpoints")&&(n=this.agent.invoke_setInstrumentationBreakpoint({instrumentation:"beforeScriptExecution"})),this.pauseOnExceptionStateChanged(),this.asyncStackTracesStateChanged(),e.Settings.Settings.instance().moduleSetting("breakpoints-active").get()||this.breakpointsActiveChanged(),this.dispatchEventToListeners(Tr.DebuggerWasEnabled,this);const[s]=await Promise.all([r,n]);this.registerDebugger(s)}async syncDebuggerId(){const e=a.Runtime.Runtime.queryParam("remoteFrontend")||a.Runtime.Runtime.queryParam("ws")?1e7:1e8,t=this.agent.invoke_enable({maxScriptsCacheSize:e});return t.then(this.registerDebugger.bind(this)),t}onFrameNavigated(){Cr.shouldResyncDebuggerId||(Cr.shouldResyncDebuggerId=!0)}registerDebugger(e){if(e.getError())return;const{debuggerId:t}=e;Rr.set(t,this),this.#Wr=t,this.dispatchEventToListeners(Tr.DebuggerIsReadyToPause,this)}isReadyToPause(){return Boolean(this.#Wr)}static async modelForDebuggerId(e){return Cr.shouldResyncDebuggerId&&(await Cr.resyncDebuggerIdForModels(),Cr.shouldResyncDebuggerId=!1),Rr.get(e)||null}static async resyncDebuggerIdForModels(){const e=Rr.values();for(const t of e)t.debuggerEnabled()&&await t.syncDebuggerId()}async disableDebugger(){this.#Vr&&(this.#Vr=!1,await this.asyncStackTracesStateChanged(),await this.agent.invoke_disable(),this.#Zr=!1,this.globalObjectCleared(),this.dispatchEventToListeners(Tr.DebuggerWasDisabled,this),"string"==typeof this.#Wr&&Rr.delete(this.#Wr),this.#Wr=null)}skipAllPauses(e){this.#Gr&&(clearTimeout(this.#Gr),this.#Gr=0),this.agent.invoke_setSkipAllPauses({skip:e})}skipAllPausesUntilReloadOrTimeout(e){this.#Gr&&clearTimeout(this.#Gr),this.agent.invoke_setSkipAllPauses({skip:!0}),this.#Gr=window.setTimeout(this.skipAllPauses.bind(this,!1),e)}pauseOnExceptionStateChanged(){const t=e.Settings.Settings.instance().moduleSetting("pause-on-caught-exception").get();let r;const n=e.Settings.Settings.instance().moduleSetting("pause-on-uncaught-exception").get();r=t&&n?"all":t?"caught":n?"uncaught":"none",this.agent.invoke_setPauseOnExceptions({state:r})}asyncStackTracesStateChanged(){const t=!e.Settings.Settings.instance().moduleSetting("disable-async-stack-traces").get()&&this.#Vr?32:0;return this.agent.invoke_setAsyncCallStackDepth({maxDepth:t})}breakpointsActiveChanged(){this.agent.invoke_setBreakpointsActive({active:e.Settings.Settings.instance().moduleSetting("breakpoints-active").get()})}setComputeAutoStepRangesCallback(e){this.#Qr=e}async computeAutoStepSkipList(e){let t=[];if(this.#Qr&&this.#Hr&&this.#Hr.callFrames.length>0){const[r]=this.#Hr.callFrames;t=await this.#Qr.call(null,e,r)}return Sr(t.map((({start:e,end:t})=>({scriptId:e.scriptId,start:{lineNumber:e.lineNumber,columnNumber:e.columnNumber},end:{lineNumber:t.lineNumber,columnNumber:t.columnNumber}}))))}async stepInto(){const e=await this.computeAutoStepSkipList("StepInto");this.agent.invoke_stepInto({breakOnAsyncCall:!1,skipList:e})}async stepOver(){this.#Yr=this.#Hr?.callFrames[0]?.functionLocation()??null;const e=await this.computeAutoStepSkipList("StepOver");this.agent.invoke_stepOver({skipList:e})}async stepOut(){const e=await this.computeAutoStepSkipList("StepOut");0!==e.length?this.agent.invoke_stepOver({skipList:e}):this.agent.invoke_stepOut()}scheduleStepIntoAsync(){this.computeAutoStepSkipList("StepInto").then((e=>{this.agent.invoke_stepInto({breakOnAsyncCall:!0,skipList:e})}))}resume(){this.agent.invoke_resume({terminateOnResume:!1}),this.#Zr=!1}pause(){this.#Zr=!0,this.skipAllPauses(!1),this.agent.invoke_pause()}async setBreakpointByURL(r,n,s,i){let a;if(this.target().type()===Ue.Node&&e.ParsedURL.schemeIs(r,"file:")){const n=e.ParsedURL.ParsedURL.urlToRawPathString(r,o.Platform.isWin());a=`${t.StringUtilities.escapeForRegExp(n)}|${t.StringUtilities.escapeForRegExp(r)}`,o.Platform.isWin()&&n.match(/^.:\\/)&&(a=`[${n[0].toUpperCase()}${n[0].toLowerCase()}]`+a.substr(1))}let l=0;const d=this.#_r.get(r)||[];for(let e=0,t=d.length;e<t;++e){const t=d[e];n===t.lineOffset&&(l=l?Math.min(l,t.columnOffset):t.columnOffset)}s=Math.max(s||0,l);const c=await this.agent.invoke_setBreakpointByUrl({lineNumber:n,url:a?void 0:r,urlRegex:a,columnNumber:s,condition:i});if(c.getError())return{locations:[],breakpointId:null};let h=[];return c.locations&&(h=c.locations.map((e=>Pr.fromPayload(this,e)))),{locations:h,breakpointId:c.breakpointId}}async setBreakpointInAnonymousScript(e,t,r,n){const s=await this.agent.invoke_setBreakpointByUrl({lineNumber:t,scriptHash:e,columnNumber:r,condition:n});if(s.getError())return{locations:[],breakpointId:null};let i=[];return s.locations&&(i=s.locations.map((e=>Pr.fromPayload(this,e)))),{locations:i,breakpointId:s.breakpointId}}async removeBreakpoint(e){await this.agent.invoke_removeBreakpoint({breakpointId:e})}async getPossibleBreakpoints(e,t,r){const n=await this.agent.invoke_getPossibleBreakpoints({start:e.payload(),end:t?t.payload():void 0,restrictToFunction:r});return n.getError()||!n.locations?[]:n.locations.map((e=>Lr.fromPayload(this,e)))}async fetchAsyncStackTrace(e){const t=await this.agent.invoke_getStackTrace({stackTraceId:e});return t.getError()?null:t.stackTrace}breakpointResolved(e,t){this.#Jr.dispatchEventToListeners(e,Pr.fromPayload(this,t))}globalObjectCleared(){this.resetDebuggerPausedDetails(),this.reset(),this.dispatchEventToListeners(Tr.GlobalObjectCleared,this)}reset(){for(const e of this.#qr.values())this.#Ur.detachSourceMap(e);this.#qr.clear(),this.#_r.clear(),this.#zr=[],this.#Yr=null}scripts(){return Array.from(this.#qr.values())}scriptForId(e){return this.#qr.get(e)||null}scriptsForSourceURL(e){return this.#_r.get(e)||[]}scriptsForExecutionContext(e){const t=[];for(const r of this.#qr.values())r.executionContextId===e.id&&t.push(r);return t}get callFrames(){return this.#Hr?this.#Hr.callFrames:null}debuggerPausedDetails(){return this.#Hr}async setDebuggerPausedDetails(e){return this.#Zr=!1,this.#Hr=e,!(this.#Kr&&!await this.#Kr.call(null,e,this.#Yr))&&(this.#Yr=null,this.dispatchEventToListeners(Tr.DebuggerPaused,this),this.setSelectedCallFrame(e.callFrames[0]),!0)}resetDebuggerPausedDetails(){this.#Zr=!1,this.#Hr=null,this.setSelectedCallFrame(null)}setBeforePausedCallback(e){this.#Kr=e}setExpandCallFramesCallback(e){this.#$r=e}setEvaluateOnCallFrameCallback(e){this.evaluateOnCallFrameCallback=e}setSynchronizeBreakpointsCallback(e){this.#Xr=e}async pausedScript(t,r,n,s,i,a){if("instrumentation"===r){const e=this.scriptForId(n.scriptId);return this.#Xr&&e&&await this.#Xr(e),void this.resume()}const o=new Or(this,t,r,n,s,i,a);if(this.#$r&&(o.callFrames=await this.#$r.call(null,o.callFrames)),this.continueToLocationCallback){const e=this.continueToLocationCallback;if(this.continueToLocationCallback=null,e(o))return}await this.setDebuggerPausedDetails(o)?e.EventTarget.fireEvent("DevTools.DebuggerPaused"):this.#Yr?this.stepOver():this.stepInto()}resumedScript(){this.resetDebuggerPausedDetails(),this.dispatchEventToListeners(Tr.DebuggerResumed,this)}parsedScriptSource(e,t,r,n,s,i,a,l,d,c,h,u,g,p,m,f,b,y,v,I){const k=this.#qr.get(e);if(k)return k;let w=!1;d&&"isDefault"in d&&(w=!d.isDefault);const S=new fr(this,e,t,r,n,s,i,a,l,w,c,h,u,p,m,f,b,y,v,I);this.registerScript(S),this.dispatchEventToListeners(Tr.ParsedScriptSource,S),S.isInlineScript()&&!S.hasSourceURL&&(S.isModule?o.userMetrics.inlineScriptParsed(0):o.userMetrics.inlineScriptParsed(1)),S.sourceMapURL&&!g&&this.#Ur.attachSourceMap(S,S.sourceURL,S.sourceMapURL);return g&&S.isAnonymousScript()&&(this.#zr.push(S),this.collectDiscardedScripts()),S}setSourceMapURL(e,t){this.#Ur.detachSourceMap(e),e.sourceMapURL=t,this.#Ur.attachSourceMap(e,e.sourceURL,e.sourceMapURL)}async setDebugInfoURL(e,t){this.#$r&&this.#Hr&&(this.#Hr.callFrames=await this.#$r.call(null,this.#Hr.callFrames)),this.dispatchEventToListeners(Tr.DebugInfoAttached,e)}executionContextDestroyed(e){for(const t of this.#qr.values())t.executionContextId===e.id&&this.#Ur.detachSourceMap(t)}registerScript(e){if(this.#qr.set(e.scriptId,e),e.isAnonymousScript())return;let t=this.#_r.get(e.sourceURL);t||(t=[],this.#_r.set(e.sourceURL,t)),t.unshift(e)}unregisterScript(e){console.assert(e.isAnonymousScript()),this.#qr.delete(e.scriptId)}collectDiscardedScripts(){if(this.#zr.length<1e3)return;const e=this.#zr.splice(0,100);for(const t of e)this.unregisterScript(t),this.dispatchEventToListeners(Tr.DiscardedAnonymousScriptSource,t)}createRawLocation(e,t,r,n){return this.createRawLocationByScriptId(e.scriptId,t,r,n)}createRawLocationByURL(e,t,r,n,s){for(const i of this.#_r.get(e)||[]){if(!s){if(i.lineOffset>t||i.lineOffset===t&&void 0!==r&&i.columnOffset>r)continue;if(i.endLine<t||i.endLine===t&&void 0!==r&&i.endColumn<=r)continue}return new Pr(this,i.scriptId,t,r,n)}return null}createRawLocationByScriptId(e,t,r,n){return new Pr(this,e,t,r,n)}createRawLocationsByStackTrace(e){const t=[];for(let r=e;r;r=r.parent)for(const{scriptId:e,lineNumber:n,columnNumber:s}of r.callFrames)t.push(this.createRawLocationByScriptId(e,n,s));return t}isPaused(){return Boolean(this.debuggerPausedDetails())}isPausing(){return this.#Zr}setSelectedCallFrame(e){this.#jr!==e&&(this.#jr=e,this.dispatchEventToListeners(Tr.CallFrameSelected,this))}selectedCallFrame(){return this.#jr}async evaluateOnSelectedCallFrame(e){const t=this.selectedCallFrame();if(!t)throw new Error("No call frame selected");return t.evaluate(e)}functionDetailsPromise(e){return e.getAllProperties(!1,!1).then(function(e){if(!e)return null;let t=null;if(e.internalProperties)for(const r of e.internalProperties)"[[FunctionLocation]]"===r.name&&(t=r.value);let r=null;if(e.properties)for(const t of e.properties)"name"===t.name&&t.value&&"string"===t.value.type&&(r=t.value);let n=null;t&&(n=this.createRawLocationByScriptId(t.value.scriptId,t.value.lineNumber,t.value.columnNumber));return{location:n,functionName:r?r.value:""}}.bind(this))}async setVariableValue(e,t,r,n){return(await this.agent.invoke_setVariableValue({scopeNumber:e,variableName:t,newValue:r,callFrameId:n})).getError()}addBreakpointListener(e,t,r){this.#Jr.addEventListener(e,t,r)}removeBreakpointListener(e,t,r){this.#Jr.removeEventListener(e,t,r)}async setBlackboxPatterns(e){return!(await this.agent.invoke_setBlackboxPatterns({patterns:e})).getError()}dispose(){this.#Ur.dispose(),this.#Wr&&Rr.delete(this.#Wr),e.Settings.Settings.instance().moduleSetting("pause-on-exception-enabled").removeChangeListener(this.pauseOnExceptionStateChanged,this),e.Settings.Settings.instance().moduleSetting("pause-on-caught-exception").removeChangeListener(this.pauseOnExceptionStateChanged,this),e.Settings.Settings.instance().moduleSetting("disable-async-stack-traces").removeChangeListener(this.asyncStackTracesStateChanged,this)}async suspendModel(){await this.disableDebugger()}async resumeModel(){await this.enableDebugger()}static shouldResyncDebuggerId=!1;getContinueToLocationCallback(){return this.continueToLocationCallback}getEvaluateOnCallFrameCallback(){return this.evaluateOnCallFrameCallback}}const Rr=new Map;var xr,Tr;!function(e){e.DontPauseOnExceptions="none",e.PauseOnAllExceptions="all",e.PauseOnCaughtExceptions="caught",e.PauseOnUncaughtExceptions="uncaught"}(xr||(xr={})),function(e){e.DebuggerWasEnabled="DebuggerWasEnabled",e.DebuggerWasDisabled="DebuggerWasDisabled",e.DebuggerPaused="DebuggerPaused",e.DebuggerResumed="DebuggerResumed",e.DebugInfoAttached="DebugInfoAttached",e.ParsedScriptSource="ParsedScriptSource",e.DiscardedAnonymousScriptSource="DiscardedAnonymousScriptSource",e.GlobalObjectCleared="GlobalObjectCleared",e.CallFrameSelected="CallFrameSelected",e.DebuggerIsReadyToPause="DebuggerIsReadyToPause",e.ScriptSourceWasEdited="ScriptSourceWasEdited"}(Tr||(Tr={}));class Mr{#en;constructor(e){this.#en=e}paused({callFrames:e,reason:t,data:r,hitBreakpoints:n,asyncStackTrace:s,asyncStackTraceId:i}){this.#en.debuggerEnabled()&&this.#en.pausedScript(e,t,r,n||[],s,i)}resumed(){this.#en.debuggerEnabled()&&this.#en.resumedScript()}scriptParsed({scriptId:e,url:t,startLine:r,startColumn:n,endLine:s,endColumn:i,executionContextId:a,hash:o,executionContextAuxData:l,isLiveEdit:d,sourceMapURL:c,hasSourceURL:h,length:u,isModule:g,stackTrace:p,codeOffset:m,scriptLanguage:f,debugSymbols:b,embedderName:y}){this.#en.debuggerEnabled()&&this.#en.parsedScriptSource(e,t,r,n,s,i,a,o,l,Boolean(d),c,Boolean(h),!1,u||0,g||null,p||null,m||null,f||null,b||null,y||null)}scriptFailedToParse({scriptId:e,url:t,startLine:r,startColumn:n,endLine:s,endColumn:i,executionContextId:a,hash:o,executionContextAuxData:l,sourceMapURL:d,hasSourceURL:c,length:h,isModule:u,stackTrace:g,codeOffset:p,scriptLanguage:m,embedderName:f}){this.#en.debuggerEnabled()&&this.#en.parsedScriptSource(e,t,r,n,s,i,a,o,l,!1,d,Boolean(c),!0,h||0,u||null,g||null,p||null,m||null,null,f||null)}breakpointResolved({breakpointId:e,location:t}){this.#en.debuggerEnabled()&&this.#en.breakpointResolved(e,t)}}class Pr{debuggerModel;scriptId;lineNumber;columnNumber;inlineFrameIndex;constructor(e,t,r,n,s){this.debuggerModel=e,this.scriptId=t,this.lineNumber=r,this.columnNumber=n||0,this.inlineFrameIndex=s||0}static fromPayload(e,t,r){return new Pr(e,t.scriptId,t.lineNumber,t.columnNumber,r)}payload(){return{scriptId:this.scriptId,lineNumber:this.lineNumber,columnNumber:this.columnNumber}}script(){return this.debuggerModel.scriptForId(this.scriptId)}continueToLocation(e){e&&(this.debuggerModel.continueToLocationCallback=this.paused.bind(this,e)),this.debuggerModel.agent.invoke_continueToLocation({location:this.payload(),targetCallFrames:"current"})}paused(e,t){const r=t.callFrames[0].location();return r.scriptId===this.scriptId&&r.lineNumber===this.lineNumber&&r.columnNumber===this.columnNumber&&(e(),!0)}id(){return this.debuggerModel.target().id()+":"+this.scriptId+":"+this.lineNumber+":"+this.columnNumber}}class Lr extends Pr{type;constructor(e,t,r,n,s){super(e,t,r,n),s&&(this.type=s)}static fromPayload(e,t){return new Lr(e,t.scriptId,t.lineNumber,t.columnNumber,t.type)}}class Ar{debuggerModel;script;payload;#tn;#rn;#nn;inlineFrameIndex;functionName;#sn;#in;missingDebugInfoDetails;canBeRestarted;constructor(e,t,r,n,s){this.debuggerModel=e,this.script=t,this.payload=r,this.#tn=Pr.fromPayload(e,r.location,n),this.#rn=[],this.#nn=null,this.inlineFrameIndex=n||0,this.functionName=s||r.functionName,this.missingDebugInfoDetails=null,this.canBeRestarted=Boolean(r.canBeRestarted);for(let e=0;e<r.scopeChain.length;++e){const t=new Er(this,e);this.#rn.push(t),"local"===t.type()&&(this.#nn=t)}r.functionLocation&&(this.#sn=Pr.fromPayload(e,r.functionLocation)),this.#in=r.returnValue?this.debuggerModel.runtimeModel().createRemoteObject(r.returnValue):null}static fromPayloadArray(e,t){const r=[];for(let n=0;n<t.length;++n){const s=t[n],i=e.scriptForId(s.location.scriptId);i&&r.push(new Ar(e,i,s))}return r}createVirtualCallFrame(e,t){return new Ar(this.debuggerModel,this.script,this.payload,e,t)}get id(){return this.payload.callFrameId}scopeChain(){return this.#rn}localScope(){return this.#nn}thisObject(){return this.payload.this?this.debuggerModel.runtimeModel().createRemoteObject(this.payload.this):null}returnValue(){return this.#in}async setReturnValue(e){if(!this.#in)return null;const t=await this.debuggerModel.agent.invoke_evaluateOnCallFrame({callFrameId:this.id,expression:e,silent:!0,objectGroup:"backtrace"});if(t.getError()||t.exceptionDetails)return null;return(await this.debuggerModel.agent.invoke_setReturnValue({newValue:t.result})).getError()?null:(this.#in=this.debuggerModel.runtimeModel().createRemoteObject(t.result),this.#in)}location(){return this.#tn}functionLocation(){return this.#sn||null}async evaluate(e){const t=this.debuggerModel,r=t.runtimeModel();if((Boolean(e.throwOnSideEffect)||void 0!==e.timeout)&&(!1===r.hasSideEffectSupport()||null===r.hasSideEffectSupport()&&!await r.checkSideEffectSupport()))return{error:"Side-effect checks not supported by backend."};const n=t.getEvaluateOnCallFrameCallback();if(n){const t=await n(this,e);if(t)return t}const s=await this.debuggerModel.agent.invoke_evaluateOnCallFrame({callFrameId:this.id,expression:e.expression,objectGroup:e.objectGroup,includeCommandLineAPI:e.includeCommandLineAPI,silent:e.silent,returnByValue:e.returnByValue,generatePreview:e.generatePreview,throwOnSideEffect:e.throwOnSideEffect,timeout:e.timeout}),i=s.getError();return i?{error:i}:{object:r.createRemoteObject(s.result),exceptionDetails:s.exceptionDetails}}async restart(){console.assert(this.canBeRestarted,"This frame can not be restarted."),await this.debuggerModel.agent.invoke_restartFrame({callFrameId:this.id,mode:"StepInto"})}getPayload(){return this.payload}}class Er{#an;#on;#g;#h;#ln;#dn;#ot;constructor(e,t){this.#an=e,this.#on=e.getPayload().scopeChain[t],this.#g=this.#on.type,this.#h=this.#on.name,this.#ln=t,this.#ot=null;const r=this.#on.startLocation?Pr.fromPayload(e.debuggerModel,this.#on.startLocation):null,n=this.#on.endLocation?Pr.fromPayload(e.debuggerModel,this.#on.endLocation):null;r&&n&&r.scriptId===n.scriptId?this.#dn={start:r,end:n}:this.#dn=null}callFrame(){return this.#an}type(){return this.#g}typeName(){switch(this.#g){case"local":return wr(Ir.local);case"closure":return wr(Ir.closure);case"catch":return wr(Ir.catchBlock);case"eval":return i.i18n.lockedString("Eval");case"block":return wr(Ir.block);case"script":return wr(Ir.script);case"with":return wr(Ir.withBlock);case"global":return wr(Ir.global);case"module":return wr(Ir.module);case"wasm-expression-stack":return wr(Ir.expression)}return""}name(){return this.#h}range(){return this.#dn}object(){if(this.#ot)return this.#ot;const e=this.#an.debuggerModel.runtimeModel(),t="with"!==this.#g&&"global"!==this.#g;return this.#ot=t?e.createScopeRemoteObject(this.#on.object,new Je(this.#ln,this.#an.id)):e.createRemoteObject(this.#on.object),this.#ot}description(){return"with"!==this.#g&&"global"!==this.#g?"":this.#on.object.description||""}icon(){}}class Or{debuggerModel;callFrames;reason;auxData;breakpointIds;asyncStackTrace;asyncStackTraceId;constructor(e,t,r,n,s,i,a){this.debuggerModel=e,this.callFrames=Ar.fromPayloadArray(e,t),this.reason=r,this.auxData=n,this.breakpointIds=s,i&&(this.asyncStackTrace=this.cleanRedundantFrames(i)),this.asyncStackTraceId=a}exception(){return"exception"!==this.reason&&"promiseRejection"!==this.reason?null:this.debuggerModel.runtimeModel().createRemoteObject(this.auxData)}cleanRedundantFrames(e){let t=e,r=null;for(;t;)"async function"===t.description&&t.callFrames.length&&t.callFrames.shift(),r&&!t.callFrames.length?r.parent=t.parent:r=t,t=t.parent;return e}}c.register(Cr,{capabilities:4,autostart:!0});const Nr="debugger://logpoint",Dr="debugger://breakpoint";var Fr=Object.freeze({__proto__:null,sortAndMergeRanges:Sr,DebuggerModel:Cr,get PauseOnExceptionsState(){return xr},get Events(){return Tr},Location:Pr,BreakLocation:Lr,CallFrame:Ar,Scope:Er,DebuggerPausedDetails:Or,LOGPOINT_SOURCE_URL:Nr,COND_BREAKPOINT_SOURCE_URL:Dr});class Br{#cn;#hn;constructor(){const t="rgba";this.#cn=[new e.Color.Legacy([.9607843137254902,.592156862745098,.5803921568627451,1],t),new e.Color.Legacy([.9411764705882353,.7490196078431373,.2980392156862745,1],t),new e.Color.Legacy([.8313725490196079,.9294117647058824,.19215686274509805,1],t),new e.Color.Legacy([.6196078431372549,.9215686274509803,.2784313725490196,1],t),new e.Color.Legacy([.3568627450980392,.8196078431372549,.8431372549019608,1],t),new e.Color.Legacy([.7372549019607844,.807843137254902,.984313725490196,1],t),new e.Color.Legacy([.7764705882352941,.7450980392156863,.9333333333333333,1],t),new e.Color.Legacy([.8156862745098039,.5803921568627451,.9176470588235294,1],t),new e.Color.Legacy([.9215686274509803,.5803921568627451,.8117647058823529,1],t)],this.#hn=0}next(){const e=this.#cn[this.#hn];return this.#hn++,this.#hn>=this.#cn.length&&(this.#hn=0),e}}var Ur=Object.freeze({__proto__:null,OverlayColorGenerator:Br});class Hr{#un;#cn;#gn;#pn;#mn;#fn;#bn;#yn;#vn;#In;#kn;#wn;#Sn;#Cn;#Rn;constructor(t,r){this.#un=t,this.#Rn=r,this.#gn=e.Settings.Settings.instance().createLocalSetting("persistent-highlight-setting",[]),this.#pn=new Map,this.#mn=new Map,this.#fn=new Map,this.#bn=new Map,this.#yn=new Map,this.#cn=new Map,this.#vn=new Br,this.#In=new Br,this.#kn=e.Settings.Settings.instance().moduleSetting("show-grid-line-labels"),this.#kn.addChangeListener(this.onSettingChange,this),this.#wn=e.Settings.Settings.instance().moduleSetting("extend-grid-lines"),this.#wn.addChangeListener(this.onSettingChange,this),this.#Sn=e.Settings.Settings.instance().moduleSetting("show-grid-areas"),this.#Sn.addChangeListener(this.onSettingChange,this),this.#Cn=e.Settings.Settings.instance().moduleSetting("show-grid-track-sizes"),this.#Cn.addChangeListener(this.onSettingChange,this)}onSettingChange(){this.resetOverlay()}buildGridHighlightConfig(e){const t=this.colorOfGrid(e).asLegacyColor(),r=t.setAlpha(.1).asLegacyColor(),n=t.setAlpha(.3).asLegacyColor(),s=t.setAlpha(.8).asLegacyColor(),i=this.#wn.get(),a="lineNumbers"===this.#kn.get(),o=a,l="lineNames"===this.#kn.get();return{rowGapColor:n.toProtocolRGBA(),rowHatchColor:s.toProtocolRGBA(),columnGapColor:n.toProtocolRGBA(),columnHatchColor:s.toProtocolRGBA(),gridBorderColor:t.toProtocolRGBA(),gridBorderDash:!1,rowLineColor:t.toProtocolRGBA(),columnLineColor:t.toProtocolRGBA(),rowLineDash:!0,columnLineDash:!0,showGridExtensionLines:i,showPositiveLineNumbers:a,showNegativeLineNumbers:o,showLineNames:l,showAreaNames:this.#Sn.get(),showTrackSizes:this.#Cn.get(),areaBorderColor:t.toProtocolRGBA(),gridBackgroundColor:r.toProtocolRGBA()}}buildFlexContainerHighlightConfig(e){const t=this.colorOfFlex(e).asLegacyColor();return{containerBorder:{color:t.toProtocolRGBA(),pattern:"dashed"},itemSeparator:{color:t.toProtocolRGBA(),pattern:"dotted"},lineSeparator:{color:t.toProtocolRGBA(),pattern:"dashed"},mainDistributedSpace:{hatchColor:t.toProtocolRGBA()},crossDistributedSpace:{hatchColor:t.toProtocolRGBA()}}}buildScrollSnapContainerHighlightConfig(t){return{snapAreaBorder:{color:e.Color.PageHighlight.GridBorder.toProtocolRGBA(),pattern:"dashed"},snapportBorder:{color:e.Color.PageHighlight.GridBorder.toProtocolRGBA()},scrollMarginColor:e.Color.PageHighlight.Margin.toProtocolRGBA(),scrollPaddingColor:e.Color.PageHighlight.Padding.toProtocolRGBA()}}highlightGridInOverlay(e){this.#pn.set(e,this.buildGridHighlightConfig(e)),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Rn.onGridOverlayStateChanged({nodeId:e,enabled:!0})}isGridHighlighted(e){return this.#pn.has(e)}colorOfGrid(e){let t=this.#cn.get(e);return t||(t=this.#vn.next(),this.#cn.set(e,t)),t}setColorOfGrid(e,t){this.#cn.set(e,t)}hideGridInOverlay(e){this.#pn.has(e)&&(this.#pn.delete(e),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Rn.onGridOverlayStateChanged({nodeId:e,enabled:!1}))}highlightScrollSnapInOverlay(e){this.#mn.set(e,this.buildScrollSnapContainerHighlightConfig(e)),this.updateHighlightsInOverlay(),this.#Rn.onScrollSnapOverlayStateChanged({nodeId:e,enabled:!0}),this.savePersistentHighlightSetting()}isScrollSnapHighlighted(e){return this.#mn.has(e)}hideScrollSnapInOverlay(e){this.#mn.has(e)&&(this.#mn.delete(e),this.updateHighlightsInOverlay(),this.#Rn.onScrollSnapOverlayStateChanged({nodeId:e,enabled:!1}),this.savePersistentHighlightSetting())}highlightFlexInOverlay(e){this.#fn.set(e,this.buildFlexContainerHighlightConfig(e)),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Rn.onFlexOverlayStateChanged({nodeId:e,enabled:!0})}isFlexHighlighted(e){return this.#fn.has(e)}colorOfFlex(e){let t=this.#cn.get(e);return t||(t=this.#In.next(),this.#cn.set(e,t)),t}setColorOfFlex(e,t){this.#cn.set(e,t)}hideFlexInOverlay(e){this.#fn.has(e)&&(this.#fn.delete(e),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Rn.onFlexOverlayStateChanged({nodeId:e,enabled:!1}))}highlightContainerQueryInOverlay(e){this.#bn.set(e,this.buildContainerQueryContainerHighlightConfig()),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Rn.onContainerQueryOverlayStateChanged({nodeId:e,enabled:!0})}hideContainerQueryInOverlay(e){this.#bn.has(e)&&(this.#bn.delete(e),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting(),this.#Rn.onContainerQueryOverlayStateChanged({nodeId:e,enabled:!1}))}isContainerQueryHighlighted(e){return this.#bn.has(e)}buildContainerQueryContainerHighlightConfig(){return{containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},descendantBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"}}}highlightIsolatedElementInOverlay(e){this.#yn.set(e,this.buildIsolationModeHighlightConfig()),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting()}hideIsolatedElementInOverlay(e){this.#yn.has(e)&&(this.#yn.delete(e),this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting())}isIsolatedElementHighlighted(e){return this.#yn.has(e)}buildIsolationModeHighlightConfig(){return{resizerColor:e.Color.IsolationModeHighlight.Resizer.toProtocolRGBA(),resizerHandleColor:e.Color.IsolationModeHighlight.ResizerHandle.toProtocolRGBA(),maskColor:e.Color.IsolationModeHighlight.Mask.toProtocolRGBA()}}hideAllInOverlayWithoutSave(){this.#fn.clear(),this.#pn.clear(),this.#mn.clear(),this.#bn.clear(),this.#yn.clear(),this.updateHighlightsInOverlay()}refreshHighlights(){const e=this.updateHighlightsForDeletedNodes(this.#pn),t=this.updateHighlightsForDeletedNodes(this.#fn),r=this.updateHighlightsForDeletedNodes(this.#mn),n=this.updateHighlightsForDeletedNodes(this.#bn),s=this.updateHighlightsForDeletedNodes(this.#yn);(t||e||r||n||s)&&(this.updateHighlightsInOverlay(),this.savePersistentHighlightSetting())}updateHighlightsForDeletedNodes(e){let t=!1;for(const r of e.keys())null===this.#un.getDOMModel().nodeForId(r)&&(e.delete(r),t=!0);return t}resetOverlay(){for(const e of this.#pn.keys())this.#pn.set(e,this.buildGridHighlightConfig(e));for(const e of this.#fn.keys())this.#fn.set(e,this.buildFlexContainerHighlightConfig(e));for(const e of this.#mn.keys())this.#mn.set(e,this.buildScrollSnapContainerHighlightConfig(e));for(const e of this.#bn.keys())this.#bn.set(e,this.buildContainerQueryContainerHighlightConfig());for(const e of this.#yn.keys())this.#yn.set(e,this.buildIsolationModeHighlightConfig());this.updateHighlightsInOverlay()}updateHighlightsInOverlay(){const e=this.#pn.size>0||this.#fn.size>0||this.#bn.size>0||this.#yn.size>0;this.#un.setShowViewportSizeOnResize(!e),this.updateGridHighlightsInOverlay(),this.updateFlexHighlightsInOverlay(),this.updateScrollSnapHighlightsInOverlay(),this.updateContainerQueryHighlightsInOverlay(),this.updateIsolatedElementHighlightsInOverlay()}updateGridHighlightsInOverlay(){const e=this.#un,t=[];for(const[e,r]of this.#pn.entries())t.push({nodeId:e,gridHighlightConfig:r});e.target().overlayAgent().invoke_setShowGridOverlays({gridNodeHighlightConfigs:t})}updateFlexHighlightsInOverlay(){const e=this.#un,t=[];for(const[e,r]of this.#fn.entries())t.push({nodeId:e,flexContainerHighlightConfig:r});e.target().overlayAgent().invoke_setShowFlexOverlays({flexNodeHighlightConfigs:t})}updateScrollSnapHighlightsInOverlay(){const e=this.#un,t=[];for(const[e,r]of this.#mn.entries())t.push({nodeId:e,scrollSnapContainerHighlightConfig:r});e.target().overlayAgent().invoke_setShowScrollSnapOverlays({scrollSnapHighlightConfigs:t})}updateContainerQueryHighlightsInOverlay(){const e=this.#un,t=[];for(const[e,r]of this.#bn.entries())t.push({nodeId:e,containerQueryContainerHighlightConfig:r});e.target().overlayAgent().invoke_setShowContainerQueryOverlays({containerQueryHighlightConfigs:t})}updateIsolatedElementHighlightsInOverlay(){const e=this.#un,t=[];for(const[e,r]of this.#yn.entries())t.push({nodeId:e,isolationModeHighlightConfig:r});e.target().overlayAgent().invoke_setShowIsolatedElements({isolatedElementHighlightConfigs:t})}async restoreHighlightsForDocument(){this.#fn=new Map,this.#pn=new Map,this.#mn=new Map,this.#bn=new Map,this.#yn=new Map;const e=await this.#un.getDOMModel().requestDocument(),r=e?e.documentURL:t.DevToolsPath.EmptyUrlString;await Promise.all(this.#gn.get().map((async e=>{if(e.url===r)return this.#un.getDOMModel().pushNodeByPathToFrontend(e.path).then((t=>{const r=this.#un.getDOMModel().nodeForId(t);if(r)switch(e.type){case"GRID":this.#pn.set(r.id,this.buildGridHighlightConfig(r.id)),this.#Rn.onGridOverlayStateChanged({nodeId:r.id,enabled:!0});break;case"FLEX":this.#fn.set(r.id,this.buildFlexContainerHighlightConfig(r.id)),this.#Rn.onFlexOverlayStateChanged({nodeId:r.id,enabled:!0});break;case"CONTAINER_QUERY":this.#bn.set(r.id,this.buildContainerQueryContainerHighlightConfig()),this.#Rn.onContainerQueryOverlayStateChanged({nodeId:r.id,enabled:!0});break;case"SCROLL_SNAP":this.#mn.set(r.id,this.buildScrollSnapContainerHighlightConfig(r.id)),this.#Rn.onScrollSnapOverlayStateChanged({nodeId:r.id,enabled:!0});break;case"ISOLATED_ELEMENT":this.#yn.set(r.id,this.buildIsolationModeHighlightConfig())}}))}))),this.updateHighlightsInOverlay()}currentUrl(){const e=this.#un.getDOMModel().existingDocument();return e?e.documentURL:t.DevToolsPath.EmptyUrlString}getPersistentHighlightSettingForOneType(e,t){const r=[];for(const n of e.keys()){const e=this.#un.getDOMModel().nodeForId(n);e&&r.push({url:this.currentUrl(),path:e.path(),type:t})}return r}savePersistentHighlightSetting(){const e=this.currentUrl(),t=[...this.#gn.get().filter((t=>t.url!==e)),...this.getPersistentHighlightSettingForOneType(this.#pn,"GRID"),...this.getPersistentHighlightSettingForOneType(this.#fn,"FLEX"),...this.getPersistentHighlightSettingForOneType(this.#bn,"CONTAINER_QUERY"),...this.getPersistentHighlightSettingForOneType(this.#mn,"SCROLL_SNAP"),...this.getPersistentHighlightSettingForOneType(this.#yn,"ISOLATED_ELEMENT")];this.#gn.set(t)}}var qr=Object.freeze({__proto__:null,OverlayPersistentHighlighter:Hr});const _r={pausedInDebugger:"Paused in debugger"},zr=i.i18n.registerUIStrings("core/sdk/OverlayModel.ts",_r),jr=i.i18n.getLocalizedString.bind(void 0,zr),Vr={mac:{x:85,y:0,width:185,height:40},linux:{x:0,y:0,width:196,height:34},windows:{x:0,y:0,width:238,height:33}};class Wr extends c{#ir;overlayAgent;#en;#xn;#Tn;#Mn;#Pn;#Ln;#An;#En;#On;#Nn;#Dn;#Fn;#Bn;#Un;#Hn;#qn;#_n;#zn;constructor(t){super(t),this.#ir=t.model(tn),t.registerOverlayDispatcher(this),this.overlayAgent=t.overlayAgent(),this.#en=t.model(Cr),this.#en&&(e.Settings.Settings.instance().moduleSetting("disable-paused-state-overlay").addChangeListener(this.updatePausedInDebuggerMessage,this),this.#en.addEventListener(Tr.DebuggerPaused,this.updatePausedInDebuggerMessage,this),this.#en.addEventListener(Tr.DebuggerResumed,this.updatePausedInDebuggerMessage,this),this.#en.addEventListener(Tr.GlobalObjectCleared,this.updatePausedInDebuggerMessage,this)),this.#xn=!1,this.#Tn=null,this.#Mn=new Kr(this),this.#Pn=this.#Mn,this.#Ln=e.Settings.Settings.instance().moduleSetting("show-paint-rects"),this.#An=e.Settings.Settings.instance().moduleSetting("show-layout-shift-regions"),this.#En=e.Settings.Settings.instance().moduleSetting("show-ad-highlights"),this.#On=e.Settings.Settings.instance().moduleSetting("show-debug-borders"),this.#Nn=e.Settings.Settings.instance().moduleSetting("show-fps-counter"),this.#Dn=e.Settings.Settings.instance().moduleSetting("show-scroll-bottleneck-rects"),this.#Fn=e.Settings.Settings.instance().moduleSetting("show-web-vitals"),this.#Bn=[],this.#Un=!0,t.suspended()||(this.overlayAgent.invoke_enable(),this.wireAgentToSettings()),this.#Hn=new Hr(this,{onGridOverlayStateChanged:({nodeId:e,enabled:t})=>this.dispatchEventToListeners("PersistentGridOverlayStateChanged",{nodeId:e,enabled:t}),onFlexOverlayStateChanged:({nodeId:e,enabled:t})=>this.dispatchEventToListeners("PersistentFlexContainerOverlayStateChanged",{nodeId:e,enabled:t}),onContainerQueryOverlayStateChanged:({nodeId:e,enabled:t})=>this.dispatchEventToListeners("PersistentContainerQueryOverlayStateChanged",{nodeId:e,enabled:t}),onScrollSnapOverlayStateChanged:({nodeId:e,enabled:t})=>this.dispatchEventToListeners("PersistentScrollSnapOverlayStateChanged",{nodeId:e,enabled:t})}),this.#ir.addEventListener($r.NodeRemoved,(()=>{this.#Hn&&this.#Hn.refreshHighlights()})),this.#ir.addEventListener($r.DocumentUpdated,(()=>{this.#Hn&&(this.#Hn.hideAllInOverlayWithoutSave(),t.suspended()||this.#Hn.restoreHighlightsForDocument())})),this.#qn=new Qr(this),this.#_n=!1,this.#zn=new Gr(this.#ir.cssModel())}static highlightObjectAsDOMNode(e){const t=e.runtimeModel().target().model(tn);t&&t.overlayModel().highlightInOverlay({object:e,selectorList:void 0})}static hideDOMNodeHighlight(){for(const e of je.instance().models(Wr))e.delayedHideHighlight(0)}static async muteHighlight(){return Promise.all(je.instance().models(Wr).map((e=>e.suspendModel())))}static async unmuteHighlight(){return Promise.all(je.instance().models(Wr).map((e=>e.resumeModel())))}static highlightRect(e){for(const t of je.instance().models(Wr))t.highlightRect(e)}static clearHighlight(){for(const e of je.instance().models(Wr))e.clearHighlight()}getDOMModel(){return this.#ir}highlightRect({x:e,y:t,width:r,height:n,color:s,outlineColor:i}){const a=s||{r:255,g:0,b:255,a:.3},o=i||{r:255,g:0,b:255,a:.5};return this.overlayAgent.invoke_highlightRect({x:e,y:t,width:r,height:n,color:a,outlineColor:o})}clearHighlight(){return this.overlayAgent.invoke_hideHighlight()}async wireAgentToSettings(){this.#Bn=[this.#Ln.addChangeListener((()=>this.overlayAgent.invoke_setShowPaintRects({result:this.#Ln.get()}))),this.#An.addChangeListener((()=>this.overlayAgent.invoke_setShowLayoutShiftRegions({result:this.#An.get()}))),this.#En.addChangeListener((()=>this.overlayAgent.invoke_setShowAdHighlights({show:this.#En.get()}))),this.#On.addChangeListener((()=>this.overlayAgent.invoke_setShowDebugBorders({show:this.#On.get()}))),this.#Nn.addChangeListener((()=>this.overlayAgent.invoke_setShowFPSCounter({show:this.#Nn.get()}))),this.#Dn.addChangeListener((()=>this.overlayAgent.invoke_setShowScrollBottleneckRects({show:this.#Dn.get()}))),this.#Fn.addChangeListener((()=>this.overlayAgent.invoke_setShowWebVitals({show:this.#Fn.get()})))],this.#Ln.get()&&this.overlayAgent.invoke_setShowPaintRects({result:!0}),this.#An.get()&&this.overlayAgent.invoke_setShowLayoutShiftRegions({result:!0}),this.#En.get()&&this.overlayAgent.invoke_setShowAdHighlights({show:!0}),this.#On.get()&&this.overlayAgent.invoke_setShowDebugBorders({show:!0}),this.#Nn.get()&&this.overlayAgent.invoke_setShowFPSCounter({show:!0}),this.#Dn.get()&&this.overlayAgent.invoke_setShowScrollBottleneckRects({show:!0}),this.#Fn.get()&&this.overlayAgent.invoke_setShowWebVitals({show:!0}),this.#en&&this.#en.isPaused()&&this.updatePausedInDebuggerMessage(),await this.overlayAgent.invoke_setShowViewportSizeOnResize({show:this.#Un}),this.#Hn?.resetOverlay()}async suspendModel(){e.EventTarget.removeEventListeners(this.#Bn),await this.overlayAgent.invoke_disable()}async resumeModel(){await Promise.all([this.overlayAgent.invoke_enable(),this.wireAgentToSettings()])}setShowViewportSizeOnResize(e){this.#Un!==e&&(this.#Un=e,this.target().suspended()||this.overlayAgent.invoke_setShowViewportSizeOnResize({show:e}))}updatePausedInDebuggerMessage(){if(this.target().suspended())return;const t=this.#en&&this.#en.isPaused()&&!e.Settings.Settings.instance().moduleSetting("disable-paused-state-overlay").get()?jr(_r.pausedInDebugger):void 0;this.overlayAgent.invoke_setPausedInDebuggerMessage({message:t})}setHighlighter(e){this.#Pn=e||this.#Mn}async setInspectMode(e,t=!0){await this.#ir.requestDocument(),this.#xn="none"!==e,this.dispatchEventToListeners("InspectModeWillBeToggled",this),this.#Pn.setInspectMode(e,this.buildHighlightConfig("all",t))}inspectModeEnabled(){return this.#xn}highlightInOverlay(e,t,r){if(this.#_n)return;this.#Tn&&(clearTimeout(this.#Tn),this.#Tn=null);const n=this.buildHighlightConfig(t);void 0!==r&&(n.showInfo=r),this.#Pn.highlightInOverlay(e,n)}highlightInOverlayForTwoSeconds(e){this.highlightInOverlay(e),this.delayedHideHighlight(2e3)}highlightGridInPersistentOverlay(e){this.#Hn&&this.#Hn.highlightGridInOverlay(e)}isHighlightedGridInPersistentOverlay(e){return!!this.#Hn&&this.#Hn.isGridHighlighted(e)}hideGridInPersistentOverlay(e){this.#Hn&&this.#Hn.hideGridInOverlay(e)}highlightScrollSnapInPersistentOverlay(e){this.#Hn&&this.#Hn.highlightScrollSnapInOverlay(e)}isHighlightedScrollSnapInPersistentOverlay(e){return!!this.#Hn&&this.#Hn.isScrollSnapHighlighted(e)}hideScrollSnapInPersistentOverlay(e){this.#Hn&&this.#Hn.hideScrollSnapInOverlay(e)}highlightFlexContainerInPersistentOverlay(e){this.#Hn&&this.#Hn.highlightFlexInOverlay(e)}isHighlightedFlexContainerInPersistentOverlay(e){return!!this.#Hn&&this.#Hn.isFlexHighlighted(e)}hideFlexContainerInPersistentOverlay(e){this.#Hn&&this.#Hn.hideFlexInOverlay(e)}highlightContainerQueryInPersistentOverlay(e){this.#Hn&&this.#Hn.highlightContainerQueryInOverlay(e)}isHighlightedContainerQueryInPersistentOverlay(e){return!!this.#Hn&&this.#Hn.isContainerQueryHighlighted(e)}hideContainerQueryInPersistentOverlay(e){this.#Hn&&this.#Hn.hideContainerQueryInOverlay(e)}highlightSourceOrderInOverlay(t){const r={parentOutlineColor:e.Color.SourceOrderHighlight.ParentOutline.toProtocolRGBA(),childOutlineColor:e.Color.SourceOrderHighlight.ChildOutline.toProtocolRGBA()};this.#qn.highlightSourceOrderInOverlay(t,r)}colorOfGridInPersistentOverlay(e){return this.#Hn?this.#Hn.colorOfGrid(e).asString("hex"):null}setColorOfGridInPersistentOverlay(t,r){if(!this.#Hn)return;const n=e.Color.parse(r);n&&(this.#Hn.setColorOfGrid(t,n),this.#Hn.resetOverlay())}colorOfFlexInPersistentOverlay(e){return this.#Hn?this.#Hn.colorOfFlex(e).asString("hex"):null}setColorOfFlexInPersistentOverlay(t,r){if(!this.#Hn)return;const n=e.Color.parse(r);n&&(this.#Hn.setColorOfFlex(t,n),this.#Hn.resetOverlay())}hideSourceOrderInOverlay(){this.#qn.hideSourceOrderHighlight()}setSourceOrderActive(e){this.#_n=e}sourceOrderModeActive(){return this.#_n}highlightIsolatedElementInPersistentOverlay(e){this.#Hn&&this.#Hn.highlightIsolatedElementInOverlay(e)}hideIsolatedElementInPersistentOverlay(e){this.#Hn&&this.#Hn.hideIsolatedElementInOverlay(e)}isHighlightedIsolatedElementInPersistentOverlay(e){return!!this.#Hn&&this.#Hn.isIsolatedElementHighlighted(e)}delayedHideHighlight(e){null===this.#Tn&&(this.#Tn=window.setTimeout((()=>this.highlightInOverlay({clear:!0})),e))}highlightFrame(e){this.#Tn&&(clearTimeout(this.#Tn),this.#Tn=null),this.#Pn.highlightFrame(e)}showHingeForDualScreen(e){if(e){const{x:t,y:r,width:n,height:s,contentColor:i,outlineColor:a}=e;this.overlayAgent.invoke_setShowHinge({hingeConfig:{rect:{x:t,y:r,width:n,height:s},contentColor:i,outlineColor:a}})}else this.overlayAgent.invoke_setShowHinge({})}setWindowControlsPlatform(e){this.#zn.selectedPlatform=e}setWindowControlsThemeColor(e){this.#zn.themeColor=e}getWindowControlsConfig(){return this.#zn.config}async toggleWindowControlsToolbar(e){const t=e?{windowControlsOverlayConfig:this.#zn.config}:{},r=this.overlayAgent.invoke_setShowWindowControlsOverlay(t),n=this.#zn.toggleEmulatedOverlay(e);await Promise.all([r,n]),this.setShowViewportSizeOnResize(!e)}buildHighlightConfig(t="all",r=!1){const n=e.Settings.Settings.instance().moduleSetting("show-metrics-rulers").get(),s={showInfo:"all"===t||"container-outline"===t,showRulers:n,showStyles:r,showAccessibilityInfo:r,showExtensionLines:n,gridHighlightConfig:{},flexContainerHighlightConfig:{},flexItemHighlightConfig:{},contrastAlgorithm:a.Runtime.experiments.isEnabled("apca")?"apca":"aa"};return"all"!==t&&"content"!==t||(s.contentColor=e.Color.PageHighlight.Content.toProtocolRGBA()),"all"!==t&&"padding"!==t||(s.paddingColor=e.Color.PageHighlight.Padding.toProtocolRGBA()),"all"!==t&&"border"!==t||(s.borderColor=e.Color.PageHighlight.Border.toProtocolRGBA()),"all"!==t&&"margin"!==t||(s.marginColor=e.Color.PageHighlight.Margin.toProtocolRGBA()),"all"===t&&(s.eventTargetColor=e.Color.PageHighlight.EventTarget.toProtocolRGBA(),s.shapeColor=e.Color.PageHighlight.Shape.toProtocolRGBA(),s.shapeMarginColor=e.Color.PageHighlight.ShapeMargin.toProtocolRGBA(),s.gridHighlightConfig={rowGapColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA(),rowHatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),columnGapColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA(),columnHatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),rowLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),columnLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),rowLineDash:!0,columnLineDash:!0},s.flexContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},itemSeparator:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dotted"},lineSeparator:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},mainDistributedSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()},crossDistributedSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()},rowGapSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()},columnGapSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()}},s.flexItemHighlightConfig={baseSizeBox:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA()},baseSizeBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dotted"},flexibilityArrow:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA()}}),t.endsWith("gap")&&(s.gridHighlightConfig={gridBorderColor:e.Color.PageHighlight.GridBorder.toProtocolRGBA(),gridBorderDash:!0},"gap"!==t&&"row-gap"!==t||(s.gridHighlightConfig.rowGapColor=e.Color.PageHighlight.GapBackground.toProtocolRGBA(),s.gridHighlightConfig.rowHatchColor=e.Color.PageHighlight.GapHatch.toProtocolRGBA()),"gap"!==t&&"column-gap"!==t||(s.gridHighlightConfig.columnGapColor=e.Color.PageHighlight.GapBackground.toProtocolRGBA(),s.gridHighlightConfig.columnHatchColor=e.Color.PageHighlight.GapHatch.toProtocolRGBA())),t.endsWith("gap")&&(s.flexContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"}},"gap"!==t&&"row-gap"!==t||(s.flexContainerHighlightConfig.rowGapSpace={hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()}),"gap"!==t&&"column-gap"!==t||(s.flexContainerHighlightConfig.columnGapSpace={hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()})),"grid-areas"===t&&(s.gridHighlightConfig={rowLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),columnLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),rowLineDash:!0,columnLineDash:!0,showAreaNames:!0,areaBorderColor:e.Color.PageHighlight.GridAreaBorder.toProtocolRGBA()}),"grid-template-columns"===t&&(s.contentColor=e.Color.PageHighlight.Content.toProtocolRGBA(),s.gridHighlightConfig={columnLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),columnLineDash:!0}),"grid-template-rows"===t&&(s.contentColor=e.Color.PageHighlight.Content.toProtocolRGBA(),s.gridHighlightConfig={rowLineColor:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),rowLineDash:!0}),"justify-content"===t&&(s.flexContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},mainDistributedSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()}}),"align-content"===t&&(s.flexContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},crossDistributedSpace:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA(),fillColor:e.Color.PageHighlight.GapBackground.toProtocolRGBA()}}),"align-items"===t&&(s.flexContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},lineSeparator:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"},crossAlignment:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA()}}),"flexibility"===t&&(s.flexItemHighlightConfig={baseSizeBox:{hatchColor:e.Color.PageHighlight.GapHatch.toProtocolRGBA()},baseSizeBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dotted"},flexibilityArrow:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA()}}),"container-outline"===t&&(s.containerQueryContainerHighlightConfig={containerBorder:{color:e.Color.PageHighlight.LayoutLine.toProtocolRGBA(),pattern:"dashed"}}),s}nodeHighlightRequested({nodeId:e}){const t=this.#ir.nodeForId(e);t&&this.dispatchEventToListeners("HighlightNodeRequested",t)}static setInspectNodeHandler(e){Wr.inspectNodeHandler=e}inspectNodeRequested({backendNodeId:t}){const r=new Yr(this.target(),t);Wr.inspectNodeHandler?r.resolvePromise().then((e=>{e&&Wr.inspectNodeHandler&&Wr.inspectNodeHandler(e)})):e.Revealer.reveal(r),this.dispatchEventToListeners("InspectModeExited")}screenshotRequested({viewport:e}){this.dispatchEventToListeners("ScreenshotRequested",e),this.dispatchEventToListeners("InspectModeExited")}inspectModeCanceled(){this.dispatchEventToListeners("InspectModeExited")}static inspectNodeHandler=null;getOverlayAgent(){return this.overlayAgent}async hasStyleSheetText(e){return this.#zn.initializeStyleSheetText(e)}}class Gr{#J;#jn;#Vn;#Wn;#Gn={showCSS:!1,selectedPlatform:"Windows",themeColor:"#ffffff"};constructor(e){this.#J=e}get selectedPlatform(){return this.#Gn.selectedPlatform}set selectedPlatform(e){this.#Gn.selectedPlatform=e}get themeColor(){return this.#Gn.themeColor}set themeColor(e){this.#Gn.themeColor=e}get config(){return this.#Gn}async initializeStyleSheetText(e){if(this.#jn&&e===this.#Wn)return!0;const t=this.#Kn(e);if(!t)return!1;if(this.#Vn=this.#Qn(t),!this.#Vn)return!1;const r=await this.#J.getStyleSheetText(this.#Vn);return!!r&&(this.#jn=r,this.#Wn=e,!0)}async toggleEmulatedOverlay(e){if(this.#Vn&&this.#jn)if(e){const e=Gr.#$n(this.#Gn.selectedPlatform.toLowerCase(),this.#jn);e&&await this.#J.setStyleSheetText(this.#Vn,e,!1)}else await this.#J.setStyleSheetText(this.#Vn,this.#jn,!1)}static#$n(e,t){const r=Vr[e];return Gr.#Xn(r.x,r.y,r.width,r.height,t)}#Kn(t){const r=e.ParsedURL.ParsedURL.extractOrigin(t),n=this.#J.styleSheetHeaders().find((e=>e.sourceURL&&e.sourceURL.includes(r)));return n?.sourceURL}#Qn(e){const t=this.#J.getStyleSheetIdsForURL(e);return t.length>0?t[0]:void 0}static#Xn(e,t,r,n,s){if(!s)return;return s.replace(/: env\(titlebar-area-x(?:,[^)]*)?\);/g,`: env(titlebar-area-x, ${e}px);`).replace(/: env\(titlebar-area-y(?:,[^)]*)?\);/g,`: env(titlebar-area-y, ${t}px);`).replace(/: env\(titlebar-area-width(?:,[^)]*)?\);/g,`: env(titlebar-area-width, calc(100% - ${r}px));`).replace(/: env\(titlebar-area-height(?:,[^)]*)?\);/g,`: env(titlebar-area-height, ${n}px);`)}transformStyleSheetforTesting(e,t,r,n,s){return Gr.#Xn(e,t,r,n,s)}}class Kr{#un;constructor(e){this.#un=e}highlightInOverlay(e,t){const{node:r,deferredNode:n,object:s,selectorList:i}={node:void 0,deferredNode:void 0,object:void 0,selectorList:void 0,...e},a=r?r.id:void 0,o=n?n.backendNodeId():void 0,l=s?s.objectId:void 0;a||o||l?this.#un.target().overlayAgent().invoke_highlightNode({highlightConfig:t,nodeId:a,backendNodeId:o,objectId:l,selector:i}):this.#un.target().overlayAgent().invoke_hideHighlight()}async setInspectMode(e,t){await this.#un.target().overlayAgent().invoke_setInspectMode({mode:e,highlightConfig:t})}highlightFrame(t){this.#un.target().overlayAgent().invoke_highlightFrame({frameId:t,contentColor:e.Color.PageHighlight.Content.toProtocolRGBA(),contentOutlineColor:e.Color.PageHighlight.ContentOutline.toProtocolRGBA()})}}class Qr{#un;constructor(e){this.#un=e}highlightSourceOrderInOverlay(e,t){this.#un.setSourceOrderActive(!0),this.#un.setShowViewportSizeOnResize(!1),this.#un.getOverlayAgent().invoke_highlightSourceOrder({sourceOrderConfig:t,nodeId:e.id})}hideSourceOrderHighlight(){this.#un.setSourceOrderActive(!1),this.#un.setShowViewportSizeOnResize(!0),this.#un.clearHighlight()}}c.register(Wr,{capabilities:2,autostart:!0});var $r,Xr=Object.freeze({__proto__:null,OverlayModel:Wr,WindowControls:Gr,SourceOrderHighlighter:Qr});class Jr{#Jn;#Yn;ownerDocument;#Zn;id;index;#es;#ts;#rs;#ns;nodeValueInternal;#ss;#is;#as;#os;#ls;#ds;#cs;#hs;#us;assignedSlot;shadowRootsInternal;#gs;#ps;#ms;childNodeCountInternal;childrenInternal;nextSibling;previousSibling;firstChild;lastChild;parentNode;templateContentInternal;contentDocumentInternal;childDocumentPromiseForTesting;#fs;publicId;systemId;internalSubset;name;value;constructor(e){this.#Jn=e,this.#Yn=this.#Jn.getAgent(),this.index=void 0,this.#cs=null,this.#hs=new Map,this.#us=[],this.assignedSlot=null,this.shadowRootsInternal=[],this.#gs=new Map,this.#ps=new Map,this.#ms=0,this.childrenInternal=null,this.nextSibling=null,this.previousSibling=null,this.firstChild=null,this.lastChild=null,this.parentNode=null}static create(e,t,r,n){const s=new Jr(e);return s.init(t,r,n),s}init(e,t,r){if(this.#Yn=this.#Jn.getAgent(),this.ownerDocument=e,this.#Zn=t,this.id=r.nodeId,this.#es=r.backendNodeId,this.#Jn.registerNode(this),this.#ts=r.nodeType,this.#rs=r.nodeName,this.#ns=r.localName,this.nodeValueInternal=r.nodeValue,this.#ss=r.pseudoType,this.#is=r.pseudoIdentifier,this.#as=r.shadowRootType,this.#os=r.frameId||null,this.#ls=r.xmlVersion,this.#ds=Boolean(r.isSVG),r.attributes&&this.setAttributesPayload(r.attributes),this.childNodeCountInternal=r.childNodeCount||0,r.shadowRoots)for(let e=0;e<r.shadowRoots.length;++e){const t=r.shadowRoots[e],n=Jr.create(this.#Jn,this.ownerDocument,!0,t);this.shadowRootsInternal.push(n),n.parentNode=this}r.templateContent&&(this.templateContentInternal=Jr.create(this.#Jn,this.ownerDocument,!0,r.templateContent),this.templateContentInternal.parentNode=this,this.childrenInternal=[]);const n=new Set(["EMBED","IFRAME","OBJECT","PORTAL","FENCEDFRAME"]);r.contentDocument?(this.contentDocumentInternal=new en(this.#Jn,r.contentDocument),this.contentDocumentInternal.parentNode=this,this.childrenInternal=[]):r.frameId&&n.has(r.nodeName)&&(this.childDocumentPromiseForTesting=this.requestChildDocument(r.frameId,this.#Jn.target()),this.childrenInternal=[]),r.importedDocument&&(this.#fs=Jr.create(this.#Jn,this.ownerDocument,!0,r.importedDocument),this.#fs.parentNode=this,this.childrenInternal=[]),r.distributedNodes&&this.setDistributedNodePayloads(r.distributedNodes),r.assignedSlot&&this.setAssignedSlot(r.assignedSlot),r.children&&this.setChildrenPayload(r.children),this.setPseudoElements(r.pseudoElements),this.#ts===Node.ELEMENT_NODE?(this.ownerDocument&&!this.ownerDocument.documentElement&&"HTML"===this.#rs&&(this.ownerDocument.documentElement=this),this.ownerDocument&&!this.ownerDocument.body&&"BODY"===this.#rs&&(this.ownerDocument.body=this)):this.#ts===Node.DOCUMENT_TYPE_NODE?(this.publicId=r.publicId,this.systemId=r.systemId,this.internalSubset=r.internalSubset):this.#ts===Node.ATTRIBUTE_NODE&&(this.name=r.name,this.value=r.value)}async requestChildDocument(e,t){const r=await Ge.instance().getOrWaitForFrame(e,t),n=r.resourceTreeModel()?.target().model(tn);return n?.requestDocument()||null}isAdFrameNode(){if(this.isIframe()&&this.#os){const e=Ge.instance().getFrame(this.#os);return!!e&&"none"!==e.adFrameType()}return!1}isSVGNode(){return this.#ds}isMediaNode(){return"AUDIO"===this.#rs||"VIDEO"===this.#rs}isViewTransitionPseudoNode(){return!!this.#ss&&["view-transition","view-transition-group","view-transition-image-pair","view-transition-old","view-transition-new"].includes(this.#ss)}creationStackTrace(){if(this.#cs)return this.#cs;const e=this.#Yn.invoke_getNodeStackTraces({nodeId:this.id});return this.#cs=e.then((e=>e.creation||null)),this.#cs}get subtreeMarkerCount(){return this.#ms}domModel(){return this.#Jn}backendNodeId(){return this.#es}children(){return this.childrenInternal?this.childrenInternal.slice():null}setChildren(e){this.childrenInternal=e}hasAttributes(){return this.#gs.size>0}childNodeCount(){return this.childNodeCountInternal}setChildNodeCount(e){this.childNodeCountInternal=e}hasShadowRoots(){return Boolean(this.shadowRootsInternal.length)}shadowRoots(){return this.shadowRootsInternal.slice()}templateContent(){return this.templateContentInternal||null}contentDocument(){return this.contentDocumentInternal||null}setContentDocument(e){this.contentDocumentInternal=e}isIframe(){return"IFRAME"===this.#rs}isPortal(){return"PORTAL"===this.#rs}importedDocument(){return this.#fs||null}nodeType(){return this.#ts}nodeName(){return this.#rs}pseudoType(){return this.#ss}pseudoIdentifier(){return this.#is}hasPseudoElements(){return this.#hs.size>0}pseudoElements(){return this.#hs}beforePseudoElement(){return this.#hs.get("before")?.at(-1)}afterPseudoElement(){return this.#hs.get("after")?.at(-1)}markerPseudoElement(){return this.#hs.get("marker")?.at(-1)}backdropPseudoElement(){return this.#hs.get("backdrop")?.at(-1)}viewTransitionPseudoElements(){return[...this.#hs.get("view-transition")||[],...this.#hs.get("view-transition-group")||[],...this.#hs.get("view-transition-image-pair")||[],...this.#hs.get("view-transition-old")||[],...this.#hs.get("view-transition-new")||[]]}hasAssignedSlot(){return null!==this.assignedSlot}isInsertionPoint(){return!this.isXMLNode()&&("SHADOW"===this.#rs||"CONTENT"===this.#rs||"SLOT"===this.#rs)}distributedNodes(){return this.#us}isInShadowTree(){return this.#Zn}ancestorShadowHost(){const e=this.ancestorShadowRoot();return e?e.parentNode:null}ancestorShadowRoot(){if(!this.#Zn)return null;let e=this;for(;e&&!e.isShadowRoot();)e=e.parentNode;return e}ancestorUserAgentShadowRoot(){const e=this.ancestorShadowRoot();return e&&e.shadowRootType()===Jr.ShadowRootTypes.UserAgent?e:null}isShadowRoot(){return Boolean(this.#as)}shadowRootType(){return this.#as||null}nodeNameInCorrectCase(){const e=this.shadowRootType();return e?"#shadow-root ("+e+")":this.localName()?this.localName().length!==this.nodeName().length?this.nodeName():this.localName():this.nodeName()}setNodeName(e,t){this.#Yn.invoke_setNodeName({nodeId:this.id,name:e}).then((e=>{e.getError()||this.#Jn.markUndoableState(),t&&t(e.getError()||null,this.#Jn.nodeForId(e.nodeId))}))}localName(){return this.#ns}nodeValue(){return this.nodeValueInternal}setNodeValueInternal(e){this.nodeValueInternal=e}setNodeValue(e,t){this.#Yn.invoke_setNodeValue({nodeId:this.id,value:e}).then((e=>{e.getError()||this.#Jn.markUndoableState(),t&&t(e.getError()||null)}))}getAttribute(e){const t=this.#gs.get(e);return t?t.value:void 0}setAttribute(e,t,r){this.#Yn.invoke_setAttributesAsText({nodeId:this.id,text:t,name:e}).then((e=>{e.getError()||this.#Jn.markUndoableState(),r&&r(e.getError()||null)}))}setAttributeValue(e,t,r){this.#Yn.invoke_setAttributeValue({nodeId:this.id,name:e,value:t}).then((e=>{e.getError()||this.#Jn.markUndoableState(),r&&r(e.getError()||null)}))}setAttributeValuePromise(e,t){return new Promise((r=>this.setAttributeValue(e,t,r)))}attributes(){return[...this.#gs.values()]}async removeAttribute(e){(await this.#Yn.invoke_removeAttribute({nodeId:this.id,name:e})).getError()||(this.#gs.delete(e),this.#Jn.markUndoableState())}getChildNodes(e){this.childrenInternal?e(this.children()):this.#Yn.invoke_requestChildNodes({nodeId:this.id}).then((t=>{e(t.getError()?null:this.children())}))}async getSubtree(e,t){return(await this.#Yn.invoke_requestChildNodes({nodeId:this.id,depth:e,pierce:t})).getError()?null:this.childrenInternal}async getOuterHTML(){const{outerHTML:e}=await this.#Yn.invoke_getOuterHTML({nodeId:this.id});return e}setOuterHTML(e,t){this.#Yn.invoke_setOuterHTML({nodeId:this.id,outerHTML:e}).then((e=>{e.getError()||this.#Jn.markUndoableState(),t&&t(e.getError()||null)}))}removeNode(e){return this.#Yn.invoke_removeNode({nodeId:this.id}).then((t=>{t.getError()||this.#Jn.markUndoableState(),e&&e(t.getError()||null)}))}async copyNode(){const{outerHTML:e}=await this.#Yn.invoke_getOuterHTML({nodeId:this.id});return null!==e&&o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(e),e}path(){function e(e){return(void 0!==e.index||e.isShadowRoot()&&e.parentNode)&&e.#rs.length}const t=[];let r=this;for(;r&&e(r);){const e="number"==typeof r.index?r.index:r.shadowRootType()===Jr.ShadowRootTypes.UserAgent?"u":"a";t.push([e,r.#rs]),r=r.parentNode}return t.reverse(),t.join(",")}isAncestor(e){if(!e)return!1;let t=e.parentNode;for(;t;){if(this===t)return!0;t=t.parentNode}return!1}isDescendant(e){return null!==e&&e.isAncestor(this)}frameOwnerFrameId(){return this.#os}frameId(){let e=this.parentNode||this;for(;!e.#os&&e.parentNode;)e=e.parentNode;return e.#os}setAttributesPayload(e){let t=!this.#gs||e.length!==2*this.#gs.size;const r=this.#gs||new Map;this.#gs=new Map;for(let n=0;n<e.length;n+=2){const s=e[n],i=e[n+1];if(this.addAttribute(s,i),t)continue;const a=r.get(s);a&&a.value===i||(t=!0)}return t}insertChild(e,t){if(!this.childrenInternal)throw new Error("DOMNode._children is expected to not be null.");const r=Jr.create(this.#Jn,this.ownerDocument,this.#Zn,t);return this.childrenInternal.splice(e?this.childrenInternal.indexOf(e)+1:0,0,r),this.renumber(),r}removeChild(e){const t=e.pseudoType();if(t){const r=this.#hs.get(t)?.filter((t=>t!==e));r&&r.length>0?this.#hs.set(t,r):this.#hs.delete(t)}else{const t=this.shadowRootsInternal.indexOf(e);if(-1!==t)this.shadowRootsInternal.splice(t,1);else{if(!this.childrenInternal)throw new Error("DOMNode._children is expected to not be null.");if(-1===this.childrenInternal.indexOf(e))throw new Error("DOMNode._children is expected to contain the node to be removed.");this.childrenInternal.splice(this.childrenInternal.indexOf(e),1)}}e.parentNode=null,this.#ms-=e.#ms,e.#ms&&this.#Jn.dispatchEventToListeners($r.MarkersChanged,this),this.renumber()}setChildrenPayload(e){this.childrenInternal=[];for(let t=0;t<e.length;++t){const r=e[t],n=Jr.create(this.#Jn,this.ownerDocument,this.#Zn,r);this.childrenInternal.push(n)}this.renumber()}setPseudoElements(e){if(e)for(let t=0;t<e.length;++t){const r=Jr.create(this.#Jn,this.ownerDocument,this.#Zn,e[t]);r.parentNode=this;const n=r.pseudoType();if(!n)throw new Error("DOMNode.pseudoType() is expected to be defined.");const s=this.#hs.get(n);s?s.push(r):this.#hs.set(n,[r])}}setDistributedNodePayloads(e){this.#us=[];for(const t of e)this.#us.push(new Zr(this.#Jn.target(),t.backendNodeId,t.nodeType,t.nodeName))}setAssignedSlot(e){this.assignedSlot=new Zr(this.#Jn.target(),e.backendNodeId,e.nodeType,e.nodeName)}renumber(){if(!this.childrenInternal)throw new Error("DOMNode._children is expected to not be null.");if(this.childNodeCountInternal=this.childrenInternal.length,0===this.childNodeCountInternal)return this.firstChild=null,void(this.lastChild=null);this.firstChild=this.childrenInternal[0],this.lastChild=this.childrenInternal[this.childNodeCountInternal-1];for(let e=0;e<this.childNodeCountInternal;++e){const t=this.childrenInternal[e];t.index=e,t.nextSibling=e+1<this.childNodeCountInternal?this.childrenInternal[e+1]:null,t.previousSibling=e-1>=0?this.childrenInternal[e-1]:null,t.parentNode=this}}addAttribute(e,t){const r={name:e,value:t,_node:this};this.#gs.set(e,r)}setAttributeInternal(e,t){const r=this.#gs.get(e);r?r.value=t:this.addAttribute(e,t)}removeAttributeInternal(e){this.#gs.delete(e)}copyTo(e,t,r){this.#Yn.invoke_copyTo({nodeId:this.id,targetNodeId:e.id,insertBeforeNodeId:t?t.id:void 0}).then((e=>{e.getError()||this.#Jn.markUndoableState(),r&&r(e.getError()||null,this.#Jn.nodeForId(e.nodeId))}))}moveTo(e,t,r){this.#Yn.invoke_moveTo({nodeId:this.id,targetNodeId:e.id,insertBeforeNodeId:t?t.id:void 0}).then((e=>{e.getError()||this.#Jn.markUndoableState(),r&&r(e.getError()||null,this.#Jn.nodeForId(e.nodeId))}))}isXMLNode(){return Boolean(this.#ls)}setMarker(e,t){if(null!==t){if(this.parentNode&&!this.#ps.has(e))for(let e=this;e;e=e.parentNode)++e.#ms;this.#ps.set(e,t);for(let e=this;e;e=e.parentNode)this.#Jn.dispatchEventToListeners($r.MarkersChanged,e)}else{if(!this.#ps.has(e))return;this.#ps.delete(e);for(let e=this;e;e=e.parentNode)--e.#ms;for(let e=this;e;e=e.parentNode)this.#Jn.dispatchEventToListeners($r.MarkersChanged,e)}}marker(e){return this.#ps.get(e)||null}getMarkerKeysForTest(){return[...this.#ps.keys()]}traverseMarkers(e){!function t(r){if(r.#ms){for(const t of r.#ps.keys())e(r,t);if(r.childrenInternal)for(const e of r.childrenInternal)t(e)}}(this)}resolveURL(t){if(!t)return t;for(let r=this;r;r=r.parentNode)if(r instanceof en&&r.baseURL)return e.ParsedURL.ParsedURL.completeURL(r.baseURL,t);return null}highlight(e){this.#Jn.overlayModel().highlightInOverlay({node:this,selectorList:void 0},e)}highlightForTwoSeconds(){this.#Jn.overlayModel().highlightInOverlayForTwoSeconds({node:this,selectorList:void 0})}async resolveToObject(e,t){const{object:r}=await this.#Yn.invoke_resolveNode({nodeId:this.id,backendNodeId:void 0,executionContextId:t,objectGroup:e});return r&&this.#Jn.runtimeModelInternal.createRemoteObject(r)||null}async boxModel(){const{model:e}=await this.#Yn.invoke_getBoxModel({nodeId:this.id});return e}async setAsInspectedNode(){let e=this;for(e&&e.pseudoType()&&(e=e.parentNode);e;){let t=e.ancestorUserAgentShadowRoot();if(!t)break;if(t=e.ancestorShadowHost(),!t)break;e=t}if(!e)throw new Error("In DOMNode.setAsInspectedNode: node is expected to not be null.");await this.#Yn.invoke_setInspectedNode({nodeId:e.id})}enclosingElementOrSelf(){let e=this;return e&&e.nodeType()===Node.TEXT_NODE&&e.parentNode&&(e=e.parentNode),e&&e.nodeType()!==Node.ELEMENT_NODE&&(e=null),e}async callFunction(e,t=[]){const r=await this.resolveToObject();if(!r)return null;const n=await r.callFunction(e,t.map((e=>Qe.toCallArgument(e))));return r.release(),n.wasThrown||!n.object?null:{value:n.object.value}}async scrollIntoView(){const e=this.enclosingElementOrSelf();if(!e)return;await e.callFunction((function(){this.scrollIntoViewIfNeeded(!0)}))&&e.highlightForTwoSeconds()}async focus(){const e=this.enclosingElementOrSelf();if(!e)throw new Error("DOMNode.focus expects node to not be null.");await e.callFunction((function(){this.focus()}))&&(e.highlightForTwoSeconds(),await this.#Jn.target().pageAgent().invoke_bringToFront())}simpleSelector(){const e=this.localName()||this.nodeName().toLowerCase();if(this.nodeType()!==Node.ELEMENT_NODE)return e;const t=this.getAttribute("type"),r=this.getAttribute("id"),n=this.getAttribute("class");if("input"===e&&t&&!r&&!n)return e+'[type="'+CSS.escape(t)+'"]';if(r)return e+"#"+CSS.escape(r);if(n){return("div"===e?"":e)+"."+n.trim().split(/\s+/g).map((e=>CSS.escape(e))).join(".")}return this.pseudoIdentifier()?`${e}(${this.pseudoIdentifier()})`:e}}!function(e){let t;!function(e){e.UserAgent="user-agent",e.Open="open",e.Closed="closed"}(t=e.ShadowRootTypes||(e.ShadowRootTypes={}))}(Jr||(Jr={}));class Yr{#Jn;#es;constructor(e,t){this.#Jn=e.model(tn),this.#es=t}resolve(e){this.resolvePromise().then(e)}async resolvePromise(){const e=await this.#Jn.pushNodesByBackendIdsToFrontend(new Set([this.#es]));return e&&e.get(this.#es)||null}backendNodeId(){return this.#es}domModel(){return this.#Jn}highlight(){this.#Jn.overlayModel().highlightInOverlay({deferredNode:this,selectorList:void 0})}}class Zr{nodeType;nodeName;deferredNode;constructor(e,t,r,n){this.nodeType=r,this.nodeName=n,this.deferredNode=new Yr(e,t)}}class en extends Jr{body;documentElement;documentURL;baseURL;constructor(e,t){super(e),this.body=null,this.documentElement=null,this.init(this,!1,t),this.documentURL=t.documentURL||"",this.baseURL=t.baseURL||""}}class tn extends c{agent;idToDOMNode=new Map;#bs;#ys;runtimeModelInternal;#vs;#Is;#ks;#ws;#Ss;constructor(e){super(e),this.agent=e.domAgent(),this.#bs=null,this.#ys=new Set,e.registerDOMDispatcher(new rn(this)),this.runtimeModelInternal=e.model(ar),this.#Is=null,e.suspended()||this.agent.invoke_enable({}),a.Runtime.experiments.isEnabled("capture-node-creation-stacks")&&this.agent.invoke_setNodeStackTracesEnabled({enable:!0})}runtimeModel(){return this.runtimeModelInternal}cssModel(){return this.target().model(Kt)}overlayModel(){return this.target().model(Wr)}static cancelSearch(){for(const e of je.instance().models(tn))e.cancelSearch()}scheduleMutationEvent(e){this.hasEventListeners($r.DOMMutated)&&(this.#vs=(this.#vs||0)+1,Promise.resolve().then(function(e,t){if(!this.hasEventListeners($r.DOMMutated)||this.#vs!==t)return;this.dispatchEventToListeners($r.DOMMutated,e)}.bind(this,e,this.#vs)))}requestDocument(){return this.#bs?Promise.resolve(this.#bs):(this.#Is||(this.#Is=this.requestDocumentInternal()),this.#Is)}async getOwnerNodeForFrame(e){const t=await this.agent.invoke_getFrameOwner({frameId:e});return t.getError()?null:new Yr(this.target(),t.backendNodeId)}async requestDocumentInternal(){const e=await this.agent.invoke_getDocument({});if(e.getError())return null;const{root:t}=e;if(this.#Is=null,t&&this.setDocument(t),!this.#bs)return console.error("No document"),null;const r=this.parentModel();if(r&&!this.#ks){await r.requestDocument();const e=this.target().model(mn)?.mainFrame;if(e){const t=await r.agent.invoke_getFrameOwner({frameId:e.id});!t.getError()&&t.nodeId&&(this.#ks=r.nodeForId(t.nodeId))}}if(this.#ks){const e=this.#ks.contentDocument();this.#ks.setContentDocument(this.#bs),this.#ks.setChildren([]),this.#bs?(this.#bs.parentNode=this.#ks,this.dispatchEventToListeners($r.NodeInserted,this.#bs)):e&&this.dispatchEventToListeners($r.NodeRemoved,{node:e,parent:this.#ks})}return this.#bs}existingDocument(){return this.#bs}async pushNodeToFrontend(e){await this.requestDocument();const{nodeId:t}=await this.agent.invoke_requestNode({objectId:e});return t?this.nodeForId(t):null}pushNodeByPathToFrontend(e){return this.requestDocument().then((()=>this.agent.invoke_pushNodeByPathToFrontend({path:e}))).then((({nodeId:e})=>e))}async pushNodesByBackendIdsToFrontend(e){await this.requestDocument();const t=[...e],{nodeIds:r}=await this.agent.invoke_pushNodesByBackendIdsToFrontend({backendNodeIds:t});if(!r)return null;const n=new Map;for(let e=0;e<r.length;++e)r[e]&&n.set(t[e],this.nodeForId(r[e]));return n}attributeModified(e,t,r){const n=this.idToDOMNode.get(e);n&&(n.setAttributeInternal(t,r),this.dispatchEventToListeners($r.AttrModified,{node:n,name:t}),this.scheduleMutationEvent(n))}attributeRemoved(e,t){const r=this.idToDOMNode.get(e);r&&(r.removeAttributeInternal(t),this.dispatchEventToListeners($r.AttrRemoved,{node:r,name:t}),this.scheduleMutationEvent(r))}inlineStyleInvalidated(e){t.SetUtilities.addAll(this.#ys,e),this.#ws||(this.#ws=window.setTimeout(this.loadNodeAttributes.bind(this),20))}loadNodeAttributes(){this.#ws=void 0;for(const e of this.#ys)this.agent.invoke_getAttributes({nodeId:e}).then((({attributes:t})=>{if(!t)return;const r=this.idToDOMNode.get(e);r&&r.setAttributesPayload(t)&&(this.dispatchEventToListeners($r.AttrModified,{node:r,name:"style"}),this.scheduleMutationEvent(r))}));this.#ys.clear()}characterDataModified(e,t){const r=this.idToDOMNode.get(e);r?(r.setNodeValueInternal(t),this.dispatchEventToListeners($r.CharacterDataModified,r),this.scheduleMutationEvent(r)):console.error("nodeId could not be resolved to a node")}nodeForId(e){return e&&this.idToDOMNode.get(e)||null}documentUpdated(){const e=Boolean(this.#bs);this.setDocument(null),this.parentModel()&&e&&!this.#Is&&this.requestDocument()}setDocument(e){this.idToDOMNode=new Map,this.#bs=e&&"nodeId"in e?new en(this,e):null,sn.instance().dispose(this),this.parentModel()||this.dispatchEventToListeners($r.DocumentUpdated,this)}setDocumentForTest(e){this.setDocument(e)}setDetachedRoot(e){"#document"===e.nodeName?new en(this,e):Jr.create(this,null,!1,e)}setChildNodes(e,t){if(!e&&t.length)return void this.setDetachedRoot(t[0]);const r=this.idToDOMNode.get(e);r?.setChildrenPayload(t)}childNodeCountUpdated(e,t){const r=this.idToDOMNode.get(e);r?(r.setChildNodeCount(t),this.dispatchEventToListeners($r.ChildNodeCountUpdated,r),this.scheduleMutationEvent(r)):console.error("nodeId could not be resolved to a node")}childNodeInserted(e,t,r){const n=this.idToDOMNode.get(e),s=this.idToDOMNode.get(t);if(!n)return void console.error("parentId could not be resolved to a node");const i=n.insertChild(s,r);this.idToDOMNode.set(i.id,i),this.dispatchEventToListeners($r.NodeInserted,i),this.scheduleMutationEvent(i)}childNodeRemoved(e,t){const r=this.idToDOMNode.get(e),n=this.idToDOMNode.get(t);r&&n?(r.removeChild(n),this.unbind(n),this.dispatchEventToListeners($r.NodeRemoved,{node:n,parent:r}),this.scheduleMutationEvent(n)):console.error("parentId or nodeId could not be resolved to a node")}shadowRootPushed(e,t){const r=this.idToDOMNode.get(e);if(!r)return;const n=Jr.create(this,r.ownerDocument,!0,t);n.parentNode=r,this.idToDOMNode.set(n.id,n),r.shadowRootsInternal.unshift(n),this.dispatchEventToListeners($r.NodeInserted,n),this.scheduleMutationEvent(n)}shadowRootPopped(e,t){const r=this.idToDOMNode.get(e);if(!r)return;const n=this.idToDOMNode.get(t);n&&(r.removeChild(n),this.unbind(n),this.dispatchEventToListeners($r.NodeRemoved,{node:n,parent:r}),this.scheduleMutationEvent(n))}pseudoElementAdded(e,t){const r=this.idToDOMNode.get(e);if(!r)return;const n=Jr.create(this,r.ownerDocument,!1,t);n.parentNode=r,this.idToDOMNode.set(n.id,n);const s=n.pseudoType();if(!s)throw new Error("DOMModel._pseudoElementAdded expects pseudoType to be defined.");const i=r.pseudoElements().get(s);if(i){if(!s.startsWith("view-transition"))throw new Error("DOMModel.pseudoElementAdded expects parent to not already have this pseudo type added; only view-transition* pseudo elements can coexist under the same parent.");i.push(n)}else r.pseudoElements().set(s,[n]);this.dispatchEventToListeners($r.NodeInserted,n),this.scheduleMutationEvent(n)}topLayerElementsUpdated(){this.dispatchEventToListeners($r.TopLayerElementsChanged)}pseudoElementRemoved(e,t){const r=this.idToDOMNode.get(e);if(!r)return;const n=this.idToDOMNode.get(t);n&&(r.removeChild(n),this.unbind(n),this.dispatchEventToListeners($r.NodeRemoved,{node:n,parent:r}),this.scheduleMutationEvent(n))}distributedNodesUpdated(e,t){const r=this.idToDOMNode.get(e);r&&(r.setDistributedNodePayloads(t),this.dispatchEventToListeners($r.DistributedNodesChanged,r),this.scheduleMutationEvent(r))}unbind(e){this.idToDOMNode.delete(e.id);const t=e.children();for(let e=0;t&&e<t.length;++e)this.unbind(t[e]);for(let t=0;t<e.shadowRootsInternal.length;++t)this.unbind(e.shadowRootsInternal[t]);const r=e.pseudoElements();for(const e of r.values())for(const t of e)this.unbind(t);const n=e.templateContent();n&&this.unbind(n)}async getNodesByStyle(e,t=!1){if(await this.requestDocument(),!this.#bs)throw new Error("DOMModel.getNodesByStyle expects to have a document.");const r=await this.agent.invoke_getNodesForSubtreeByStyle({nodeId:this.#bs.id,computedStyles:e,pierce:t});if(r.getError())throw r.getError();return r.nodeIds}async performSearch(e,t){const r=await this.agent.invoke_performSearch({query:e,includeUserAgentShadowDOM:t});return r.getError()||(this.#Ss=r.searchId),r.getError()?0:r.resultCount}async searchResult(e){if(!this.#Ss)return null;const{nodeIds:t}=await this.agent.invoke_getSearchResults({searchId:this.#Ss,fromIndex:e,toIndex:e+1});return t&&1===t.length?this.nodeForId(t[0]):null}cancelSearch(){this.#Ss&&(this.agent.invoke_discardSearchResults({searchId:this.#Ss}),this.#Ss=void 0)}classNamesPromise(e){return this.agent.invoke_collectClassNamesFromSubtree({nodeId:e}).then((({classNames:e})=>e||[]))}querySelector(e,t){return this.agent.invoke_querySelector({nodeId:e,selector:t}).then((({nodeId:e})=>e))}querySelectorAll(e,t){return this.agent.invoke_querySelectorAll({nodeId:e,selector:t}).then((({nodeIds:e})=>e))}getTopLayerElements(){return this.agent.invoke_getTopLayerElements().then((({nodeIds:e})=>e))}markUndoableState(e){sn.instance().markUndoableState(this,e||!1)}async nodeForLocation(e,t,r){const n=await this.agent.invoke_getNodeForLocation({x:e,y:t,includeUserAgentShadowDOM:r});return n.getError()||!n.nodeId?null:this.nodeForId(n.nodeId)}async getContainerForNode(e,t,r,n){const{nodeId:s}=await this.agent.invoke_getContainerForNode({nodeId:e,containerName:t,physicalAxes:r,logicalAxes:n});return s?this.nodeForId(s):null}pushObjectAsNodeToFrontend(e){return e.isNode()&&e.objectId?this.pushNodeToFrontend(e.objectId):Promise.resolve(null)}suspendModel(){return this.agent.invoke_disable().then((()=>this.setDocument(null)))}async resumeModel(){await this.agent.invoke_enable({})}dispose(){sn.instance().dispose(this)}parentModel(){const e=this.target().parentTarget();return e?e.model(tn):null}getAgent(){return this.agent}registerNode(e){this.idToDOMNode.set(e.id,e)}}!function(e){e.AttrModified="AttrModified",e.AttrRemoved="AttrRemoved",e.CharacterDataModified="CharacterDataModified",e.DOMMutated="DOMMutated",e.NodeInserted="NodeInserted",e.NodeRemoved="NodeRemoved",e.DocumentUpdated="DocumentUpdated",e.ChildNodeCountUpdated="ChildNodeCountUpdated",e.DistributedNodesChanged="DistributedNodesChanged",e.MarkersChanged="MarkersChanged",e.TopLayerElementsChanged="TopLayerElementsChanged"}($r||($r={}));class rn{#ir;constructor(e){this.#ir=e}documentUpdated(){this.#ir.documentUpdated()}attributeModified({nodeId:e,name:t,value:r}){this.#ir.attributeModified(e,t,r)}attributeRemoved({nodeId:e,name:t}){this.#ir.attributeRemoved(e,t)}inlineStyleInvalidated({nodeIds:e}){this.#ir.inlineStyleInvalidated(e)}characterDataModified({nodeId:e,characterData:t}){this.#ir.characterDataModified(e,t)}setChildNodes({parentId:e,nodes:t}){this.#ir.setChildNodes(e,t)}childNodeCountUpdated({nodeId:e,childNodeCount:t}){this.#ir.childNodeCountUpdated(e,t)}childNodeInserted({parentNodeId:e,previousNodeId:t,node:r}){this.#ir.childNodeInserted(e,t,r)}childNodeRemoved({parentNodeId:e,nodeId:t}){this.#ir.childNodeRemoved(e,t)}shadowRootPushed({hostId:e,root:t}){this.#ir.shadowRootPushed(e,t)}shadowRootPopped({hostId:e,rootId:t}){this.#ir.shadowRootPopped(e,t)}pseudoElementAdded({parentId:e,pseudoElement:t}){this.#ir.pseudoElementAdded(e,t)}pseudoElementRemoved({parentId:e,pseudoElementId:t}){this.#ir.pseudoElementRemoved(e,t)}distributedNodesUpdated({insertionPointId:e,distributedNodes:t}){this.#ir.distributedNodesUpdated(e,t)}topLayerElementsUpdated(){this.#ir.topLayerElementsUpdated()}}let nn=null;class sn{#Cs;#hn;#Rs;constructor(){this.#Cs=[],this.#hn=0,this.#Rs=null}static instance(e={forceNew:null}){const{forceNew:t}=e;return nn&&!t||(nn=new sn),nn}async markUndoableState(e,t){this.#Rs&&e!==this.#Rs&&(this.#Rs.markUndoableState(),this.#Rs=null),t&&this.#Rs===e||(this.#Cs=this.#Cs.slice(0,this.#hn),this.#Cs.push(e),this.#hn=this.#Cs.length,t?this.#Rs=e:(await e.getAgent().invoke_markUndoableState(),this.#Rs=null))}async undo(){if(0===this.#hn)return Promise.resolve();--this.#hn,this.#Rs=null,await this.#Cs[this.#hn].getAgent().invoke_undo()}async redo(){if(this.#hn>=this.#Cs.length)return Promise.resolve();++this.#hn,this.#Rs=null,await this.#Cs[this.#hn-1].getAgent().invoke_redo()}dispose(e){let r=0;for(let t=0;t<this.#hn;++t)this.#Cs[t]===e&&++r;t.ArrayUtilities.removeElement(this.#Cs,e),this.#hn-=r,this.#Rs===e&&(this.#Rs=null)}}c.register(tn,{capabilities:2,autostart:!0});var an=Object.freeze({__proto__:null,get DOMNode(){return Jr},DeferredDOMNode:Yr,DOMNodeShortcut:Zr,DOMDocument:en,DOMModel:tn,get Events(){return $r},DOMModelUndoStack:sn});class on{#lr;#xs;#Ts;#Ms;#Ps;#Ls;#As;#Es;#Os;#Ns;#Ds;#Fs;#Bs=null;#Us=null;constructor(r,n,s,i,a,o,l,d,c,h){this.#lr=r,this.#xs=n,this.url=s,this.#Ms=i,this.#Ps=a,this.#Ls=o,this.#As=l||e.ResourceType.resourceTypes.Other,this.#Es=d,this.#Os=!1,this.#Ns=c&&t.DateUtilities.isValid(c)?c:null,this.#Ds=h}lastModified(){if(this.#Ns||!this.#xs)return this.#Ns;const e=this.#xs.responseLastModified(),r=e?new Date(e):null;return this.#Ns=r&&t.DateUtilities.isValid(r)?r:null,this.#Ns}contentSize(){return"number"!=typeof this.#Ds&&this.#xs?this.#xs.resourceSize:this.#Ds}get request(){return this.#xs}get url(){return this.#Ts}set url(t){this.#Ts=t,this.#Fs=new e.ParsedURL.ParsedURL(t)}get parsedURL(){return this.#Fs}get documentURL(){return this.#Ms}get frameId(){return this.#Ps}get loaderId(){return this.#Ls}get displayName(){return this.#Fs?this.#Fs.displayName:""}resourceType(){return this.#xs?this.#xs.resourceType():this.#As}get mimeType(){return this.#xs?this.#xs.mimeType:this.#Es}get content(){return this.#Bs?.isTextContent?this.#Bs.text:this.#Bs?.base64??null}get isGenerated(){return this.#Os}set isGenerated(e){this.#Os=e}contentURL(){return this.#Ts}contentType(){return this.resourceType()===e.ResourceType.resourceTypes.Document&&-1!==this.mimeType.indexOf("javascript")?e.ResourceType.resourceTypes.Script:this.resourceType()}async requestContent(){const e=await this.requestContentData();return s.ContentData.ContentData.asDeferredContent(e)}async requestContentData(){return this.#Bs?this.#Bs:(this.#Us||(this.#Us=this.innerRequestContent().then((e=>(s.ContentData.ContentData.isError(e)||(this.#Bs=e),this.#Us=null,e)))),this.#Us)}canonicalMimeType(){return this.contentType().canonicalMimeType()||this.mimeType}async searchInContent(e,t,r){if(!this.frameId)return[];if(this.request)return this.request.searchInContent(e,t,r);const n=await this.#lr.target().pageAgent().invoke_searchInResource({frameId:this.frameId,url:this.url,query:e,caseSensitive:t,isRegex:r});return s.TextUtils.performSearchInSearchMatches(n.result||[],e,t,r)}async populateImageSource(e){const t=await this.requestContentData();s.ContentData.ContentData.isError(t)||(e.src=t.asDataUrl()??this.#Ts)}async innerRequestContent(){if(this.request)return this.request.contentData();const e=await this.#lr.target().pageAgent().invoke_getResourceContent({frameId:this.frameId,url:this.url}),t=e.getError();return t?{error:t}:new s.ContentData.ContentData(e.content,e.base64Encoded,this.mimeType)}hasTextContent(){return!!this.#Bs?.isTextContent||(this.#As.isTextType()||t.MimeType.isTextType(this.mimeType))}frame(){return this.#Ps?this.#lr.frameForId(this.#Ps):null}statusCode(){return this.#xs?this.#xs.statusCode:0}}var ln,dn=Object.freeze({__proto__:null,Resource:on});class cn extends c{#Hs;#qs;#_s;constructor(e){super(e),this.#Hs="",this.#qs="",this.#_s=new Set}updateSecurityOrigins(e){const t=this.#_s;this.#_s=e;for(const e of t)this.#_s.has(e)||this.dispatchEventToListeners(ln.SecurityOriginRemoved,e);for(const e of this.#_s)t.has(e)||this.dispatchEventToListeners(ln.SecurityOriginAdded,e)}securityOrigins(){return[...this.#_s]}mainSecurityOrigin(){return this.#Hs}unreachableMainSecurityOrigin(){return this.#qs}setMainSecurityOrigin(e,t){this.#Hs=e,this.#qs=t||null,this.dispatchEventToListeners(ln.MainSecurityOriginChanged,{mainSecurityOrigin:this.#Hs,unreachableMainSecurityOrigin:this.#qs})}}!function(e){e.SecurityOriginAdded="SecurityOriginAdded",e.SecurityOriginRemoved="SecurityOriginRemoved",e.MainSecurityOriginChanged="MainSecurityOriginChanged"}(ln||(ln={})),c.register(cn,{capabilities:0,autostart:!1});var hn=Object.freeze({__proto__:null,SecurityOriginManager:cn,get Events(){return ln}});class un extends c{#zs;#js;constructor(e){super(e),this.#zs="",this.#js=new Set}updateStorageKeys(e){const t=this.#js;this.#js=e;for(const e of t)this.#js.has(e)||this.dispatchEventToListeners("StorageKeyRemoved",e);for(const e of this.#js)t.has(e)||this.dispatchEventToListeners("StorageKeyAdded",e)}storageKeys(){return[...this.#js]}mainStorageKey(){return this.#zs}setMainStorageKey(e){this.#zs=e,this.dispatchEventToListeners("MainStorageKeyChanged",{mainStorageKey:this.#zs})}}c.register(un,{capabilities:0,autostart:!1});var gn,pn=Object.freeze({__proto__:null,StorageKeyManager:un,parseStorageKey:function(t){const r=t.split("^"),n={origin:e.ParsedURL.ParsedURL.extractOrigin(r[0]),components:new Map};for(let e=1;e<r.length;++e)n.components.set(r[e].charAt(0),r[e].substring(1));return n}});class mn extends c{agent;storageAgent;#Vs;#Ws;framesInternal;#Gs;#Ks;#Qs;isInterstitialShowing;mainFrame;#$s;constructor(e){super(e);const t=e.model(gt);t&&(t.addEventListener(pt.RequestFinished,this.onRequestFinished,this),t.addEventListener(pt.RequestUpdateDropped,this.onRequestUpdateDropped,this)),this.agent=e.pageAgent(),this.storageAgent=e.storageAgent(),this.agent.invoke_enable(),this.#Vs=e.model(cn),this.#Ws=e.model(un),this.#$s=new Set,e.registerPageDispatcher(new bn(this)),this.framesInternal=new Map,this.#Gs=!1,this.#Ks=null,this.#Qs=0,this.isInterstitialShowing=!1,this.mainFrame=null,this.#Xs()}async#Xs(){return this.agent.invoke_getResourceTree().then((e=>{this.processCachedResources(e.getError()?null:e.frameTree),this.mainFrame&&this.processPendingEvents(this.mainFrame)}))}static frameForRequest(e){const t=gt.forRequest(e),r=t?t.target().model(mn):null;return r&&e.frameId?r.frameForId(e.frameId):null}static frames(){const e=[];for(const t of je.instance().models(mn))e.push(...t.frames());return e}static resourceForURL(e){for(const t of je.instance().models(mn)){const r=t.mainFrame,n=r?r.resourceForURL(e):null;if(n)return n}return null}static reloadAllPages(e,t){for(const r of je.instance().models(mn))r.target().parentTarget()?.type()!==Ue.Frame&&r.reloadPage(e,t)}async storageKeyForFrame(e){if(!this.framesInternal.has(e))return null;const t=await this.storageAgent.invoke_getStorageKeyForFrame({frameId:e});return"Frame tree node for given frame not found"===t.getError()?null:t.storageKey}domModel(){return this.target().model(tn)}processCachedResources(e){e&&":"!==e.frame.url&&(this.dispatchEventToListeners(gn.WillLoadCachedResources),this.addFramesRecursively(null,e),this.target().setInspectedURL(e.frame.url)),this.#Gs=!0;const t=this.target().model(ar);t&&(t.setExecutionContextComparator(this.executionContextComparator.bind(this)),t.fireExecutionContextOrderChanged()),this.dispatchEventToListeners(gn.CachedResourcesLoaded,this)}cachedResourcesLoaded(){return this.#Gs}addFrame(e,t){this.framesInternal.set(e.id,e),e.isMainFrame()&&(this.mainFrame=e),this.dispatchEventToListeners(gn.FrameAdded,e),this.updateSecurityOrigins(),this.updateStorageKeys()}frameAttached(e,t,r){const n=t&&this.framesInternal.get(t)||null;if(!this.#Gs&&n)return null;if(this.framesInternal.has(e))return null;const s=new fn(this,n,e,null,r||null);return t&&!n&&(s.crossTargetParentFrameId=t),s.isMainFrame()&&this.mainFrame&&this.frameDetached(this.mainFrame.id,!1),this.addFrame(s,!0),s}frameNavigated(e,t){const r=e.parentId&&this.framesInternal.get(e.parentId)||null;if(!this.#Gs&&r)return;let n=this.framesInternal.get(e.id)||null;if(!n&&(n=this.frameAttached(e.id,e.parentId||null),console.assert(Boolean(n)),!n))return;this.dispatchEventToListeners(gn.FrameWillNavigate,n),n.navigate(e),t&&(n.backForwardCacheDetails.restoredFromCache="BackForwardCacheRestore"===t),n.isMainFrame()&&this.target().setInspectedURL(n.url),this.dispatchEventToListeners(gn.FrameNavigated,n),n.isPrimaryFrame()&&this.primaryPageChanged(n,"Navigation");const s=n.resources();for(let e=0;e<s.length;++e)this.dispatchEventToListeners(gn.ResourceAdded,s[e]);this.updateSecurityOrigins(),this.updateStorageKeys(),n.backForwardCacheDetails.restoredFromCache&&(Ge.instance().modelRemoved(this),Ge.instance().modelAdded(this),this.#Xs())}primaryPageChanged(e,t){this.processPendingEvents(e),this.dispatchEventToListeners(gn.PrimaryPageChanged,{frame:e,type:t});const r=this.target().model(gt);r&&e.isOutermostFrame()&&r.clearRequests()}documentOpened(t){this.frameNavigated(t,void 0);const r=this.framesInternal.get(t.id);if(r&&!r.getResourcesMap().get(t.url)){const n=this.createResourceFromFramePayload(t,t.url,e.ResourceType.resourceTypes.Document,t.mimeType,null,null);n.isGenerated=!0,r.addResource(n)}}frameDetached(e,t){if(!this.#Gs)return;const r=this.framesInternal.get(e);if(!r)return;const n=r.sameTargetParentFrame();n?n.removeChildFrame(r,t):r.remove(t),this.updateSecurityOrigins(),this.updateStorageKeys()}onRequestFinished(e){if(!this.#Gs)return;const t=e.data;if(t.failed)return;const r=t.frameId?this.framesInternal.get(t.frameId):null;r&&r.addRequest(t)}onRequestUpdateDropped(t){if(!this.#Gs)return;const r=t.data,n=r.frameId;if(!n)return;const s=this.framesInternal.get(n);if(!s)return;const i=r.url;if(s.getResourcesMap().get(i))return;const a=new on(this,null,i,s.url,n,r.loaderId,e.ResourceType.resourceTypes[r.resourceType],r.mimeType,r.lastModified,null);s.addResource(a)}frameForId(e){return this.framesInternal.get(e)||null}forAllResources(e){return!!this.mainFrame&&this.mainFrame.callForFrameResources(e)}frames(){return[...this.framesInternal.values()]}resourceForURL(e){return this.mainFrame?this.mainFrame.resourceForURL(e):null}addFramesRecursively(t,r){const n=r.frame;let s=this.framesInternal.get(n.id);s||(s=new fn(this,t,n.id,n,null)),!t&&n.parentId&&(s.crossTargetParentFrameId=n.parentId),this.addFrame(s);for(const e of r.childFrames||[])this.addFramesRecursively(s,e);for(let t=0;t<r.resources.length;++t){const i=r.resources[t],a=this.createResourceFromFramePayload(n,i.url,e.ResourceType.resourceTypes[i.type],i.mimeType,i.lastModified||null,i.contentSize||null);s.addResource(a)}if(!s.getResourcesMap().get(n.url)){const t=this.createResourceFromFramePayload(n,n.url,e.ResourceType.resourceTypes.Document,n.mimeType,null,null);s.addResource(t)}}createResourceFromFramePayload(e,t,r,n,s,i){const a="number"==typeof s?new Date(1e3*s):null;return new on(this,null,t,e.url,e.id,e.loaderId,r,n,a,i)}suspendReload(){this.#Qs++}resumeReload(){if(this.#Qs--,console.assert(this.#Qs>=0,"Unbalanced call to ResourceTreeModel.resumeReload()"),!this.#Qs&&this.#Ks){const{ignoreCache:e,scriptToEvaluateOnLoad:t}=this.#Ks;this.reloadPage(e,t)}}reloadPage(e,t){if(this.#Ks||this.dispatchEventToListeners(gn.PageReloadRequested,this),this.#Qs)return void(this.#Ks={ignoreCache:e,scriptToEvaluateOnLoad:t});this.#Ks=null;const r=this.target().model(gt);r&&r.clearRequests(),this.dispatchEventToListeners(gn.WillReloadPage),this.agent.invoke_reload({ignoreCache:e,scriptToEvaluateOnLoad:t})}navigate(e){return this.agent.invoke_navigate({url:e})}async navigationHistory(){const e=await this.agent.invoke_getNavigationHistory();return e.getError()?null:{currentIndex:e.currentIndex,entries:e.entries}}navigateToHistoryEntry(e){this.agent.invoke_navigateToHistoryEntry({entryId:e.id})}setLifecycleEventsEnabled(e){return this.agent.invoke_setLifecycleEventsEnabled({enabled:e})}async fetchAppManifest(){const e=await this.agent.invoke_getAppManifest();return e.getError()?{url:e.url,data:null,errors:[]}:{url:e.url,data:e.data||null,errors:e.errors}}async getInstallabilityErrors(){return(await this.agent.invoke_getInstallabilityErrors()).installabilityErrors||[]}async getAppId(){return this.agent.invoke_getAppId()}executionContextComparator(e,t){function r(e){let t=e;const r=[];for(;t;)r.push(t),t=t.sameTargetParentFrame();return r.reverse()}if(e.target()!==t.target())return cr.comparator(e,t);const n=e.frameId?r(this.frameForId(e.frameId)):[],s=t.frameId?r(this.frameForId(t.frameId)):[];let i,a;for(let e=0;;e++)if(!n[e]||!s[e]||n[e]!==s[e]){i=n[e],a=s[e];break}return!i&&a?-1:!a&&i?1:i&&a?i.id.localeCompare(a.id):cr.comparator(e,t)}getSecurityOriginData(){const t=new Set;let r=null,n=null;for(const s of this.framesInternal.values()){const i=s.securityOrigin;if(i&&(t.add(i),s.isMainFrame()&&(r=i,s.unreachableUrl()))){n=new e.ParsedURL.ParsedURL(s.unreachableUrl()).securityOrigin()}}return{securityOrigins:t,mainSecurityOrigin:r,unreachableMainSecurityOrigin:n}}async getStorageKeyData(){const e=new Set;let t=null;for(const{isMainFrame:r,storageKey:n}of await Promise.all([...this.framesInternal.values()].map((e=>e.getStorageKey(!1).then((t=>({isMainFrame:e.isMainFrame(),storageKey:t})))))))r&&(t=n),n&&e.add(n);return{storageKeys:e,mainStorageKey:t}}updateSecurityOrigins(){const e=this.getSecurityOriginData();this.#Vs.setMainSecurityOrigin(e.mainSecurityOrigin||"",e.unreachableMainSecurityOrigin||""),this.#Vs.updateSecurityOrigins(e.securityOrigins)}async updateStorageKeys(){const e=await this.getStorageKeyData();this.#Ws.setMainStorageKey(e.mainStorageKey||""),this.#Ws.updateStorageKeys(e.storageKeys)}async getMainStorageKey(){return this.mainFrame?this.mainFrame.getStorageKey(!1):null}getMainSecurityOrigin(){const e=this.getSecurityOriginData();return e.mainSecurityOrigin||e.unreachableMainSecurityOrigin}onBackForwardCacheNotUsed(e){this.mainFrame&&this.mainFrame.id===e.frameId&&this.mainFrame.loaderId===e.loaderId?(this.mainFrame.setBackForwardCacheDetails(e),this.dispatchEventToListeners(gn.BackForwardCacheDetailsUpdated,this.mainFrame)):this.#$s.add(e)}processPendingEvents(e){if(e.isMainFrame())for(const t of this.#$s)if(e.id===t.frameId&&e.loaderId===t.loaderId){e.setBackForwardCacheDetails(t),this.#$s.delete(t);break}}}!function(e){e.FrameAdded="FrameAdded",e.FrameNavigated="FrameNavigated",e.FrameDetached="FrameDetached",e.FrameResized="FrameResized",e.FrameWillNavigate="FrameWillNavigate",e.PrimaryPageChanged="PrimaryPageChanged",e.ResourceAdded="ResourceAdded",e.WillLoadCachedResources="WillLoadCachedResources",e.CachedResourcesLoaded="CachedResourcesLoaded",e.DOMContentLoaded="DOMContentLoaded",e.LifecycleEvent="LifecycleEvent",e.Load="Load",e.PageReloadRequested="PageReloadRequested",e.WillReloadPage="WillReloadPage",e.InterstitialShown="InterstitialShown",e.InterstitialHidden="InterstitialHidden",e.BackForwardCacheDetailsUpdated="BackForwardCacheDetailsUpdated",e.JavaScriptDialogOpening="JavaScriptDialogOpening"}(gn||(gn={}));class fn{#un;#Js;#Pe;crossTargetParentFrameId;#Ls;#h;#Ts;#Ys;#Zs;#ei;#ti;#ri;#ni;#si;#ii;#ai;#oi;#li;resourcesMap;backForwardCacheDetails={restoredFromCache:void 0,explanations:[],explanationsTree:void 0};constructor(e,r,n,s,i){this.#un=e,this.#Js=r,this.#Pe=n,this.crossTargetParentFrameId=null,this.#Ls=s?.loaderId||"",this.#h=s&&s.name,this.#Ts=s&&s.url||t.DevToolsPath.EmptyUrlString,this.#Ys=s&&s.domainAndRegistry||"",this.#Zs=s&&s.securityOrigin,this.#ti=s&&s.unreachableUrl||t.DevToolsPath.EmptyUrlString,this.#ri=s?.adFrameStatus,this.#ni=s&&s.secureContextType,this.#si=s&&s.crossOriginIsolatedContextType,this.#ii=s&&s.gatedAPIFeatures,this.#ai=i,this.#oi=null,this.#li=new Set,this.resourcesMap=new Map,this.#Js&&this.#Js.#li.add(this)}isSecureContext(){return null!==this.#ni&&this.#ni.startsWith("Secure")}getSecureContextType(){return this.#ni}isCrossOriginIsolated(){return null!==this.#si&&this.#si.startsWith("Isolated")}getCrossOriginIsolatedContextType(){return this.#si}getGatedAPIFeatures(){return this.#ii}getCreationStackTraceData(){return{creationStackTrace:this.#ai,creationStackTraceTarget:this.#oi||this.resourceTreeModel().target()}}navigate(e){this.#Ls=e.loaderId,this.#h=e.name,this.#Ts=e.url,this.#Ys=e.domainAndRegistry,this.#Zs=e.securityOrigin,this.getStorageKey(!0),this.#ti=e.unreachableUrl||t.DevToolsPath.EmptyUrlString,this.#ri=e?.adFrameStatus,this.#ni=e.secureContextType,this.#si=e.crossOriginIsolatedContextType,this.#ii=e.gatedAPIFeatures,this.backForwardCacheDetails={restoredFromCache:void 0,explanations:[],explanationsTree:void 0};const r=this.resourcesMap.get(this.#Ts);this.resourcesMap.clear(),this.removeChildFrames(),r&&r.loaderId===this.#Ls&&this.addResource(r)}resourceTreeModel(){return this.#un}get id(){return this.#Pe}get name(){return this.#h||""}get url(){return this.#Ts}domainAndRegistry(){return this.#Ys}async getAdScriptId(e){return(await this.#un.agent.invoke_getAdScriptId({frameId:e})).adScriptId||null}get securityOrigin(){return this.#Zs}getStorageKey(e){return this.#ei&&!e||(this.#ei=this.#un.storageKeyForFrame(this.#Pe)),this.#ei}unreachableUrl(){return this.#ti}get loaderId(){return this.#Ls}adFrameType(){return this.#ri?.adFrameType||"none"}adFrameStatus(){return this.#ri}get childFrames(){return[...this.#li]}sameTargetParentFrame(){return this.#Js}crossTargetParentFrame(){if(!this.crossTargetParentFrameId)return null;const e=this.#un.target().parentTarget();if(e?.type()!==Ue.Frame)return null;const t=e.model(mn);return t&&t.framesInternal.get(this.crossTargetParentFrameId)||null}parentFrame(){return this.sameTargetParentFrame()||this.crossTargetParentFrame()}isMainFrame(){return!this.#Js}isOutermostFrame(){return this.#un.target().parentTarget()?.type()!==Ue.Frame&&!this.#Js&&!this.crossTargetParentFrameId}isPrimaryFrame(){return!this.#Js&&this.#un.target()===je.instance().primaryPageTarget()}removeChildFrame(e,t){this.#li.delete(e),e.remove(t)}removeChildFrames(){const e=this.#li;this.#li=new Set;for(const t of e)t.remove(!1)}remove(e){this.removeChildFrames(),this.#un.framesInternal.delete(this.id),this.#un.dispatchEventToListeners(gn.FrameDetached,{frame:this,isSwap:e})}addResource(e){this.resourcesMap.get(e.url)!==e&&(this.resourcesMap.set(e.url,e),this.#un.dispatchEventToListeners(gn.ResourceAdded,e))}addRequest(e){let t=this.resourcesMap.get(e.url());t&&t.request===e||(t=new on(this.#un,e,e.url(),e.documentURL,e.frameId,e.loaderId,e.resourceType(),e.mimeType,null,null),this.resourcesMap.set(t.url,t),this.#un.dispatchEventToListeners(gn.ResourceAdded,t))}resources(){return Array.from(this.resourcesMap.values())}resourceForURL(e){const t=this.resourcesMap.get(e);if(t)return t;for(const t of this.#li){const r=t.resourceForURL(e);if(r)return r}return null}callForFrameResources(e){for(const t of this.resourcesMap.values())if(e(t))return!0;for(const t of this.#li)if(t.callForFrameResources(e))return!0;return!1}displayName(){if(this.isOutermostFrame())return i.i18n.lockedString("top");const t=new e.ParsedURL.ParsedURL(this.#Ts).displayName;return t?this.#h?this.#h+" ("+t+")":t:i.i18n.lockedString("iframe")}async getOwnerDeferredDOMNode(){const e=this.parentFrame();return e?e.resourceTreeModel().domModel().getOwnerNodeForFrame(this.#Pe):null}async getOwnerDOMNodeOrDocument(){const e=await this.getOwnerDeferredDOMNode();return e?e.resolvePromise():this.isOutermostFrame()?this.resourceTreeModel().domModel().requestDocument():null}async highlight(){const e=this.parentFrame(),t=this.resourceTreeModel().target().parentTarget(),r=async e=>{const t=await e.getOwnerNodeForFrame(this.#Pe);t&&e.overlayModel().highlightInOverlay({deferredNode:t,selectorList:""},"all",!0)};if(e)return r(e.resourceTreeModel().domModel());if(t?.type()===Ue.Frame){const e=t.model(tn);if(e)return r(e)}const n=await this.resourceTreeModel().domModel().requestDocument();n&&this.resourceTreeModel().domModel().overlayModel().highlightInOverlay({node:n,selectorList:""},"all",!0)}async getPermissionsPolicyState(){const e=await this.resourceTreeModel().target().pageAgent().invoke_getPermissionsPolicyState({frameId:this.#Pe});return e.getError()?null:e.states}async getOriginTrials(){const e=await this.resourceTreeModel().target().pageAgent().invoke_getOriginTrials({frameId:this.#Pe});return e.getError()?[]:e.originTrials}setCreationStackTrace(e){this.#ai=e.creationStackTrace,this.#oi=e.creationStackTraceTarget}setBackForwardCacheDetails(e){this.backForwardCacheDetails.restoredFromCache=!1,this.backForwardCacheDetails.explanations=e.notRestoredExplanations,this.backForwardCacheDetails.explanationsTree=e.notRestoredExplanationsTree}getResourcesMap(){return this.resourcesMap}}class bn{#lr;constructor(e){this.#lr=e}backForwardCacheNotUsed(e){this.#lr.onBackForwardCacheNotUsed(e)}domContentEventFired({timestamp:e}){this.#lr.dispatchEventToListeners(gn.DOMContentLoaded,e)}loadEventFired({timestamp:e}){this.#lr.dispatchEventToListeners(gn.Load,{resourceTreeModel:this.#lr,loadTime:e})}lifecycleEvent({frameId:e,name:t}){this.#lr.dispatchEventToListeners(gn.LifecycleEvent,{frameId:e,name:t})}frameAttached({frameId:e,parentFrameId:t,stack:r}){this.#lr.frameAttached(e,t,r)}frameNavigated({frame:e,type:t}){this.#lr.frameNavigated(e,t)}documentOpened({frame:e}){this.#lr.documentOpened(e)}frameDetached({frameId:e,reason:t}){this.#lr.frameDetached(e,"swap"===t)}frameStartedLoading({}){}frameStoppedLoading({}){}frameRequestedNavigation({}){}frameScheduledNavigation({}){}frameClearedScheduledNavigation({}){}navigatedWithinDocument({}){}frameResized(){this.#lr.dispatchEventToListeners(gn.FrameResized)}javascriptDialogOpening(e){this.#lr.dispatchEventToListeners(gn.JavaScriptDialogOpening,e),e.hasBrowserHandler||this.#lr.agent.invoke_handleJavaScriptDialog({accept:!1})}javascriptDialogClosed({}){}screencastFrame({}){}screencastVisibilityChanged({}){}interstitialShown(){this.#lr.isInterstitialShowing=!0,this.#lr.dispatchEventToListeners(gn.InterstitialShown)}interstitialHidden(){this.#lr.isInterstitialShowing=!1,this.#lr.dispatchEventToListeners(gn.InterstitialHidden)}windowOpen({}){}compilationCacheProduced({}){}fileChooserOpened({}){}downloadWillBegin({}){}downloadProgress(){}}c.register(mn,{capabilities:2,autostart:!0,early:!0});var yn=Object.freeze({__proto__:null,ResourceTreeModel:mn,get Events(){return gn},ResourceTreeFrame:fn,PageDispatcher:bn});class vn extends c{#di;#ci;constructor(e){super(e),this.#di=new Map,this.#ci=new Map,je.instance().addModelListener(mn,gn.PrimaryPageChanged,this.#hi,this)}addBlockedCookie(e,t){const r=e.key(),n=this.#di.get(r);this.#di.set(r,e),t?this.#ci.set(e,t):this.#ci.delete(e),n&&this.#ci.delete(n)}removeBlockedCookie(e){this.#di.delete(e.key())}#hi(){this.#di.clear(),this.#ci.clear()}getCookieToBlockedReasonsMap(){return this.#ci}async getCookies(e){const t=await this.target().networkAgent().invoke_getCookies({urls:e});if(t.getError())return[];const r=t.cookies.map(F.fromProtocolCookie),n=Array.from(this.#di.values());return r.filter((e=>!(e=>n.some((t=>e.isEqual(t))))(e))).concat(n)}async deleteCookie(e){await this.deleteCookies([e])}async clear(e,t){const r=await this.getCookiesForDomain(e||null);if(t){const e=r.filter((e=>e.matchesSecurityOrigin(t)));await this.deleteCookies(e)}else await this.deleteCookies(r)}async saveCookie(e){let t,r=e.domain();r.startsWith(".")||(r=""),e.expires()&&(t=Math.floor(Date.parse(`${e.expires()}`)/1e3));const n=a.Runtime.experiments.isEnabled("experimental-cookie-features"),s={name:e.name(),value:e.value(),url:e.url()||void 0,domain:r,path:e.path(),secure:e.secure(),httpOnly:e.httpOnly(),sameSite:e.sameSite(),expires:t,priority:e.priority(),partitionKey:e.partitionKey(),sourceScheme:n?e.sourceScheme():(i=e.sourceScheme(),"Unset"===i?i:void 0),sourcePort:n?e.sourcePort():void 0};var i;const o=await this.target().networkAgent().invoke_setCookie(s);return!(o.getError()||!o.success)&&o.success}getCookiesForDomain(t){const r=[];const n=this.target().model(mn);return n&&(n.mainFrame&&n.mainFrame.unreachableUrl()&&r.push(n.mainFrame.unreachableUrl()),n.forAllResources((function(n){const s=e.ParsedURL.ParsedURL.fromString(n.documentURL);return!s||t&&s.securityOrigin()!==t||r.push(n.url),!1}))),this.getCookies(r)}async deleteCookies(e){const t=this.target().networkAgent();this.#di.clear(),this.#ci.clear(),await Promise.all(e.map((e=>t.invoke_deleteCookies({name:e.name(),url:void 0,domain:e.domain(),path:e.path(),partitionKey:e.partitionKey()}))))}}c.register(vn,{capabilities:16,autostart:!1});var In=Object.freeze({__proto__:null,CookieModel:vn});class kn{#ui;#gi;#pi;#mi;#fi;#bi;#yi;constructor(e){e&&(this.#ui=e.toLowerCase().replace(/^\./,"")),this.#gi=[],this.#mi=0}static parseSetCookie(e,t){return new kn(t).parseSetCookie(e)}getCookieAttribute(e){if(!e)return null;switch(e.toLowerCase()){case"domain":return"domain";case"expires":return"expires";case"max-age":return"max-age";case"httponly":return"http-only";case"name":return"name";case"path":return"path";case"samesite":return"same-site";case"secure":return"secure";case"value":return"value";case"priority":return"priority";case"sourceport":return"source-port";case"sourcescheme":return"source-scheme";case"partitioned":return"partitioned";default:return console.error("Failed getting cookie attribute: "+e),null}}cookies(){return this.#gi}parseSetCookie(e){if(!this.initialize(e))return null;for(let e=this.extractKeyValue();e;e=this.extractKeyValue())this.#fi?this.#fi.addAttribute(this.getCookieAttribute(e.key),e.value):this.addCookie(e,1),this.advanceAndCheckCookieDelimiter()&&this.flushCookie();return this.flushCookie(),this.#gi}initialize(e){return this.#pi=e,"string"==typeof e&&(this.#gi=[],this.#fi=null,this.#bi="",this.#mi=this.#pi.length,!0)}flushCookie(){this.#fi&&(this.#fi.setSize(this.#mi-this.#pi.length-this.#yi),this.#fi.setCookieLine(this.#bi.replace("\n",""))),this.#fi=null,this.#bi=""}extractKeyValue(){if(!this.#pi||!this.#pi.length)return null;const e=/^[ \t]*([^=;\n]+)[ \t]*(?:=[ \t]*([^;\n]*))?/.exec(this.#pi);if(!e)return console.error("Failed parsing cookie header before: "+this.#pi),null;const t=new wn(e[1]&&e[1].trim(),e[2]&&e[2].trim(),this.#mi-this.#pi.length);return this.#bi+=e[0],this.#pi=this.#pi.slice(e[0].length),t}advanceAndCheckCookieDelimiter(){if(!this.#pi)return!1;const e=/^\s*[\n;]\s*/.exec(this.#pi);return!!e&&(this.#bi+=e[0],this.#pi=this.#pi.slice(e[0].length),null!==e[0].match("\n"))}addCookie(e,t){this.#fi&&this.#fi.setSize(e.position-this.#yi),this.#fi="string"==typeof e.value?new F(e.key,e.value,t):new F("",e.key,t),this.#ui&&this.#fi.addAttribute("domain",this.#ui),this.#yi=e.position,this.#gi.push(this.#fi)}}class wn{key;value;position;constructor(e,t,r){this.key=e,this.value=t,this.position=r}}var Sn=Object.freeze({__proto__:null,CookieParser:kn});class Cn{#vi;#Ii;#ki=!1;#wi="";#Si="";#Ci="";#Ri="";constructor(e,t){this.#vi=e,this.#Ii=new Rn(this.#xi.bind(this),t)}async addBase64Chunk(e){await this.#Ii.addBase64Chunk(e)}#xi(e){let t=0;for(let r=0;r<e.length;++r)this.#ki&&"\n"===e[r]?(this.#ki=!1,++t):(this.#ki=!1,"\r"!==e[r]&&"\n"!==e[r]||(this.#wi+=e.substring(t,r),this.#Ti(),this.#wi="",t=r+1,this.#ki="\r"===e[r]));this.#wi+=e.substring(t)}#Ti(){if(0===this.#wi.length){if(this.#Ci.length>0){const e=this.#Ci.slice(0,-1);this.#vi(this.#Ri||"message",e,this.#Si),this.#Ci=""}return void(this.#Ri="")}let e,t=this.#wi.indexOf(":");t<0?(t=this.#wi.length,e=t):(e=t+1,e<this.#wi.length&&" "===this.#wi[e]&&++e);const r=this.#wi.substring(0,t);"event"!==r?("data"===r&&(this.#Ci+=this.#wi.substring(e),this.#Ci+="\n"),"id"===r&&(this.#Si=this.#wi.substring(e))):this.#Ri=this.#wi.substring(e)}}class Rn{#Ii;#Mi;constructor(e,t){this.#Ii=new TextDecoderStream(t),this.#Mi=this.#Ii.writable.getWriter(),this.#Ii.readable.pipeTo(new WritableStream({write:e}))}async addBase64Chunk(e){const t=window.atob(e),r=Uint8Array.from(t,(e=>e.codePointAt(0)));await this.#Mi.ready,await this.#Mi.write(r)}}var xn=Object.freeze({__proto__:null,ServerSentEventsParser:Cn});class Tn{#Pi;#Li;#Ai=0;#Ei=[];constructor(e,t){this.#Pi=e,t&&(this.#Ai=e.pseudoWallTime(e.startTime),this.#Li=new Cn(this.#Oi.bind(this),e.charset()??void 0),this.#Pi.requestStreamingContent().then((t=>{s.StreamingContentData.isError(t)||(this.#Li?.addBase64Chunk(t.content().base64),t.addEventListener("ChunkAdded",(({data:{chunk:t}})=>{this.#Ai=e.pseudoWallTime(e.endTime),this.#Li?.addBase64Chunk(t)})))})))}get eventSourceMessages(){return this.#Ei}onProtocolEventSourceMessageReceived(e,t,r,n){this.#Ni({eventName:e,eventId:r,data:t,time:n})}#Oi(e,t,r){this.#Ni({eventName:e,eventId:r,data:t,time:this.#Ai})}#Ni(e){this.#Ei.push(e),this.#Pi.dispatchEventToListeners(Bn.EventSourceMessageAdded,e)}}const Mn={deprecatedSyntaxFoundPleaseUse:"Deprecated syntax found. Please use: <name>;dur=<duration>;desc=<description>",duplicateParameterSIgnored:'Duplicate parameter "{PH1}" ignored.',noValueFoundForParameterS:'No value found for parameter "{PH1}".',unrecognizedParameterS:'Unrecognized parameter "{PH1}".',extraneousTrailingCharacters:"Extraneous trailing characters.",unableToParseSValueS:'Unable to parse "{PH1}" value "{PH2}".'},Pn=i.i18n.registerUIStrings("core/sdk/ServerTiming.ts",Mn),Ln=i.i18n.getLocalizedString.bind(void 0,Pn);class An{metric;value;description;constructor(e,t,r){this.metric=e,this.value=t,this.description=r}static parseHeaders(e){const r=e.filter((e=>"server-timing"===e.name.toLowerCase()));if(!r.length)return null;const n=r.reduce(((e,t)=>{const r=this.createFromHeaderValue(t.value);return e.push(...r.map((function(e){return new An(e.name,e.hasOwnProperty("dur")?e.dur:null,e.hasOwnProperty("desc")?e.desc:"")}))),e}),[]);return n.sort(((e,r)=>t.StringUtilities.compare(e.metric.toLowerCase(),r.metric.toLowerCase()))),n}static createFromHeaderValue(e){function t(){e=e.replace(/^\s*/,"")}function r(r){return console.assert(1===r.length),t(),e.charAt(0)===r&&(e=e.substring(1),!0)}function n(){const t=/^(?:\s*)([\w!#$%&'*+\-.^`|~]+)(?:\s*)(.*)/.exec(e);return t?(e=t[2],t[1]):null}function s(){return t(),'"'===e.charAt(0)?function(){console.assert('"'===e.charAt(0)),e=e.substring(1);let t="";for(;e.length;){const r=/^([^"\\]*)(.*)/.exec(e);if(!r)return null;if(t+=r[1],'"'===r[2].charAt(0))return e=r[2].substring(1),t;console.assert("\\"===r[2].charAt(0)),t+=r[2].charAt(1),e=r[2].substring(2)}return null}():n()}function i(){const t=/([,;].*)/.exec(e);t&&(e=t[1])}const a=[];let o;for(;null!==(o=n());){const t={name:o};for("="===e.charAt(0)&&this.showWarning(Ln(Mn.deprecatedSyntaxFoundPleaseUse));r(";");){let e;if(null===(e=n()))continue;e=e.toLowerCase();const a=this.getParserForParameter(e);let o=null;if(r("=")&&(o=s(),i()),a){if(t.hasOwnProperty(e)){this.showWarning(Ln(Mn.duplicateParameterSIgnored,{PH1:e}));continue}null===o&&this.showWarning(Ln(Mn.noValueFoundForParameterS,{PH1:e})),a.call(this,t,o)}else this.showWarning(Ln(Mn.unrecognizedParameterS,{PH1:e}))}if(a.push(t),!r(","))break}return e.length&&this.showWarning(Ln(Mn.extraneousTrailingCharacters)),a}static getParserForParameter(e){switch(e){case"dur":{function t(t,r){if(t.dur=0,null!==r){const n=parseFloat(r);if(isNaN(n))return void An.showWarning(Ln(Mn.unableToParseSValueS,{PH1:e,PH2:r}));t.dur=n}}return t}case"desc":{function r(e,t){e.desc=t||""}return r}default:return null}}static showWarning(t){e.Console.Console.instance().warn(`ServerTiming: ${t}`)}}var En=Object.freeze({__proto__:null,ServerTiming:An});const On={binary:"(binary)",secureOnly:'This cookie was blocked because it had the "`Secure`" attribute and the connection was not secure.',notOnPath:"This cookie was blocked because its path was not an exact match for or a superdirectory of the request url's path.",domainMismatch:"This cookie was blocked because neither did the request URL's domain exactly match the cookie's domain, nor was the request URL's domain a subdomain of the cookie's Domain attribute value.",sameSiteStrict:'This cookie was blocked because it had the "`SameSite=Strict`" attribute and the request was made from a different site. This includes top-level navigation requests initiated by other sites.',sameSiteLax:'This cookie was blocked because it had the "`SameSite=Lax`" attribute and the request was made from a different site and was not initiated by a top-level navigation.',sameSiteUnspecifiedTreatedAsLax:'This cookie didn\'t specify a "`SameSite`" attribute when it was stored and was defaulted to "SameSite=Lax," and was blocked because the request was made from a different site and was not initiated by a top-level navigation. The cookie had to have been set with "`SameSite=None`" to enable cross-site usage.',sameSiteNoneInsecure:'This cookie was blocked because it had the "`SameSite=None`" attribute but was not marked "Secure". Cookies without SameSite restrictions must be marked "Secure" and sent over a secure connection.',userPreferences:"This cookie was blocked due to user preferences.",thirdPartyPhaseout:"This cookie was blocked due to third-party cookie phaseout. Learn more in the Issues tab.",unknownError:"An unknown error was encountered when trying to send this cookie.",schemefulSameSiteStrict:'This cookie was blocked because it had the "`SameSite=Strict`" attribute but the request was cross-site. This includes top-level navigation requests initiated by other sites. This request is considered cross-site because the URL has a different scheme than the current site.',schemefulSameSiteLax:'This cookie was blocked because it had the "`SameSite=Lax`" attribute but the request was cross-site and was not initiated by a top-level navigation. This request is considered cross-site because the URL has a different scheme than the current site.',schemefulSameSiteUnspecifiedTreatedAsLax:'This cookie didn\'t specify a "`SameSite`" attribute when it was stored, was defaulted to "`SameSite=Lax"`, and was blocked because the request was cross-site and was not initiated by a top-level navigation. This request is considered cross-site because the URL has a different scheme than the current site.',samePartyFromCrossPartyContext:"This cookie was blocked because it had the \"`SameParty`\" attribute but the request was cross-party. The request was considered cross-party because the domain of the resource's URL and the domains of the resource's enclosing frames/documents are neither owners nor members in the same First-Party Set.",nameValuePairExceedsMaxSize:"This cookie was blocked because it was too large. The combined size of the name and value must be less than or equal to 4096 characters.",thisSetcookieWasBlockedDueToUser:"This attempt to set a cookie via a `Set-Cookie` header was blocked due to user preferences.",thisSetcookieWasBlockedDueThirdPartyPhaseout:"Setting this cookie was blocked due to third-party cookie phaseout. Learn more in the Issues tab.",thisSetcookieHadInvalidSyntax:"This `Set-Cookie` header had invalid syntax.",thisSetcookieHadADisallowedCharacter:"This `Set-Cookie` header contained a disallowed character (a forbidden ASCII control character, or the tab character if it appears in the middle of the cookie name, value, an attribute name, or an attribute value).",theSchemeOfThisConnectionIsNot:"The scheme of this connection is not allowed to store cookies.",anUnknownErrorWasEncounteredWhenTrying:"An unknown error was encountered when trying to store this cookie.",thisSetcookieWasBlockedBecauseItHadTheSamesiteStrictLax:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the "{PH1}" attribute but came from a cross-site response which was not the response to a top-level navigation. This response is considered cross-site because the URL has a different scheme than the current site.',thisSetcookieDidntSpecifyASamesite:'This `Set-Cookie` header didn\'t specify a "`SameSite`" attribute, was defaulted to "`SameSite=Lax"`, and was blocked because it came from a cross-site response which was not the response to a top-level navigation. This response is considered cross-site because the URL has a different scheme than the current site.',thisSetcookieWasBlockedBecauseItHadTheSameparty:"This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the \"`SameParty`\" attribute but the request was cross-party. The request was considered cross-party because the domain of the resource's URL and the domains of the resource's enclosing frames/documents are neither owners nor members in the same First-Party Set.",thisSetcookieWasBlockedBecauseItHadTheSamepartyAttribute:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the "`SameParty`" attribute but also had other conflicting attributes. Chrome requires cookies that use the "`SameParty`" attribute to also have the "Secure" attribute, and to not be restricted to "`SameSite=Strict`".',blockedReasonSecureOnly:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the "Secure" attribute but was not received over a secure connection.',blockedReasonSameSiteStrictLax:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the "{PH1}" attribute but came from a cross-site response which was not the response to a top-level navigation.',blockedReasonSameSiteUnspecifiedTreatedAsLax:'This `Set-Cookie` header didn\'t specify a "`SameSite`" attribute and was defaulted to "`SameSite=Lax,`" and was blocked because it came from a cross-site response which was not the response to a top-level navigation. The `Set-Cookie` had to have been set with "`SameSite=None`" to enable cross-site usage.',blockedReasonSameSiteNoneInsecure:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it had the "`SameSite=None`" attribute but did not have the "Secure" attribute, which is required in order to use "`SameSite=None`".',blockedReasonOverwriteSecure:"This attempt to set a cookie via a `Set-Cookie` header was blocked because it was not sent over a secure connection and would have overwritten a cookie with the Secure attribute.",blockedReasonInvalidDomain:"This attempt to set a cookie via a `Set-Cookie` header was blocked because its Domain attribute was invalid with regards to the current host url.",blockedReasonInvalidPrefix:'This attempt to set a cookie via a `Set-Cookie` header was blocked because it used the "`__Secure-`" or "`__Host-`" prefix in its name and broke the additional rules applied to cookies with these prefixes as defined in `https://tools.ietf.org/html/draft-west-cookie-prefixes-05`.',thisSetcookieWasBlockedBecauseTheNameValuePairExceedsMaxSize:"This attempt to set a cookie via a `Set-Cookie` header was blocked because the cookie was too large. The combined size of the name and value must be less than or equal to 4096 characters.",setcookieHeaderIsIgnoredIn:"Set-Cookie header is ignored in response from url: {PH1}. The combined size of the name and value must be less than or equal to 4096 characters.",exemptionReasonUserSetting:"This cookie is allowed by user preference.",exemptionReasonTPCDMetadata:"This cookie is allowed by a third-party cookie deprecation trial grace period. Learn more: goo.gle/ps-dt.",exemptionReasonTPCDDeprecationTrial:"This cookie is allowed by third-party cookie phaseout deprecation trial.",exemptionReasonTPCDHeuristics:"This cookie is allowed by third-party cookie phaseout heuristics. Learn more: goo.gle/hbe",exemptionReasonEnterprisePolicy:"This cookie is allowed by Chrome Enterprise policy. Learn more: goo.gle/ce-3pc",exemptionReasonStorageAccessAPI:"This cookie is allowed by the Storage Access API. Learn more: goo.gle/saa",exemptionReasonTopLevelStorageAccessAPI:"This cookie is allowed by the top-level Storage Access API. Learn more: goo.gle/saa-top",exemptionReasonCorsOptIn:"This cookie is allowed by CORS opt-in. Learn more: goo.gle/cors"},Nn=i.i18n.registerUIStrings("core/sdk/NetworkRequest.ts",On),Dn=i.i18n.getLocalizedString.bind(void 0,Nn);class Fn extends e.ObjectWrapper.ObjectWrapper{#Di;#Fi;#Ms;#Ps;#Ls;#Bi;#Ui;#Hi;#qi;#_i;#zi;#ji;#Vi;#Wi;#Gi;#Ki;#Qi;statusCode;statusText;requestMethod;requestTime;protocol;alternateProtocolUsage;mixedContentType;#$i;#Xi;#Ji;#Yi;#Zi;#ea;#ta;#ra;#na;#sa;#ia;#aa;#oa;#la;#da;#ca;#ha;#ua;#ga;#pa;#ma;connectionId;connectionReused;hasNetworkData;#fa;#ba;#ya;#va;#Ia;#ka;#wa;#Sa;#Ca;#Ra;#xa;localizedFailDescription;#Ts;#Ta;#Ma;#Dt;#Pa;#La;#Aa;#Es;#Ea;#Fs;#h;#Oa;#Na;#Da;#Fa;#Ba;#Ua;#Ha;#qa;#_a;#za;#ja;#Va;#Wa;#Ga;#Ka;#Qa;#$a;#Xa;#Ja;#Ya;#Za;#eo;#to;#ro;#no;#so=new Map;#io;#ao;#oo;constructor(t,r,n,s,i,a,o,l){super(),this.#Di=t,this.#Fi=r,this.setUrl(n),this.#Ms=s,this.#Ps=i,this.#Ls=a,this.#Bi=o,this.#Ui=l,this.#Hi=null,this.#qi=null,this.#_i=null,this.#zi=!1,this.#ji=null,this.#Vi=-1,this.#Wi=-1,this.#Gi=-1,this.#Ki=void 0,this.#Qi=void 0,this.statusCode=0,this.statusText="",this.requestMethod="",this.requestTime=0,this.protocol="",this.alternateProtocolUsage=void 0,this.mixedContentType="none",this.#$i=null,this.#Xi=null,this.#Ji=null,this.#Yi=null,this.#Zi=null,this.#ea=e.ResourceType.resourceTypes.Other,this.#ta=null,this.#ra=null,this.#na=[],this.#sa={},this.#ia="",this.#aa=[],this.#la=[],this.#da=[],this.#ca={},this.#ha="",this.#ua="Unknown",this.#ga=null,this.#pa="unknown",this.#ma=null,this.connectionId="0",this.connectionReused=!1,this.hasNetworkData=!1,this.#fa=null,this.#ba=Promise.resolve(null),this.#ya=!1,this.#va=!1,this.#Ia=[],this.#ka=[],this.#wa=[],this.#Sa=[],this.#xa=!1,this.#Ca=null,this.#Ra=null,this.localizedFailDescription=null,this.#ro=null,this.#no=!1,this.#io=!1,this.#ao=!1}static create(e,t,r,n,s,i,a){return new Fn(e,e,t,r,n,s,i,a)}static createForWebSocket(e,r,n){return new Fn(e,e,r,t.DevToolsPath.EmptyUrlString,null,null,n||null)}static createWithoutBackendRequest(e,t,r,n){return new Fn(e,void 0,t,r,null,null,n)}identityCompare(e){const t=this.requestId(),r=e.requestId();return t>r?1:t<r?-1:0}requestId(){return this.#Di}backendRequestId(){return this.#Fi}url(){return this.#Ts}isBlobRequest(){return e.ParsedURL.schemeIs(this.#Ts,"blob:")}setUrl(t){this.#Ts!==t&&(this.#Ts=t,this.#Fs=new e.ParsedURL.ParsedURL(t),this.#Za=void 0,this.#eo=void 0,this.#h=void 0,this.#Oa=void 0)}get documentURL(){return this.#Ms}get parsedURL(){return this.#Fs}get frameId(){return this.#Ps}get loaderId(){return this.#Ls}setRemoteAddress(e,t){this.#ha=e+":"+t,this.dispatchEventToListeners(Bn.RemoteAddressChanged,this)}remoteAddress(){return this.#ha}remoteAddressSpace(){return this.#ua}getResponseCacheStorageCacheName(){return this.#Ba}setResponseCacheStorageCacheName(e){this.#Ba=e}serviceWorkerResponseSource(){return this.#Ua}setServiceWorkerResponseSource(e){this.#Ua=e}setReferrerPolicy(e){this.#ga=e}referrerPolicy(){return this.#ga}securityState(){return this.#pa}setSecurityState(e){this.#pa=e}securityDetails(){return this.#ma}securityOrigin(){return this.#Fs.securityOrigin()}setSecurityDetails(e){this.#ma=e}get startTime(){return this.#Wi||-1}setIssueTime(e,t){this.#Vi=e,this.#Ha=t,this.#Wi=e}issueTime(){return this.#Vi}pseudoWallTime(e){return this.#Ha?this.#Ha-this.#Vi+e:e}get responseReceivedTime(){return this.#Ta||-1}set responseReceivedTime(e){this.#Ta=e}getResponseRetrievalTime(){return this.#qa}setResponseRetrievalTime(e){this.#qa=e}get endTime(){return this.#Gi||-1}set endTime(e){this.timing&&this.timing.requestTime?this.#Gi=Math.max(e,this.responseReceivedTime):(this.#Gi=e,this.#Ta>e&&(this.#Ta=e)),this.dispatchEventToListeners(Bn.TimingChanged,this)}get duration(){return-1===this.#Gi||-1===this.#Wi?-1:this.#Gi-this.#Wi}get latency(){return-1===this.#Ta||-1===this.#Wi?-1:this.#Ta-this.#Wi}get resourceSize(){return this.#_a||0}set resourceSize(e){this.#_a=e}get transferSize(){return this.#Ma||0}increaseTransferSize(e){this.#Ma=(this.#Ma||0)+e}setTransferSize(e){this.#Ma=e}get finished(){return this.#Dt}set finished(e){this.#Dt!==e&&(this.#Dt=e,e&&this.dispatchEventToListeners(Bn.FinishedLoading,this))}get failed(){return this.#Pa}set failed(e){this.#Pa=e}get canceled(){return this.#La}set canceled(e){this.#La=e}get preserved(){return this.#Aa}set preserved(e){this.#Aa=e}blockedReason(){return this.#Ki}setBlockedReason(e){this.#Ki=e}corsErrorStatus(){return this.#Qi}setCorsErrorStatus(e){this.#Qi=e}wasBlocked(){return Boolean(this.#Ki)}cached(){return(Boolean(this.#za)||Boolean(this.#ja))&&!this.#Ma}cachedInMemory(){return Boolean(this.#za)&&!this.#Ma}fromPrefetchCache(){return Boolean(this.#Va)}setFromMemoryCache(){this.#za=!0,this.#Ka=void 0}get fromDiskCache(){return this.#ja}setFromDiskCache(){this.#ja=!0}setFromPrefetchCache(){this.#Va=!0}get fetchedViaServiceWorker(){return Boolean(this.#Wa)}set fetchedViaServiceWorker(e){this.#Wa=e}get serviceWorkerRouterInfo(){return this.#Ga}set serviceWorkerRouterInfo(e){this.#Ga=e}initiatedByServiceWorker(){const e=gt.forRequest(this);return!!e&&e.target().type()===Ue.ServiceWorker}get timing(){return this.#Ka}set timing(e){if(!e||this.#za)return;this.#Wi=e.requestTime;const t=e.requestTime+e.receiveHeadersEnd/1e3;((this.#Ta||-1)<0||this.#Ta>t)&&(this.#Ta=t),this.#Wi>this.#Ta&&(this.#Ta=this.#Wi),this.#Ka=e,this.dispatchEventToListeners(Bn.TimingChanged,this)}setConnectTimingFromExtraInfo(e){this.#Wi=e.requestTime,this.dispatchEventToListeners(Bn.TimingChanged,this)}get mimeType(){return this.#Es}set mimeType(t){if(this.#Es=t,"text/event-stream"===t&&!this.#oo){const t=this.resourceType()!==e.ResourceType.resourceTypes.EventSource;this.#oo=new Tn(this,t)}}get displayName(){return this.#Fs.displayName}name(){return this.#h||this.parseNameAndPathFromURL(),this.#h}path(){return this.#Oa||this.parseNameAndPathFromURL(),this.#Oa}parseNameAndPathFromURL(){if(this.#Fs.isDataURL())this.#h=this.#Fs.dataURLDisplayName(),this.#Oa="";else if(this.#Fs.isBlobURL())this.#h=this.#Fs.url,this.#Oa="";else if(this.#Fs.isAboutBlank())this.#h=this.#Fs.url,this.#Oa="";else{this.#Oa=this.#Fs.host+this.#Fs.folderPathComponents;const r=gt.forRequest(this),n=r?e.ParsedURL.ParsedURL.fromString(r.target().inspectedURL()):null;this.#Oa=t.StringUtilities.trimURL(this.#Oa,n?n.host:""),this.#Fs.lastPathComponent||this.#Fs.queryParams?this.#h=this.#Fs.lastPathComponent+(this.#Fs.queryParams?"?"+this.#Fs.queryParams:""):this.#Fs.folderPathComponents?(this.#h=this.#Fs.folderPathComponents.substring(this.#Fs.folderPathComponents.lastIndexOf("/")+1)+"/",this.#Oa=this.#Oa.substring(0,this.#Oa.lastIndexOf("/"))):(this.#h=this.#Fs.host,this.#Oa="")}}get folder(){let e=this.#Fs.path;const t=e.indexOf("?");-1!==t&&(e=e.substring(0,t));const r=e.lastIndexOf("/");return-1!==r?e.substring(0,r):""}get pathname(){return this.#Fs.path}resourceType(){return this.#ea}setResourceType(e){this.#ea=e}get domain(){return this.#Fs.host}get scheme(){return this.#Fs.scheme}getInferredStatusText(){return this.statusText||(e=this.statusCode,i.i18n.lockedString({100:"Continue",101:"Switching Protocols",102:"Processing",103:"Early Hints",200:"OK",201:"Created",202:"Accepted",203:"Non-Authoritative Information",204:"No Content",205:"Reset Content",206:"Partial Content",207:"Multi-Status",208:"Already Reported",226:"IM Used",300:"Multiple Choices",301:"Moved Permanently",302:"Found",303:"See Other",304:"Not Modified",305:"Use Proxy",307:"Temporary Redirect",308:"Permanent Redirect",400:"Bad Request",401:"Unauthorized",402:"Payment Required",403:"Forbidden",404:"Not Found",405:"Method Not Allowed",406:"Not Acceptable",407:"Proxy Authentication Required",408:"Request Timeout",409:"Conflict",410:"Gone",411:"Length Required",412:"Precondition Failed",413:"Payload Too Large",414:"URI Too Long",415:"Unsupported Media Type",416:"Range Not Satisfiable",417:"Expectation Failed",418:"I'm a teapot",421:"Misdirected Request",422:"Unprocessable Content",423:"Locked",424:"Failed Dependency",425:"Too Early",426:"Upgrade Required",428:"Precondition Required",429:"Too Many Requests",431:"Request Header Fields Too Large",451:"Unavailable For Legal Reasons",500:"Internal Server Error",501:"Not Implemented",502:"Bad Gateway",503:"Service Unavailable",504:"Gateway Timeout",505:"HTTP Version Not Supported",506:"Variant Also Negotiates",507:"Insufficient Storage",508:"Loop Detected",510:"Not Extended",511:"Network Authentication Required"}[e]??""));var e}redirectSource(){return this.#Hi}setRedirectSource(e){this.#Hi=e}preflightRequest(){return this.#qi}setPreflightRequest(e){this.#qi=e}preflightInitiatorRequest(){return this.#_i}setPreflightInitiatorRequest(e){this.#_i=e}isPreflightRequest(){return null!==this.#Bi&&void 0!==this.#Bi&&"preflight"===this.#Bi.type}redirectDestination(){return this.#ji}setRedirectDestination(e){this.#ji=e}requestHeaders(){return this.#da}setRequestHeaders(e){this.#da=e,this.dispatchEventToListeners(Bn.RequestHeadersChanged)}requestHeadersText(){return this.#Qa}setRequestHeadersText(e){this.#Qa=e,this.dispatchEventToListeners(Bn.RequestHeadersChanged)}requestHeaderValue(e){return this.#ca[e]||(this.#ca[e]=this.computeHeaderValue(this.requestHeaders(),e)),this.#ca[e]}requestFormData(){return this.#ba||(this.#ba=gt.requestPostData(this)),this.#ba}setRequestFormData(e,t){this.#ba=e&&null===t?null:Promise.resolve(t),this.#fa=null}filteredProtocolName(){const e=this.protocol.toLowerCase();return"h2"===e?"http/2.0":e.replace(/^http\/2(\.0)?\+/,"http/2.0+")}requestHttpVersion(){const e=this.requestHeadersText();if(!e){const e=this.requestHeaderValue("version")||this.requestHeaderValue(":version");return e||this.filteredProtocolName()}const t=e.split(/\r\n/)[0].match(/(HTTP\/\d+\.\d+)$/);return t?t[1]:"HTTP/0.9"}get responseHeaders(){return this.#$a||[]}set responseHeaders(e){this.#$a=e,this.#Xa=void 0,this.#Ya=void 0,this.#Ja=void 0,this.#sa={},this.dispatchEventToListeners(Bn.ResponseHeadersChanged)}get originalResponseHeaders(){return this.#aa}set originalResponseHeaders(e){this.#aa=e,this.#oa=void 0}get setCookieHeaders(){return this.#la}set setCookieHeaders(e){this.#la=e}get responseHeadersText(){return this.#ia}set responseHeadersText(e){this.#ia=e,this.dispatchEventToListeners(Bn.ResponseHeadersChanged)}get sortedResponseHeaders(){return void 0!==this.#Xa?this.#Xa:(this.#Xa=this.responseHeaders.slice(),this.#Xa.sort((function(e,r){return t.StringUtilities.compare(e.name.toLowerCase(),r.name.toLowerCase())})))}get sortedOriginalResponseHeaders(){return void 0!==this.#oa?this.#oa:(this.#oa=this.originalResponseHeaders.slice(),this.#oa.sort((function(e,r){return t.StringUtilities.compare(e.name.toLowerCase(),r.name.toLowerCase())})))}get overrideTypes(){const e=[];return this.hasOverriddenContent&&e.push("content"),this.hasOverriddenHeaders()&&e.push("headers"),e}get hasOverriddenContent(){return this.#io}set hasOverriddenContent(e){this.#io=e}#lo(e){const t=[];for(const r of e)t.length&&t[t.length-1].name===r.name?t[t.length-1].value+=`, ${r.value}`:t.push({name:r.name,value:r.value});return t}hasOverriddenHeaders(){if(!this.#aa.length)return!1;const e=this.#lo(this.sortedResponseHeaders),t=this.#lo(this.sortedOriginalResponseHeaders);if(e.length!==t.length)return!0;for(let r=0;r<e.length;r++){if(e[r].name.toLowerCase()!==t[r].name.toLowerCase())return!0;if(e[r].value!==t[r].value)return!0}return!1}responseHeaderValue(e){return e in this.#sa||(this.#sa[e]=this.computeHeaderValue(this.responseHeaders,e)),this.#sa[e]}wasIntercepted(){return this.#no}setWasIntercepted(e){this.#no=e}get responseCookies(){if(!this.#Ja)if(this.#Ja=kn.parseSetCookie(this.responseHeaderValue("Set-Cookie"),this.domain)||[],this.#Ca)for(const e of this.#Ja)e.partitioned()&&e.setPartitionKey(this.#Ca);else if(this.#Ra)for(const e of this.#Ja)e.setPartitionKeyOpaque();return this.#Ja}responseLastModified(){return this.responseHeaderValue("last-modified")}allCookiesIncludingBlockedOnes(){return[...this.includedRequestCookies().map((e=>e.cookie)),...this.responseCookies,...this.blockedRequestCookies().map((e=>e.cookie)),...this.blockedResponseCookies().map((e=>e.cookie))].filter((e=>Boolean(e)))}get serverTimings(){return void 0===this.#Ya&&(this.#Ya=An.parseHeaders(this.responseHeaders)),this.#Ya}queryString(){if(void 0!==this.#Za)return this.#Za;let e=null;const t=this.url(),r=t.indexOf("?");if(-1!==r){e=t.substring(r+1);const n=e.indexOf("#");-1!==n&&(e=e.substring(0,n))}return this.#Za=e,this.#Za}get queryParameters(){if(this.#eo)return this.#eo;const e=this.queryString();return e?(this.#eo=this.parseParameters(e),this.#eo):null}async parseFormParameters(){const e=this.requestContentType();if(!e)return null;if(e.match(/^application\/x-www-form-urlencoded\s*(;.*)?$/i)){const e=await this.requestFormData();return e?this.parseParameters(e):null}const t=e.match(/^multipart\/form-data\s*;\s*boundary\s*=\s*(\S+)\s*$/);if(!t)return null;const r=t[1];if(!r)return null;const n=await this.requestFormData();return n?this.parseMultipartFormDataParameters(n,r):null}formParameters(){return this.#fa||(this.#fa=this.parseFormParameters()),this.#fa}responseHttpVersion(){const e=this.#ia;if(!e){const e=this.responseHeaderValue("version")||this.responseHeaderValue(":version");return e||this.filteredProtocolName()}const t=e.split(/\r\n/)[0].match(/^(HTTP\/\d+\.\d+)/);return t?t[1]:"HTTP/0.9"}parseParameters(e){return e.split("&").map((function(e){const t=e.indexOf("=");return-1===t?{name:e,value:""}:{name:e.substring(0,t),value:e.substring(t+1)}}))}parseMultipartFormDataParameters(e,r){const n=t.StringUtilities.escapeForRegExp(r),s=new RegExp('^\\r\\ncontent-disposition\\s*:\\s*form-data\\s*;\\s*name="([^"]*)"(?:\\s*;\\s*filename="([^"]*)")?(?:\\r\\ncontent-type\\s*:\\s*([^\\r\\n]*))?\\r\\n\\r\\n(.*)\\r\\n$',"is");return e.split(new RegExp(`--${n}(?:--s*$)?`,"g")).reduce((function(e,t){const[r,n,i,a,o]=t.match(s)||[];if(!r)return e;const l=i||a?Dn(On.binary):o;return e.push({name:n,value:l}),e}),[])}computeHeaderValue(e,t){t=t.toLowerCase();const r=[];for(let n=0;n<e.length;++n)e[n].name.toLowerCase()===t&&r.push(e[n].value);if(r.length)return"set-cookie"===t?r.join("\n"):r.join(", ")}contentData(){return this.#ta||(this.#to?this.#ta=this.#to():this.#ta=gt.requestContentData(this)),this.#ta}setContentDataProvider(e){console.assert(!this.#ta,"contentData can only be set once."),this.#to=e}requestStreamingContent(){if(this.#ra)return this.#ra;const e=this.finished?this.contentData():gt.streamResponseBody(this);return this.#ra=e.then((e=>s.ContentData.ContentData.isError(e)?e:s.StreamingContentData.StreamingContentData.from(e))),this.#ra}contentURL(){return this.#Ts}contentType(){return this.#ea}async requestContent(){return s.ContentData.ContentData.asDeferredContent(await this.contentData())}async searchInContent(e,t,r){if(!this.#to)return gt.searchInRequest(this,e,t,r);const n=await this.contentData();return s.ContentData.ContentData.isError(n)||!n.isTextContent?[]:s.TextUtils.performSearchInContent(n.text,e,t,r)}isHttpFamily(){return Boolean(this.url().match(/^https?:/i))}requestContentType(){return this.requestHeaderValue("Content-Type")}hasErrorStatusCode(){return this.statusCode>=400}setInitialPriority(e){this.#$i=e}initialPriority(){return this.#$i}setPriority(e){this.#Xi=e}priority(){return this.#Xi||this.#$i||null}setSignedExchangeInfo(e){this.#Ji=e}signedExchangeInfo(){return this.#Ji}setWebBundleInfo(e){this.#Yi=e}webBundleInfo(){return this.#Yi}setWebBundleInnerRequestInfo(e){this.#Zi=e}webBundleInnerRequestInfo(){return this.#Zi}async populateImageSource(e){const t=await this.contentData();if(s.ContentData.ContentData.isError(t))return;let r=t.asDataUrl();if(null===r&&!this.#Pa){(this.responseHeaderValue("cache-control")||"").includes("no-cache")||(r=this.#Ts)}null!==r&&(e.src=r)}initiator(){return this.#Bi||null}hasUserGesture(){return this.#Ui??null}frames(){return this.#na}addProtocolFrameError(e,t){this.addFrame({type:Un.Error,text:e,time:this.pseudoWallTime(t),opCode:-1,mask:!1})}addProtocolFrame(e,t,r){const n=r?Un.Send:Un.Receive;this.addFrame({type:n,text:e.payloadData,time:this.pseudoWallTime(t),opCode:e.opcode,mask:e.mask})}addFrame(e){this.#na.push(e),this.dispatchEventToListeners(Bn.WebsocketFrameAdded,e)}eventSourceMessages(){return this.#oo?.eventSourceMessages??[]}addEventSourceMessage(e,t,r,n){this.#oo?.onProtocolEventSourceMessageReceived(t,n,r,this.pseudoWallTime(e))}markAsRedirect(e){this.#zi=!0,this.#Di=`${this.#Fi}:redirected.${e}`}isRedirect(){return this.#zi}setRequestIdForTest(e){this.#Fi=e,this.#Di=e}charset(){return this.#Ea??null}setCharset(e){this.#Ea=e}addExtraRequestInfo(e){this.#Ia=e.blockedRequestCookies,this.#ka=e.includedRequestCookies,this.setRequestHeaders(e.requestHeaders),this.#ya=!0,this.setRequestHeadersText(""),this.#Na=e.clientSecurityState,this.setConnectTimingFromExtraInfo(e.connectTiming),this.#xa=e.siteHasCookieInOtherPartition??!1,this.#ao=this.#Ia.some((e=>e.blockedReasons.includes("ThirdPartyPhaseout")))}hasExtraRequestInfo(){return this.#ya}blockedRequestCookies(){return this.#Ia}includedRequestCookies(){return this.#ka}hasRequestCookies(){return this.#ka.length>0||this.#Ia.length>0}siteHasCookieInOtherPartition(){return this.#xa}static parseStatusTextFromResponseHeadersText(e){return e.split("\r")[0].split(" ").slice(2).join(" ")}addExtraResponseInfo(e){if(this.#wa=e.blockedResponseCookies,e.exemptedResponseCookies&&(this.#Sa=e.exemptedResponseCookies),this.#Ca=e.cookiePartitionKey||null,this.#Ra=e.cookiePartitionKeyOpaque||null,this.responseHeaders=e.responseHeaders,this.originalResponseHeaders=e.responseHeaders.map((e=>({...e}))),e.responseHeadersText){if(this.responseHeadersText=e.responseHeadersText,!this.requestHeadersText()){let e=`${this.requestMethod} ${this.parsedURL.path}`;this.parsedURL.queryParams&&(e+=`?${this.parsedURL.queryParams}`),e+=" HTTP/1.1\r\n";for(const{name:t,value:r}of this.requestHeaders())e+=`${t}: ${r}\r\n`;this.setRequestHeadersText(e)}this.statusText=Fn.parseStatusTextFromResponseHeadersText(e.responseHeadersText)}this.#ua=e.resourceIPAddressSpace,e.statusCode&&(this.statusCode=e.statusCode),this.#va=!0;const t=gt.forRequest(this);if(!t)return;for(const e of this.#wa)if(e.blockedReasons.includes("NameValuePairExceedsMaxSize")){const e=Dn(On.setcookieHeaderIsIgnoredIn,{PH1:this.url()});t.dispatchEventToListeners(pt.MessageGenerated,{message:e,requestId:this.#Di,warning:!0})}const r=t.target().model(vn);if(r){for(const e of this.#Sa)r.removeBlockedCookie(e.cookie);for(const e of this.#wa){const t=e.cookie;t&&(e.blockedReasons.includes("ThirdPartyPhaseout")&&(this.#ao=!0),r.addBlockedCookie(t,e.blockedReasons.map((e=>({attribute:zn(e),uiString:qn(e)})))))}}}addBlockedRequestCookiesToModel(){const e=gt.forRequest(this);if(!e)return;const t=e.target().model(vn);if(t)for(const e of this.#Ia){const r=e.cookie;r&&(e.blockedReasons.includes("ThirdPartyPhaseout")&&(this.#ao=!0),t.addBlockedCookie(r,e.blockedReasons.map((e=>({attribute:_n(e),uiString:Hn(e)})))))}}hasExtraResponseInfo(){return this.#va}blockedResponseCookies(){return this.#wa}exemptedResponseCookies(){return this.#Sa}nonBlockedResponseCookies(){const e=this.blockedResponseCookies().map((e=>e.cookieLine));return this.responseCookies.filter((t=>{const r=e.indexOf(t.getCookieLine());return-1===r||(e[r]=null,!1)}))}responseCookiesPartitionKey(){return this.#Ca}responseCookiesPartitionKeyOpaque(){return this.#Ra}redirectSourceSignedExchangeInfoHasNoErrors(){return null!==this.#Hi&&null!==this.#Hi.#Ji&&!this.#Hi.#Ji.errors}clientSecurityState(){return this.#Na}setTrustTokenParams(e){this.#Da=e}trustTokenParams(){return this.#Da}setTrustTokenOperationDoneEvent(e){this.#Fa=e,this.dispatchEventToListeners(Bn.TrustTokenResultAdded)}trustTokenOperationDoneEvent(){return this.#Fa}setIsSameSite(e){this.#ro=e}isSameSite(){return this.#ro}getAssociatedData(e){return this.#so.get(e)||null}setAssociatedData(e,t){this.#so.set(e,t)}deleteAssociatedData(e){this.#so.delete(e)}hasThirdPartyCookiePhaseoutIssue(){return this.#ao}addDataReceivedEvent({timestamp:e,dataLength:t,encodedDataLength:r,data:n}){this.resourceSize+=t,-1!==r&&this.increaseTransferSize(r),this.endTime=e,n&&this.#ra?.then((e=>{s.StreamingContentData.isError(e)||e.addChunk(n)}))}}var Bn,Un;!function(e){e.FinishedLoading="FinishedLoading",e.TimingChanged="TimingChanged",e.RemoteAddressChanged="RemoteAddressChanged",e.RequestHeadersChanged="RequestHeadersChanged",e.ResponseHeadersChanged="ResponseHeadersChanged",e.WebsocketFrameAdded="WebsocketFrameAdded",e.EventSourceMessageAdded="EventSourceMessageAdded",e.TrustTokenResultAdded="TrustTokenResultAdded"}(Bn||(Bn={})),function(e){e.Send="send",e.Receive="receive",e.Error="error"}(Un||(Un={}));const Hn=function(e){switch(e){case"SecureOnly":return Dn(On.secureOnly);case"NotOnPath":return Dn(On.notOnPath);case"DomainMismatch":return Dn(On.domainMismatch);case"SameSiteStrict":return Dn(On.sameSiteStrict);case"SameSiteLax":return Dn(On.sameSiteLax);case"SameSiteUnspecifiedTreatedAsLax":return Dn(On.sameSiteUnspecifiedTreatedAsLax);case"SameSiteNoneInsecure":return Dn(On.sameSiteNoneInsecure);case"UserPreferences":return Dn(On.userPreferences);case"UnknownError":return Dn(On.unknownError);case"SchemefulSameSiteStrict":return Dn(On.schemefulSameSiteStrict);case"SchemefulSameSiteLax":return Dn(On.schemefulSameSiteLax);case"SchemefulSameSiteUnspecifiedTreatedAsLax":return Dn(On.schemefulSameSiteUnspecifiedTreatedAsLax);case"SamePartyFromCrossPartyContext":return Dn(On.samePartyFromCrossPartyContext);case"NameValuePairExceedsMaxSize":return Dn(On.nameValuePairExceedsMaxSize);case"ThirdPartyPhaseout":return Dn(On.thirdPartyPhaseout)}return""},qn=function(e){switch(e){case"SecureOnly":return Dn(On.blockedReasonSecureOnly);case"SameSiteStrict":return Dn(On.blockedReasonSameSiteStrictLax,{PH1:"SameSite=Strict"});case"SameSiteLax":return Dn(On.blockedReasonSameSiteStrictLax,{PH1:"SameSite=Lax"});case"SameSiteUnspecifiedTreatedAsLax":return Dn(On.blockedReasonSameSiteUnspecifiedTreatedAsLax);case"SameSiteNoneInsecure":return Dn(On.blockedReasonSameSiteNoneInsecure);case"UserPreferences":return Dn(On.thisSetcookieWasBlockedDueToUser);case"SyntaxError":return Dn(On.thisSetcookieHadInvalidSyntax);case"SchemeNotSupported":return Dn(On.theSchemeOfThisConnectionIsNot);case"OverwriteSecure":return Dn(On.blockedReasonOverwriteSecure);case"InvalidDomain":return Dn(On.blockedReasonInvalidDomain);case"InvalidPrefix":return Dn(On.blockedReasonInvalidPrefix);case"UnknownError":return Dn(On.anUnknownErrorWasEncounteredWhenTrying);case"SchemefulSameSiteStrict":return Dn(On.thisSetcookieWasBlockedBecauseItHadTheSamesiteStrictLax,{PH1:"SameSite=Strict"});case"SchemefulSameSiteLax":return Dn(On.thisSetcookieWasBlockedBecauseItHadTheSamesiteStrictLax,{PH1:"SameSite=Lax"});case"SchemefulSameSiteUnspecifiedTreatedAsLax":return Dn(On.thisSetcookieDidntSpecifyASamesite);case"SamePartyFromCrossPartyContext":return Dn(On.thisSetcookieWasBlockedBecauseItHadTheSameparty);case"SamePartyConflictsWithOtherAttributes":return Dn(On.thisSetcookieWasBlockedBecauseItHadTheSamepartyAttribute);case"NameValuePairExceedsMaxSize":return Dn(On.thisSetcookieWasBlockedBecauseTheNameValuePairExceedsMaxSize);case"DisallowedCharacter":return Dn(On.thisSetcookieHadADisallowedCharacter);case"ThirdPartyPhaseout":return Dn(On.thisSetcookieWasBlockedDueThirdPartyPhaseout)}return""},_n=function(e){switch(e){case"SecureOnly":return"secure";case"NotOnPath":return"path";case"DomainMismatch":return"domain";case"SameSiteStrict":case"SameSiteLax":case"SameSiteUnspecifiedTreatedAsLax":case"SameSiteNoneInsecure":case"SchemefulSameSiteStrict":case"SchemefulSameSiteLax":case"SchemefulSameSiteUnspecifiedTreatedAsLax":return"same-site";case"SamePartyFromCrossPartyContext":case"NameValuePairExceedsMaxSize":case"UserPreferences":case"ThirdPartyPhaseout":case"UnknownError":return null}return null},zn=function(e){switch(e){case"SecureOnly":case"OverwriteSecure":return"secure";case"SameSiteStrict":case"SameSiteLax":case"SameSiteUnspecifiedTreatedAsLax":case"SameSiteNoneInsecure":case"SchemefulSameSiteStrict":case"SchemefulSameSiteLax":case"SchemefulSameSiteUnspecifiedTreatedAsLax":return"same-site";case"InvalidDomain":return"domain";case"InvalidPrefix":return"name";case"SamePartyConflictsWithOtherAttributes":case"SamePartyFromCrossPartyContext":case"NameValuePairExceedsMaxSize":case"UserPreferences":case"ThirdPartyPhaseout":case"SyntaxError":case"SchemeNotSupported":case"UnknownError":case"DisallowedCharacter":return null}return null};var jn=Object.freeze({__proto__:null,NetworkRequest:Fn,get Events(){return Bn},get WebSocketFrameType(){return Un},cookieExemptionReasonToUiString:function(e){switch(e){case"UserSetting":return Dn(On.exemptionReasonUserSetting);case"TPCDMetadata":return Dn(On.exemptionReasonTPCDMetadata);case"TPCDDeprecationTrial":return Dn(On.exemptionReasonTPCDDeprecationTrial);case"TPCDHeuristics":return Dn(On.exemptionReasonTPCDHeuristics);case"EnterprisePolicy":return Dn(On.exemptionReasonEnterprisePolicy);case"StorageAccess":return Dn(On.exemptionReasonStorageAccessAPI);case"TopLevelStorageAccess":return Dn(On.exemptionReasonTopLevelStorageAccessAPI);case"CorsOptIn":return Dn(On.exemptionReasonCorsOptIn)}return""},cookieBlockedReasonToUiString:Hn,setCookieBlockedReasonToUiString:qn,cookieBlockedReasonToAttribute:_n,setCookieBlockedReasonToAttribute:zn});class Vn{#do;#Pe;#co;#ho;#uo;#go;#po;#h;#Ye;#u;#mo;#fo;#bo;#yo;constructor(e,t){this.#do=e,this.#Pe=t.nodeId,e.setAXNodeForAXId(this.#Pe,this),t.backendDOMNodeId?(e.setAXNodeForBackendDOMNodeId(t.backendDOMNodeId,this),this.#co=t.backendDOMNodeId,this.#ho=new Yr(e.target(),t.backendDOMNodeId)):(this.#co=null,this.#ho=null),this.#uo=t.ignored,this.#uo&&"ignoredReasons"in t&&(this.#go=t.ignoredReasons),this.#po=t.role||null,this.#h=t.name||null,this.#Ye=t.description||null,this.#u=t.value||null,this.#mo=t.properties||null,this.#yo=t.childIds||null,this.#fo=t.parentId||null,t.frameId&&!t.parentId?(this.#bo=t.frameId,e.setRootAXNodeForFrameId(t.frameId,this)):this.#bo=null}id(){return this.#Pe}accessibilityModel(){return this.#do}ignored(){return this.#uo}ignoredReasons(){return this.#go||null}role(){return this.#po||null}coreProperties(){const e=[];return this.#h&&e.push({name:"name",value:this.#h}),this.#Ye&&e.push({name:"description",value:this.#Ye}),this.#u&&e.push({name:"value",value:this.#u}),e}name(){return this.#h||null}description(){return this.#Ye||null}value(){return this.#u||null}properties(){return this.#mo||null}parentNode(){return this.#fo?this.#do.axNodeForId(this.#fo):null}isDOMNode(){return Boolean(this.#co)}backendDOMNodeId(){return this.#co}deferredDOMNode(){return this.#ho}highlightDOMNode(){const e=this.deferredDOMNode();e&&e.highlight()}children(){if(!this.#yo)return[];const e=[];for(const t of this.#yo){const r=this.#do.axNodeForId(t);r&&e.push(r)}return e}numChildren(){return this.#yo?this.#yo.length:0}hasOnlyUnloadedChildren(){return!(!this.#yo||!this.#yo.length)&&this.#yo.every((e=>null===this.#do.axNodeForId(e)))}hasUnloadedChildren(){return!(!this.#yo||!this.#yo.length)&&this.#yo.some((e=>null===this.#do.axNodeForId(e)))}getFrameId(){return this.#bo||this.parentNode()?.getFrameId()||null}}class Wn extends c{agent;#vo;#Io;#ko;#wo;#So;constructor(e){super(e),e.registerAccessibilityDispatcher(this),this.agent=e.accessibilityAgent(),this.resumeModel(),this.#vo=new Map,this.#Io=new Map,this.#ko=new Map,this.#wo=new Map,this.#So=null}clear(){this.#So=null,this.#vo.clear(),this.#Io.clear(),this.#ko.clear()}async resumeModel(){await this.agent.invoke_enable()}async suspendModel(){await this.agent.invoke_disable()}async requestPartialAXTree(e){const{nodes:t}=await this.agent.invoke_getPartialAXTree({nodeId:e.id,fetchRelatives:!0});if(!t)return;const r=[];for(const e of t)r.push(new Vn(this,e))}loadComplete({root:e}){this.clear(),this.#So=new Vn(this,e),this.dispatchEventToListeners("TreeUpdated",{root:this.#So})}nodesUpdated({nodes:e}){this.createNodesFromPayload(e),this.dispatchEventToListeners("TreeUpdated",{})}createNodesFromPayload(e){return e.map((e=>new Vn(this,e)))}async requestRootNode(e){if(e&&this.#ko.has(e))return this.#ko.get(e);if(!e&&this.#So)return this.#So;const{node:t}=await this.agent.invoke_getRootAXNode({frameId:e});return t?this.createNodesFromPayload([t])[0]:void 0}async requestAXChildren(e,t){const r=this.#vo.get(e);if(!r)throw Error("Cannot request children before parent");if(!r.hasUnloadedChildren())return r.children();const n=this.#wo.get(e);if(n)await n;else{const r=this.agent.invoke_getChildAXNodes({id:e,frameId:t});this.#wo.set(e,r);const n=await r;n.getError()||(this.createNodesFromPayload(n.nodes),this.#wo.delete(e))}return r.children()}async requestAndLoadSubTreeToNode(e){const t=[];let r=this.axNodeForDOMNode(e);for(;r;){t.push(r);const e=r.parentNode();if(!e)return t;r=e}const{nodes:n}=await this.agent.invoke_getAXNodeAndAncestors({backendNodeId:e.backendNodeId()});if(!n)return null;return this.createNodesFromPayload(n)}axNodeForId(e){return this.#vo.get(e)||null}setRootAXNodeForFrameId(e,t){this.#ko.set(e,t)}axNodeForFrameId(e){return this.#ko.get(e)??null}setAXNodeForAXId(e,t){this.#vo.set(e,t)}axNodeForDOMNode(e){return e?this.#Io.get(e.backendNodeId())??null:null}setAXNodeForBackendDOMNodeId(e,t){this.#Io.set(e,t)}getAgent(){return this.agent}}c.register(Wn,{capabilities:2,autostart:!1});var Gn=Object.freeze({__proto__:null,AccessibilityNode:Vn,AccessibilityModel:Wn});class Kn extends c{agent;#wr;constructor(e){super(e),this.agent=e.autofillAgent(),e.registerAutofillDispatcher(this),this.enable()}enable(){this.#wr||o.InspectorFrontendHost.isUnderTest()||(this.agent.invoke_enable(),this.#wr=!0)}disable(){this.#wr&&!o.InspectorFrontendHost.isUnderTest()&&(this.#wr=!1,this.agent.invoke_disable())}addressFormFilled(e){this.dispatchEventToListeners("AddressFormFilled",{autofillModel:this,event:e})}}c.register(Kn,{capabilities:2,autostart:!0});var Qn=Object.freeze({__proto__:null,AutofillModel:Kn});class $n{name;#Co;enabledInternal;constructor(e,t){this.#Co=e,this.name=t,this.enabledInternal=!1}category(){return this.#Co}enabled(){return this.enabledInternal}setEnabled(e){this.enabledInternal=e}}var Xn=Object.freeze({__proto__:null,CategorizedBreakpoint:$n});class Jn{onMessage;#Ro;#xo;#To;#je;constructor(){this.onMessage=null,this.#Ro=null,this.#xo="",this.#To=0,this.#je=[o.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(o.InspectorFrontendHostAPI.Events.DispatchMessage,this.dispatchMessage,this),o.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(o.InspectorFrontendHostAPI.Events.DispatchMessageChunk,this.dispatchMessageChunk,this)]}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.#Ro=e}sendRawMessage(e){this.onMessage&&o.InspectorFrontendHost.InspectorFrontendHostInstance.sendMessageToBackend(e)}dispatchMessage(e){this.onMessage&&this.onMessage.call(null,e.data)}dispatchMessageChunk(e){const{messageChunk:t,messageSize:r}=e.data;r&&(this.#xo="",this.#To=r),this.#xo+=t,this.#xo.length===this.#To&&this.onMessage&&(this.onMessage.call(null,this.#xo),this.#xo="",this.#To=0)}async disconnect(){const t=this.#Ro;e.EventTarget.removeEventListeners(this.#je),this.#Ro=null,this.onMessage=null,t&&t.call(null,"force disconnect")}}class Yn{#Mo;onMessage;#Ro;#Po;#Lo;#Ao;constructor(e,t){this.#Mo=new WebSocket(e),this.#Mo.onerror=this.onError.bind(this),this.#Mo.onopen=this.onOpen.bind(this),this.#Mo.onmessage=e=>{this.onMessage&&this.onMessage.call(null,e.data)},this.#Mo.onclose=this.onClose.bind(this),this.onMessage=null,this.#Ro=null,this.#Po=t,this.#Lo=!1,this.#Ao=[]}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.#Ro=e}onError(){this.#Po&&this.#Po.call(null),this.#Ro&&this.#Ro.call(null,"connection failed"),this.close()}onOpen(){if(this.#Lo=!0,this.#Mo){this.#Mo.onerror=console.error;for(const e of this.#Ao)this.#Mo.send(e)}this.#Ao=[]}onClose(){this.#Po&&this.#Po.call(null),this.#Ro&&this.#Ro.call(null,"websocket closed"),this.close()}close(e){this.#Mo&&(this.#Mo.onerror=null,this.#Mo.onopen=null,this.#Mo.onclose=e||null,this.#Mo.onmessage=null,this.#Mo.close(),this.#Mo=null),this.#Po=null}sendRawMessage(e){this.#Lo&&this.#Mo?this.#Mo.send(e):this.#Ao.push(e)}disconnect(){return new Promise((e=>{this.close((()=>{this.#Ro&&this.#Ro.call(null,"force disconnect"),e()}))}))}}class Zn{onMessage;#Ro;constructor(){this.onMessage=null,this.#Ro=null}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.#Ro=e}sendRawMessage(e){window.setTimeout(this.respondWithError.bind(this,e),0)}respondWithError(e){const t=JSON.parse(e),r={message:"This is a stub connection, can't dispatch message.",code:l.InspectorBackend.DevToolsStubErrorCode,data:t};this.onMessage&&this.onMessage.call(null,{id:t.id,error:r})}async disconnect(){this.#Ro&&this.#Ro.call(null,"force disconnect"),this.#Ro=null,this.onMessage=null}}class es{#Eo;#Oo;onMessage;#Ro;constructor(e,t){this.#Eo=e,this.#Oo=t,this.onMessage=null,this.#Ro=null}setOnMessage(e){this.onMessage=e}setOnDisconnect(e){this.#Ro=e}getOnDisconnect(){return this.#Ro}sendRawMessage(e){const t=JSON.parse(e);t.sessionId||(t.sessionId=this.#Oo),this.#Eo.sendRawMessage(JSON.stringify(t))}getSessionId(){return this.#Oo}async disconnect(){this.#Ro&&this.#Ro.call(null,"force disconnect"),this.#Ro=null,this.onMessage=null}}function ts(e){const t=a.Runtime.Runtime.queryParam("ws"),r=a.Runtime.Runtime.queryParam("wss");if(t||r){const n=t?"ws":"wss";let s=t||r;o.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()&&s.startsWith("/")&&(s=`${window.location.host}${s}`);return new Yn(`${n}://${s}`,e)}return o.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()?new Zn:new Jn}var rs=Object.freeze({__proto__:null,MainConnection:Jn,WebSocketConnection:Yn,StubConnection:Zn,ParallelConnection:es,initMainConnection:async function(e,t){l.InspectorBackend.Connection.setFactory(ts.bind(null,t)),await e(),o.InspectorFrontendHost.InspectorFrontendHostInstance.connectionReady(),o.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(o.InspectorFrontendHostAPI.Events.ReattachRootTarget,(()=>{const t=je.instance().rootTarget();if(t){const e=t.router();e&&e.connection().disconnect()}e()}))}});const ns={main:"Main"},ss=i.i18n.registerUIStrings("core/sdk/ChildTargetManager.ts",ns),is=i.i18n.getLocalizedString.bind(void 0,ss);class as extends c{#No;#Do;#Fo;#Bo=new Map;#Uo=new Map;#Ho=new Map;#qo=new Map;#_o=null;constructor(e){super(e),this.#No=e.targetManager(),this.#Do=e,this.#Fo=e.targetAgent(),e.registerTargetDispatcher(this);const t=this.#No.browserTarget();t?t!==e&&t.targetAgent().invoke_autoAttachRelated({targetId:e.id(),waitForDebuggerOnStart:!0}):this.#Fo.invoke_setAutoAttach({autoAttach:!0,waitForDebuggerOnStart:!0,flatten:!0}),e.parentTarget()?.type()===Ue.Frame||o.InspectorFrontendHost.isUnderTest()||(this.#Fo.invoke_setDiscoverTargets({discover:!0}),this.#Fo.invoke_setRemoteLocations({locations:[{host:"localhost",port:9229}]}))}static install(e){as.attachCallback=e,c.register(as,{capabilities:32,autostart:!0})}childTargets(){return Array.from(this.#Uo.values())}async suspendModel(){await this.#Fo.invoke_setAutoAttach({autoAttach:!0,waitForDebuggerOnStart:!1,flatten:!0})}async resumeModel(){await this.#Fo.invoke_setAutoAttach({autoAttach:!0,waitForDebuggerOnStart:!0,flatten:!0})}dispose(){for(const e of this.#Uo.keys())this.detachedFromTarget({sessionId:e,targetId:void 0})}targetCreated({targetInfo:e}){this.#Bo.set(e.targetId,e),this.fireAvailableTargetsChanged(),this.dispatchEventToListeners("TargetCreated",e)}targetInfoChanged({targetInfo:e}){this.#Bo.set(e.targetId,e);const t=this.#Ho.get(e.targetId);if(t)if("prerender"!==t.targetInfo()?.subtype||e.subtype)t.updateTargetInfo(e);else{const r=t.model(mn);t.updateTargetInfo(e),r&&r.mainFrame&&r.primaryPageChanged(r.mainFrame,"Activation"),t.setName(is(ns.main))}this.fireAvailableTargetsChanged(),this.dispatchEventToListeners("TargetInfoChanged",e)}targetDestroyed({targetId:e}){this.#Bo.delete(e),this.fireAvailableTargetsChanged(),this.dispatchEventToListeners("TargetDestroyed",e)}targetCrashed(e){}fireAvailableTargetsChanged(){je.instance().dispatchEventToListeners("AvailableTargetsChanged",[...this.#Bo.values()])}async getParentTargetId(){return this.#_o||(this.#_o=(await this.#Do.targetAgent().invoke_getTargetInfo({})).targetInfo.targetId),this.#_o}async getTargetInfo(){return(await this.#Do.targetAgent().invoke_getTargetInfo({})).targetInfo}async attachedToTarget({sessionId:t,targetInfo:r,waitingForDebugger:n}){if(this.#_o===r.targetId)return;let s=Ue.Browser,i="";if("worker"===r.type&&r.title&&r.title!==r.url)i=r.title;else if(!["page","iframe","webview"].includes(r.type)){if(["^chrome://print/$","^chrome://file-manager/","^chrome://feedback/","^chrome://.*\\.top-chrome/$","^chrome://view-cert/$","^devtools://"].some((e=>r.url.match(e))))s=Ue.Frame;else{const t=e.ParsedURL.ParsedURL.fromString(r.url);i=t?t.lastPathComponentWithFragment():"#"+ ++as.lastAnonymousTargetId}}"iframe"===r.type||"webview"===r.type||"background_page"===r.type||"app"===r.type||"popup_page"===r.type||"page"===r.type?s=Ue.Frame:"worker"===r.type?s=Ue.Worker:"shared_worker"===r.type?s=Ue.SharedWorker:"shared_storage_worklet"===r.type?s=Ue.SharedStorageWorklet:"service_worker"===r.type?s=Ue.ServiceWorker:"auction_worklet"===r.type&&(s=Ue.AuctionWorklet);const a=this.#No.createTarget(r.targetId,i,s,this.#Do,t,void 0,void 0,r);this.#Uo.set(t,a),this.#Ho.set(a.id(),a),as.attachCallback&&await as.attachCallback({target:a,waitingForDebugger:n}),n&&a.runtimeAgent().invoke_runIfWaitingForDebugger()}detachedFromTarget({sessionId:e}){if(this.#qo.has(e))this.#qo.delete(e);else{const t=this.#Uo.get(e);t&&(t.dispose("target terminated"),this.#Uo.delete(e),this.#Ho.delete(t.id()))}}receivedMessageFromTarget({}){}async createParallelConnection(e){const t=await this.getParentTargetId(),{connection:r,sessionId:n}=await this.createParallelConnectionAndSessionForTarget(this.#Do,t);return r.setOnMessage(e),this.#qo.set(n,r),{connection:r,sessionId:n}}async createParallelConnectionAndSessionForTarget(e,t){const r=e.targetAgent(),n=e.router(),s=(await r.invoke_attachToTarget({targetId:t,flatten:!0})).sessionId,i=new es(n.connection(),s);return n.registerSession(e,s,i),i.setOnDisconnect((()=>{n.unregisterSession(s),r.invoke_detachFromTarget({sessionId:s})})),{connection:i,sessionId:s}}targetInfos(){return Array.from(this.#Bo.values())}static lastAnonymousTargetId=0;static attachCallback}var os=Object.freeze({__proto__:null,ChildTargetManager:as});const ls={couldNotLoadContentForSS:"Could not load content for {PH1} ({PH2})"},ds=i.i18n.registerUIStrings("core/sdk/CompilerSourceMappingContentProvider.ts",ls),cs=i.i18n.getLocalizedString.bind(void 0,ds);var hs,us,gs=Object.freeze({__proto__:null,CompilerSourceMappingContentProvider:class{#zo;#jo;#Vo;constructor(e,t,r){this.#zo=e,this.#jo=t,this.#Vo=r}contentURL(){return this.#zo}contentType(){return this.#jo}async requestContent(){const e=await this.requestContentData();return s.ContentData.ContentData.asDeferredContent(e)}async requestContentData(){try{const{content:e}=await Et.instance().loadResource(this.#zo,this.#Vo);return new s.ContentData.ContentData(e,!1,this.#jo.canonicalMimeType())}catch(e){const t=cs(ls.couldNotLoadContentForSS,{PH1:this.#zo,PH2:e.message});return console.error(t),{error:t}}}async searchInContent(e,t,r){const n=await this.requestContentData();return s.TextUtils.performSearchInContentData(n,e,t,r)}}});!function(e){e.Result="result",e.Command="command",e.System="system",e.QueryObjectResult="queryObjectResult"}(hs||(hs={})),function(e){e.CSS="css",e.ConsoleAPI="console-api"}(us||(us={}));const ps={profileD:"Profile {PH1}"},ms=i.i18n.registerUIStrings("core/sdk/CPUProfilerModel.ts",ps),fs=i.i18n.getLocalizedString.bind(void 0,ms);class bs extends c{#Wo;#Go;#Ko;#Qo;#$o;#Xo;registeredConsoleProfileMessages=[];constructor(e){super(e),this.#Wo=!1,this.#Go=1,this.#Ko=new Map,this.#Qo=e.profilerAgent(),this.#$o=null,e.registerProfilerDispatcher(this),this.#Qo.invoke_enable(),this.#Xo=e.model(Cr)}runtimeModel(){return this.#Xo.runtimeModel()}debuggerModel(){return this.#Xo}consoleProfileStarted({id:e,location:t,title:r}){r||(r=fs(ps.profileD,{PH1:this.#Go++}),this.#Ko.set(e,r));const n=this.createEventDataFrom(e,t,r);this.dispatchEventToListeners("ConsoleProfileStarted",n)}consoleProfileFinished({id:e,location:t,profile:r,title:n}){n||(n=this.#Ko.get(e),this.#Ko.delete(e));const s={...this.createEventDataFrom(e,t,n),cpuProfile:r};this.registeredConsoleProfileMessages.push(s),this.dispatchEventToListeners("ConsoleProfileFinished",s)}createEventDataFrom(e,t,r){const n=Pr.fromPayload(this.#Xo,t);return{id:this.target().id()+"."+e,scriptLocation:n,title:r||"",cpuProfilerModel:this}}isRecordingProfile(){return this.#Wo}startRecording(){this.#Wo=!0;return this.#Qo.invoke_setSamplingInterval({interval:100}),this.#Qo.invoke_start()}stopRecording(){return this.#Wo=!1,this.#Qo.invoke_stop().then((e=>e.profile||null))}startPreciseCoverage(e,t){this.#$o=t;return this.#Qo.invoke_startPreciseCoverage({callCount:!1,detailed:e,allowTriggeredUpdates:!0})}async takePreciseCoverage(){const e=await this.#Qo.invoke_takePreciseCoverage();return{timestamp:e&&e.timestamp||0,coverage:e&&e.result||[]}}stopPreciseCoverage(){return this.#$o=null,this.#Qo.invoke_stopPreciseCoverage()}preciseCoverageDeltaUpdate({timestamp:e,occasion:t,result:r}){this.#$o&&this.#$o(e,t,r)}}c.register(bs,{capabilities:4,autostart:!0});var ys=Object.freeze({__proto__:null,CPUProfilerModel:bs});class vs extends c{#Jo;constructor(e){super(e),e.registerLogDispatcher(this),this.#Jo=e.logAgent(),this.#Jo.invoke_enable(),o.InspectorFrontendHost.isUnderTest()||this.#Jo.invoke_startViolationsReport({config:[{name:"longTask",threshold:200},{name:"longLayout",threshold:30},{name:"blockedEvent",threshold:100},{name:"blockedParser",threshold:-1},{name:"handler",threshold:150},{name:"recurringHandler",threshold:50},{name:"discouragedAPIUse",threshold:-1}]})}entryAdded({entry:e}){this.dispatchEventToListeners("EntryAdded",{logModel:this,entry:e})}requestClear(){this.#Jo.invoke_clear()}}c.register(vs,{capabilities:8,autostart:!0});var Is=Object.freeze({__proto__:null,LogModel:vs});const ks={navigatedToS:"Navigated to {PH1}",bfcacheNavigation:"Navigation to {PH1} was restored from back/forward cache (see https://web.dev/bfcache/)",profileSStarted:"Profile ''{PH1}'' started.",profileSFinished:"Profile ''{PH1}'' finished.",failedToSaveToTempVariable:"Failed to save to temp variable."},ws=i.i18n.registerUIStrings("core/sdk/ConsoleModel.ts",ks),Ss=i.i18n.getLocalizedString.bind(void 0,ws);class Cs extends c{#Yo;#Zo;#el;#tl;#rl;#nl;#sl;#il;constructor(r){super(r),this.#Yo=[],this.#Zo=new t.MapUtilities.Multimap,this.#el=new Map,this.#tl=0,this.#rl=0,this.#nl=0,this.#sl=0,this.#il=new WeakMap;const n=r.model(mn);if(!n||n.cachedResourcesLoaded())return void this.initTarget(r);const s=n.addEventListener(gn.CachedResourcesLoaded,(()=>{e.EventTarget.removeEventListeners([s]),this.initTarget(r)}))}initTarget(e){const t=[],r=e.model(bs);r&&(t.push(r.addEventListener("ConsoleProfileStarted",this.consoleProfileStarted.bind(this,r))),t.push(r.addEventListener("ConsoleProfileFinished",this.consoleProfileFinished.bind(this,r))));const n=e.model(mn);n&&e.parentTarget()?.type()!==Ue.Frame&&t.push(n.addEventListener(gn.PrimaryPageChanged,this.primaryPageChanged,this));const s=e.model(ar);s&&(t.push(s.addEventListener(lr.ExceptionThrown,this.exceptionThrown.bind(this,s))),t.push(s.addEventListener(lr.ExceptionRevoked,this.exceptionRevoked.bind(this,s))),t.push(s.addEventListener(lr.ConsoleAPICalled,this.consoleAPICalled.bind(this,s))),e.parentTarget()?.type()!==Ue.Frame&&t.push(s.debuggerModel().addEventListener(Tr.GlobalObjectCleared,this.clearIfNecessary,this)),t.push(s.addEventListener(lr.QueryObjectRequested,this.queryObjectRequested.bind(this,s)))),this.#il.set(e,t)}targetRemoved(t){const r=t.model(ar);r&&this.#el.delete(r),e.EventTarget.removeEventListeners(this.#il.get(t)||[])}async evaluateCommandInConsole(t,r,n,s){const i=await t.evaluate({expression:n,objectGroup:"console",includeCommandLineAPI:s,silent:!1,returnByValue:!1,generatePreview:!0,replMode:!0,allowUnsafeEvalBlockedByCSP:!1},e.Settings.Settings.instance().moduleSetting("console-user-activation-eval").get(),!1);o.userMetrics.actionTaken(o.UserMetrics.Action.ConsoleEvaluated),"error"in i||(await e.Console.Console.instance().showPromise(),this.dispatchEventToListeners(Rs.CommandEvaluated,{result:i.object,commandMessage:r,exceptionDetails:i.exceptionDetails}))}addCommandMessage(e,t){const r=new Ts(e.runtimeModel,"javascript",null,t,{type:hs.Command});return r.setExecutionContextId(e.id),this.addMessage(r),r}addMessage(e){e.setPageLoadSequenceNumber(this.#sl),e.source===us.ConsoleAPI&&"clear"===e.type&&this.clearIfNecessary(),this.#Yo.push(e),this.#Zo.set(e.timestamp,e);const t=e.runtimeModel(),r=e.getExceptionId();if(r&&t){let n=this.#el.get(t);n||(n=new Map,this.#el.set(t,n)),n.set(r,e)}this.incrementErrorWarningCount(e),this.dispatchEventToListeners(Rs.MessageAdded,e)}exceptionThrown(e,t){const r=t.data,n=function(e){if(!e)return;return{requestId:e.requestId||void 0,issueId:e.issueId||void 0}}(r.details.exceptionMetaData),s=Ts.fromException(e,r.details,void 0,r.timestamp,void 0,n);s.setExceptionId(r.details.exceptionId),this.addMessage(s)}exceptionRevoked(e,t){const r=t.data,n=this.#el.get(e),s=n?n.get(r):null;s&&(this.#rl--,s.level="verbose",this.dispatchEventToListeners(Rs.MessageUpdated,s))}consoleAPICalled(e,t){const r=t.data;let n="info";"debug"===r.type?n="verbose":"error"===r.type||"assert"===r.type?n="error":"warning"===r.type?n="warning":"info"!==r.type&&"log"!==r.type||(n="info");let s="";r.args.length&&r.args[0].unserializableValue?s=r.args[0].unserializableValue:r.args.length&&("object"!=typeof r.args[0].value&&void 0!==r.args[0].value||null===r.args[0].value)?s=String(r.args[0].value):r.args.length&&r.args[0].description&&(s=r.args[0].description);const i=r.stackTrace&&r.stackTrace.callFrames.length?r.stackTrace.callFrames[0]:null,a={type:r.type,url:i?.url,line:i?.lineNumber,column:i?.columnNumber,parameters:r.args,stackTrace:r.stackTrace,timestamp:r.timestamp,executionContextId:r.executionContextId,context:r.context},o=new Ts(e,us.ConsoleAPI,n,s,a);for(const e of this.#Zo.get(o.timestamp).values())if(o.isEqual(e))return;this.addMessage(o)}queryObjectRequested(e,t){const{objects:r,executionContextId:n}=t.data,s={type:hs.QueryObjectResult,parameters:[r],executionContextId:n},i=new Ts(e,us.ConsoleAPI,"info","",s);this.addMessage(i)}clearIfNecessary(){e.Settings.Settings.instance().moduleSetting("preserve-console-log").get()||this.clear(),++this.#sl}primaryPageChanged(t){if(e.Settings.Settings.instance().moduleSetting("preserve-console-log").get()){const{frame:r}=t.data;r.backForwardCacheDetails.restoredFromCache?e.Console.Console.instance().log(Ss(ks.bfcacheNavigation,{PH1:r.url})):e.Console.Console.instance().log(Ss(ks.navigatedToS,{PH1:r.url}))}}consoleProfileStarted(e,t){const{data:r}=t;this.addConsoleProfileMessage(e,"profile",r.scriptLocation,Ss(ks.profileSStarted,{PH1:r.title}))}consoleProfileFinished(e,t){const{data:r}=t;this.addConsoleProfileMessage(e,"profileEnd",r.scriptLocation,Ss(ks.profileSFinished,{PH1:r.title}))}addConsoleProfileMessage(e,t,r,n){const s=r.script(),i=[{functionName:"",scriptId:r.scriptId,url:s?s.contentURL():"",lineNumber:r.lineNumber,columnNumber:r.columnNumber||0}];this.addMessage(new Ts(e.runtimeModel(),us.ConsoleAPI,"info",n,{type:t,stackTrace:{callFrames:i}}))}incrementErrorWarningCount(e){if("violation"!==e.source)switch(e.level){case"warning":this.#tl++;break;case"error":this.#rl++}else this.#nl++}messages(){return this.#Yo}static allMessagesUnordered(){const e=[];for(const t of je.instance().targets()){const r=t.model(Cs)?.messages()||[];e.push(...r)}return e}static requestClearMessages(){for(const e of je.instance().models(vs))e.requestClear();for(const e of je.instance().models(ar))e.discardConsoleEntries();for(const e of je.instance().targets())e.model(Cs)?.clear()}clear(){this.#Yo=[],this.#Zo.clear(),this.#el.clear(),this.#rl=0,this.#tl=0,this.#nl=0,this.dispatchEventToListeners(Rs.ConsoleCleared)}errors(){return this.#rl}static allErrors(){let e=0;for(const t of je.instance().targets())e+=t.model(Cs)?.errors()||0;return e}warnings(){return this.#tl}static allWarnings(){let e=0;for(const t of je.instance().targets())e+=t.model(Cs)?.warnings()||0;return e}violations(){return this.#nl}static allViolations(){let e=0;for(const t of je.instance().targets())e+=t.model(Cs)?.violations()||0;return e}async saveToTempVariable(t,r){if(!r||!t)return void o(null);const n=t,s=await n.globalObject("",!1);if("error"in s||Boolean(s.exceptionDetails)||!s.object)return void o("object"in s&&s.object||null);const i=s.object,a=await i.callFunction((function(e){const t="temp";let r=1;for(;t+r in this;)++r;const n=t+r;return this[n]=e,n}),[Qe.toCallArgument(r)]);if(i.release(),a.wasThrown||!a.object||"string"!==a.object.type)o(a.object||null);else{const e=a.object.value,t=this.addCommandMessage(n,e);this.evaluateCommandInConsole(n,t,e,!1)}function o(t){let r=Ss(ks.failedToSaveToTempVariable);t&&(r=r+" "+t.description),e.Console.Console.instance().error(r)}a.object&&a.object.release()}}var Rs;function xs(e,t){if(!e!=!t)return!1;if(!e||!t)return!0;const r=e.callFrames,n=t.callFrames;if(r.length!==n.length)return!1;for(let e=0,t=r.length;e<t;++e)if(r[e].scriptId!==n[e].scriptId||r[e].functionName!==n[e].functionName||r[e].lineNumber!==n[e].lineNumber||r[e].columnNumber!==n[e].columnNumber)return!1;return xs(e.parent,t.parent)}!function(e){e.ConsoleCleared="ConsoleCleared",e.MessageAdded="MessageAdded",e.MessageUpdated="MessageUpdated",e.CommandEvaluated="CommandEvaluated"}(Rs||(Rs={}));class Ts{#Cr;source;level;messageText;type;url;line;column;parameters;stackTrace;timestamp;#al;scriptId;workerId;context;#ol=null;#sl=void 0;#ll=void 0;#dl;category;stackFrameWithBreakpoint=null;#cl=null;constructor(e,t,r,n,s){if(this.#Cr=e,this.source=t,this.level=r,this.messageText=n,this.type=s?.type||"log",this.url=s?.url,this.line=s?.line||0,this.column=s?.column||0,this.parameters=s?.parameters,this.stackTrace=s?.stackTrace,this.timestamp=s?.timestamp||Date.now(),this.#al=s?.executionContextId||0,this.scriptId=s?.scriptId,this.workerId=s?.workerId,this.#dl=s?.affectedResources,this.category=s?.category,!this.#al&&this.#Cr&&(this.scriptId?this.#al=this.#Cr.executionContextIdForScriptId(this.scriptId):this.stackTrace&&(this.#al=this.#Cr.executionContextForStackTrace(this.stackTrace))),s?.context){const e=s?.context.match(/[^#]*/);this.context=e?.[0]}if(this.stackTrace){const{callFrame:e,type:t}=Ts.#hl(this.stackTrace);this.stackFrameWithBreakpoint=e,this.#cl=t}}getAffectedResources(){return this.#dl}setPageLoadSequenceNumber(e){this.#sl=e}static fromException(e,t,r,n,s,i){const a={type:r,url:s||t.url,line:t.lineNumber,column:t.columnNumber,parameters:t.exception?[Qe.fromLocalObject(t.text),t.exception]:void 0,stackTrace:t.stackTrace,timestamp:n,executionContextId:t.executionContextId,scriptId:t.scriptId,affectedResources:i};return new Ts(e,"javascript","error",ar.simpleTextFromException(t),a)}runtimeModel(){return this.#Cr}target(){return this.#Cr?this.#Cr.target():null}setOriginatingMessage(e){this.#ol=e,this.#al=e.#al}originatingMessage(){return this.#ol}setExecutionContextId(e){this.#al=e}getExecutionContextId(){return this.#al}getExceptionId(){return this.#ll}setExceptionId(e){this.#ll=e}isGroupMessage(){return"startGroup"===this.type||"startGroupCollapsed"===this.type||"endGroup"===this.type}isGroupStartMessage(){return"startGroup"===this.type||"startGroupCollapsed"===this.type}isErrorOrWarning(){return"warning"===this.level||"error"===this.level}isGroupable(){const e="error"===this.level&&("javascript"===this.source||"network"===this.source);return this.source!==us.ConsoleAPI&&this.type!==hs.Command&&this.type!==hs.Result&&this.type!==hs.System&&!e}groupCategoryKey(){return[this.source,this.level,this.type,this.#sl].join(":")}isEqual(e){if(!e)return!1;if(this.parameters){if(!e.parameters||this.parameters.length!==e.parameters.length)return!1;for(let t=0;t<e.parameters.length;++t){const r=e.parameters[t],n=this.parameters[t];if("string"==typeof r||"string"==typeof n)return!1;if("object"===r.type&&"error"!==r.subtype)return!1;if(n.type!==r.type||n.value!==r.value||n.description!==r.description)return!1}}return this.runtimeModel()===e.runtimeModel()&&this.source===e.source&&this.type===e.type&&this.level===e.level&&this.line===e.line&&this.url===e.url&&this.scriptId===e.scriptId&&this.messageText===e.messageText&&this.#al===e.#al&&(t=this.#dl,r=e.#dl,t?.requestId===r?.requestId)&&xs(this.stackTrace,e.stackTrace);var t,r}get originatesFromLogpoint(){return"LOGPOINT"===this.#cl}get originatesFromConditionalBreakpoint(){return"CONDITIONAL_BREAKPOINT"===this.#cl}static#hl({callFrames:e}){const t=[Dr,Nr],r=e.findLastIndex((({url:e})=>t.includes(e)));if(-1===r||r===e.length-1)return{callFrame:null,type:null};const n=e[r].url===Nr?"LOGPOINT":"CONDITIONAL_BREAKPOINT";return{callFrame:e[r+1],type:n}}}c.register(Cs,{capabilities:4,autostart:!0});const Ms=new Map([["xml","xml"],["javascript","javascript"],["network","network"],[us.ConsoleAPI,"console-api"],["storage","storage"],["appcache","appcache"],["rendering","rendering"],[us.CSS,"css"],["security","security"],["deprecation","deprecation"],["worker","worker"],["violation","violation"],["intervention","intervention"],["recommendation","recommendation"],["other","other"]]);var Ps=Object.freeze({__proto__:null,ConsoleModel:Cs,get Events(){return Rs},ConsoleMessage:Ts,MessageSourceDisplayName:Ms,get FrontendMessageSource(){return us},get FrontendMessageType(){return hs}});class Ls extends n.CPUProfileDataModel.CPUProfileDataModel{}var As=Object.freeze({__proto__:null,CPUProfileDataModel:Ls});class Es extends c{#ul;#gl;#J;#pl;#ml;#fl;#bl;#yl;#vl;#Il;constructor(t){super(t),this.#ul=t.emulationAgent(),this.#gl=t.deviceOrientationAgent(),this.#J=t.model(Kt),this.#pl=t.model(Wr),this.#pl&&this.#pl.addEventListener("InspectModeWillBeToggled",(()=>{this.updateTouch()}),this);const r=e.Settings.Settings.instance().moduleSetting("java-script-disabled");r.addChangeListener((async()=>await this.#ul.invoke_setScriptExecutionDisabled({value:r.get()}))),r.get()&&this.#ul.invoke_setScriptExecutionDisabled({value:!0});const n=e.Settings.Settings.instance().moduleSetting("emulation.touch");n.addChangeListener((()=>{const e=n.get();this.overrideEmulateTouch("force"===e)}));const s=e.Settings.Settings.instance().moduleSetting("emulation.idle-detection");s.addChangeListener((async()=>{const e=s.get();if("none"===e)return void await this.clearIdleOverride();const t=JSON.parse(e);await this.setIdleOverride(t)}));const i=e.Settings.Settings.instance().moduleSetting("emulated-css-media"),a=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-color-gamut"),o=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-color-scheme"),l=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-forced-colors"),d=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-contrast"),c=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-reduced-data"),h=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-reduced-transparency"),u=e.Settings.Settings.instance().moduleSetting("emulated-css-media-feature-prefers-reduced-motion");this.#ml=new Map([["type",i.get()],["color-gamut",a.get()],["prefers-color-scheme",o.get()],["forced-colors",l.get()],["prefers-contrast",d.get()],["prefers-reduced-data",c.get()],["prefers-reduced-motion",u.get()],["prefers-reduced-transparency",h.get()]]),i.addChangeListener((()=>{this.#ml.set("type",i.get()),this.updateCssMedia()})),a.addChangeListener((()=>{this.#ml.set("color-gamut",a.get()),this.updateCssMedia()})),o.addChangeListener((()=>{this.#ml.set("prefers-color-scheme",o.get()),this.updateCssMedia()})),l.addChangeListener((()=>{this.#ml.set("forced-colors",l.get()),this.updateCssMedia()})),d.addChangeListener((()=>{this.#ml.set("prefers-contrast",d.get()),this.updateCssMedia()})),c.addChangeListener((()=>{this.#ml.set("prefers-reduced-data",c.get()),this.updateCssMedia()})),u.addChangeListener((()=>{this.#ml.set("prefers-reduced-motion",u.get()),this.updateCssMedia()})),h.addChangeListener((()=>{this.#ml.set("prefers-reduced-transparency",h.get()),this.updateCssMedia()})),this.updateCssMedia();const g=e.Settings.Settings.instance().moduleSetting("emulate-auto-dark-mode");g.addChangeListener((()=>{const e=g.get();o.setDisabled(e),o.set(e?"dark":""),this.emulateAutoDarkMode(e)})),g.get()&&(o.setDisabled(!0),o.set("dark"),this.emulateAutoDarkMode(!0));const p=e.Settings.Settings.instance().moduleSetting("emulated-vision-deficiency");p.addChangeListener((()=>this.emulateVisionDeficiency(p.get()))),p.get()&&this.emulateVisionDeficiency(p.get());const m=e.Settings.Settings.instance().moduleSetting("local-fonts-disabled");m.addChangeListener((()=>this.setLocalFontsDisabled(m.get()))),m.get()&&this.setLocalFontsDisabled(m.get());const f=e.Settings.Settings.instance().moduleSetting("avif-format-disabled"),b=e.Settings.Settings.instance().moduleSetting("webp-format-disabled"),y=()=>{const e=[];f.get()&&e.push("avif"),b.get()&&e.push("webp"),this.setDisabledImageTypes(e)};f.addChangeListener(y),b.addChangeListener(y),(f.get()||b.get())&&y(),this.#yl=!0,this.#fl=!1,this.#bl=!1,this.#vl=!1,this.#Il={enabled:!1,configuration:"mobile"}}setTouchEmulationAllowed(e){this.#yl=e}supportsDeviceEmulation(){return this.target().hasAllCapabilities(4096)}async resetPageScaleFactor(){await this.#ul.invoke_resetPageScaleFactor()}async emulateDevice(e){e?await this.#ul.invoke_setDeviceMetricsOverride(e):await this.#ul.invoke_clearDeviceMetricsOverride()}overlayModel(){return this.#pl}async emulateLocation(e){if(e)if(e.unavailable)await Promise.all([this.#ul.invoke_setGeolocationOverride({}),this.#ul.invoke_setTimezoneOverride({timezoneId:""}),this.#ul.invoke_setLocaleOverride({locale:""}),this.#ul.invoke_setUserAgentOverride({userAgent:St.instance().currentUserAgent()})]);else{function t(e,t){const r=t.getError();return r?Promise.reject({type:e,message:r}):Promise.resolve()}await Promise.all([this.#ul.invoke_setGeolocationOverride({latitude:e.latitude,longitude:e.longitude,accuracy:Os.defaultGeoMockAccuracy}).then((e=>t("emulation-set-location",e))),this.#ul.invoke_setTimezoneOverride({timezoneId:e.timezoneId}).then((e=>t("emulation-set-timezone",e))),this.#ul.invoke_setLocaleOverride({locale:e.locale}).then((e=>t("emulation-set-locale",e))),this.#ul.invoke_setUserAgentOverride({userAgent:St.instance().currentUserAgent(),acceptLanguage:e.locale}).then((e=>t("emulation-set-user-agent",e)))])}else await Promise.all([this.#ul.invoke_clearGeolocationOverride(),this.#ul.invoke_setTimezoneOverride({timezoneId:""}),this.#ul.invoke_setLocaleOverride({locale:""}),this.#ul.invoke_setUserAgentOverride({userAgent:St.instance().currentUserAgent()})])}async emulateDeviceOrientation(e){e?await this.#gl.invoke_setDeviceOrientationOverride({alpha:e.alpha,beta:e.beta,gamma:e.gamma}):await this.#gl.invoke_clearDeviceOrientationOverride()}async setIdleOverride(e){await this.#ul.invoke_setIdleOverride(e)}async clearIdleOverride(){await this.#ul.invoke_clearIdleOverride()}async emulateCSSMedia(e,t){await this.#ul.invoke_setEmulatedMedia({media:e,features:t}),this.#J&&this.#J.mediaQueryResultChanged()}async emulateAutoDarkMode(e){e&&(this.#ml.set("prefers-color-scheme","dark"),await this.updateCssMedia()),await this.#ul.invoke_setAutoDarkModeOverride({enabled:e||void 0})}async emulateVisionDeficiency(e){await this.#ul.invoke_setEmulatedVisionDeficiency({type:e})}setLocalFontsDisabled(e){this.#J&&this.#J.setLocalFontsEnabled(!e)}setDisabledImageTypes(e){this.#ul.invoke_setDisabledImageTypes({imageTypes:e})}async setCPUThrottlingRate(e){await this.#ul.invoke_setCPUThrottlingRate({rate:e})}async setHardwareConcurrency(e){if(e<1)throw new Error("hardwareConcurrency must be a positive value");await this.#ul.invoke_setHardwareConcurrencyOverride({hardwareConcurrency:e})}async emulateTouch(e,t){this.#fl=e&&this.#yl,this.#bl=t&&this.#yl,await this.updateTouch()}async overrideEmulateTouch(e){this.#vl=e&&this.#yl,await this.updateTouch()}async updateTouch(){let e={enabled:this.#fl,configuration:this.#bl?"mobile":"desktop"};this.#vl&&(e={enabled:!0,configuration:"mobile"}),this.#pl&&this.#pl.inspectModeEnabled()&&(e={enabled:!1,configuration:"mobile"}),(this.#Il.enabled||e.enabled)&&(this.#Il.enabled&&e.enabled&&this.#Il.configuration===e.configuration||(this.#Il=e,await this.#ul.invoke_setTouchEmulationEnabled({enabled:e.enabled,maxTouchPoints:1}),await this.#ul.invoke_setEmitTouchEventsForMouse({enabled:e.enabled,configuration:e.configuration})))}async updateCssMedia(){const e=this.#ml.get("type")??"",t=[{name:"color-gamut",value:this.#ml.get("color-gamut")??""},{name:"prefers-color-scheme",value:this.#ml.get("prefers-color-scheme")??""},{name:"forced-colors",value:this.#ml.get("forced-colors")??""},{name:"prefers-contrast",value:this.#ml.get("prefers-contrast")??""},{name:"prefers-reduced-data",value:this.#ml.get("prefers-reduced-data")??""},{name:"prefers-reduced-motion",value:this.#ml.get("prefers-reduced-motion")??""},{name:"prefers-reduced-transparency",value:this.#ml.get("prefers-reduced-transparency")??""}];return this.emulateCSSMedia(e,t)}}class Os{latitude;longitude;timezoneId;locale;unavailable;constructor(e,t,r,n,s){this.latitude=e,this.longitude=t,this.timezoneId=r,this.locale=n,this.unavailable=s}static parseSetting(e){if(e){const[t,r,n,s]=e.split(":"),[i,a]=t.split("@");return new Os(parseFloat(i),parseFloat(a),r,n,Boolean(s))}return new Os(0,0,"","",!1)}static parseUserInput(e,t,r,n){if(!e&&!t)return null;const{valid:s}=Os.latitudeValidator(e),{valid:i}=Os.longitudeValidator(t);if(!s&&!i)return null;const a=s?parseFloat(e):-1,o=i?parseFloat(t):-1;return new Os(a,o,r,n,!1)}static latitudeValidator(e){const t=parseFloat(e);return{valid:/^([+-]?[\d]+(\.\d+)?|[+-]?\.\d+)$/.test(e)&&t>=-90&&t<=90,errorMessage:void 0}}static longitudeValidator(e){const t=parseFloat(e);return{valid:/^([+-]?[\d]+(\.\d+)?|[+-]?\.\d+)$/.test(e)&&t>=-180&&t<=180,errorMessage:void 0}}static timezoneIdValidator(e){return{valid:""===e||/[a-zA-Z]/.test(e),errorMessage:void 0}}static localeValidator(e){return{valid:""===e||/[a-zA-Z]{2}/.test(e),errorMessage:void 0}}toSetting(){return`${this.latitude}@${this.longitude}:${this.timezoneId}:${this.locale}:${this.unavailable||""}`}static defaultGeoMockAccuracy=150}class Ns{alpha;beta;gamma;constructor(e,t,r){this.alpha=e,this.beta=t,this.gamma=r}static parseSetting(e){if(e){const t=JSON.parse(e);return new Ns(t.alpha,t.beta,t.gamma)}return new Ns(0,0,0)}static parseUserInput(e,t,r){if(!e&&!t&&!r)return null;const{valid:n}=Ns.alphaAngleValidator(e),{valid:s}=Ns.betaAngleValidator(t),{valid:i}=Ns.gammaAngleValidator(r);if(!n&&!s&&!i)return null;const a=n?parseFloat(e):-1,o=s?parseFloat(t):-1,l=i?parseFloat(r):-1;return new Ns(a,o,l)}static angleRangeValidator(e,t){const r=parseFloat(e);return{valid:/^([+-]?[\d]+(\.\d+)?|[+-]?\.\d+)$/.test(e)&&r>=t.minimum&&r<t.maximum,errorMessage:void 0}}static alphaAngleValidator(e){return Ns.angleRangeValidator(e,{minimum:0,maximum:360})}static betaAngleValidator(e){return Ns.angleRangeValidator(e,{minimum:-180,maximum:180})}static gammaAngleValidator(e){return Ns.angleRangeValidator(e,{minimum:-90,maximum:90})}toSetting(){return JSON.stringify(this)}}c.register(Es,{capabilities:256,autostart:!0});var Ds=Object.freeze({__proto__:null,EmulationModel:Es,Location:Os,DeviceOrientation:Ns});let Fs;class Bs extends e.ObjectWrapper.ObjectWrapper{#kl;#wl;#Sl;constructor(){super(),this.#kl=Us.NoThrottling,je.instance().observeModels(Es,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return Fs&&!t||(Fs=new Bs),Fs}cpuThrottlingRate(){return this.#kl}setCPUThrottlingRate(e){this.#kl=e;for(const e of je.instance().models(Es))e.setCPUThrottlingRate(this.#kl);this.dispatchEventToListeners("RateChanged",this.#kl)}setHardwareConcurrency(e){this.#wl=e;for(const t of je.instance().models(Es))t.setHardwareConcurrency(e);this.dispatchEventToListeners("HardwareConcurrencyChanged",this.#wl)}hasPrimaryPageTargetSet(){try{return null!==je.instance().primaryPageTarget()}catch{return!1}}async getHardwareConcurrency(){const e=je.instance().primaryPageTarget(),t=this.#Sl;if(!e)return new Promise(t?e=>{this.#Sl=r=>{e(r),t(r)}}:e=>{this.#Sl=e});const r=await e.runtimeAgent().invoke_evaluate({expression:"navigator.hardwareConcurrency",returnByValue:!0,silent:!0,throwOnSideEffect:!0}),n=r.getError();if(n)throw new Error(n);const{result:s,exceptionDetails:i}=r;if(i)throw new Error(i.text);return s.value}modelAdded(e){if(this.#kl!==Us.NoThrottling&&e.setCPUThrottlingRate(this.#kl),void 0!==this.#wl&&e.setHardwareConcurrency(this.#wl),this.#Sl){const e=this.#Sl;this.#Sl=void 0,this.getHardwareConcurrency().then(e)}}modelRemoved(e){}}var Us;!function(e){e[e.NoThrottling=1]="NoThrottling",e[e.MidTierMobile=4]="MidTierMobile",e[e.LowEndMobile=6]="LowEndMobile"}(Us||(Us={}));var Hs=Object.freeze({__proto__:null,CPUThrottlingManager:Bs,throttlingManager:function(){return Bs.instance()},get CPUThrottlingRates(){return Us}});class qs extends c{agent;#Cr;#ir;#Cl;#Rl;suspended=!1;constructor(t){super(t),this.agent=t.domdebuggerAgent(),this.#Cr=t.model(ar),this.#ir=t.model(tn),this.#ir.addEventListener($r.DocumentUpdated,this.documentUpdated,this),this.#ir.addEventListener($r.NodeRemoved,this.nodeRemoved,this),this.#Cl=[],this.#Rl=e.Settings.Settings.instance().createLocalSetting("dom-breakpoints",[]),this.#ir.existingDocument()&&this.documentUpdated()}runtimeModel(){return this.#Cr}async suspendModel(){this.suspended=!0}async resumeModel(){this.suspended=!1}async eventListeners(e){if(console.assert(e.runtimeModel()===this.#Cr),!e.objectId)return[];const t=await this.agent.invoke_getEventListeners({objectId:e.objectId}),r=[];for(const n of t.listeners||[]){const t=this.#Cr.debuggerModel().createRawLocationByScriptId(n.scriptId,n.lineNumber,n.columnNumber);t&&r.push(new js(this,e,n.type,n.useCapture,n.passive,n.once,n.handler?this.#Cr.createRemoteObject(n.handler):null,n.originalHandler?this.#Cr.createRemoteObject(n.originalHandler):null,t,null))}return r}retrieveDOMBreakpoints(){this.#ir.requestDocument()}domBreakpoints(){return this.#Cl.slice()}hasDOMBreakpoint(e,t){return this.#Cl.some((r=>r.node===e&&r.type===t))}setDOMBreakpoint(e,t){for(const r of this.#Cl)if(r.node===e&&r.type===t)return this.toggleDOMBreakpoint(r,!0),r;const r=new zs(this,e,t,!0);return this.#Cl.push(r),this.saveDOMBreakpoints(),this.enableDOMBreakpoint(r),this.dispatchEventToListeners("DOMBreakpointAdded",r),r}removeDOMBreakpoint(e,t){this.removeDOMBreakpoints((r=>r.node===e&&r.type===t))}removeAllDOMBreakpoints(){this.removeDOMBreakpoints((e=>!0))}toggleDOMBreakpoint(e,t){t!==e.enabled&&(e.enabled=t,t?this.enableDOMBreakpoint(e):this.disableDOMBreakpoint(e),this.dispatchEventToListeners("DOMBreakpointToggled",e))}enableDOMBreakpoint(e){e.node.id&&(this.agent.invoke_setDOMBreakpoint({nodeId:e.node.id,type:e.type}),e.node.setMarker(_s,!0))}disableDOMBreakpoint(e){e.node.id&&(this.agent.invoke_removeDOMBreakpoint({nodeId:e.node.id,type:e.type}),e.node.setMarker(_s,!!this.nodeHasBreakpoints(e.node)||null))}nodeHasBreakpoints(e){for(const t of this.#Cl)if(t.node===e&&t.enabled)return!0;return!1}resolveDOMBreakpointData(e){const t=e.type,r=this.#ir.nodeForId(e.nodeId);if(!t||!r)return null;let n=null,s=!1;return"subtree-modified"===t&&(s=e.insertion||!1,n=this.#ir.nodeForId(e.targetNodeId)),{type:t,node:r,targetNode:n,insertion:s}}currentURL(){const e=this.#ir.existingDocument();return e?e.documentURL:t.DevToolsPath.EmptyUrlString}async documentUpdated(){if(this.suspended)return;const e=this.#Cl;this.#Cl=[],this.dispatchEventToListeners("DOMBreakpointsRemoved",e);const r=await this.#ir.requestDocument(),n=r?r.documentURL:t.DevToolsPath.EmptyUrlString;for(const e of this.#Rl.get())e.url===n&&this.#ir.pushNodeByPathToFrontend(e.path).then(s.bind(this,e));function s(e,t){const r=t?this.#ir.nodeForId(t):null;if(!r)return;const n=new zs(this,r,e.type,e.enabled);this.#Cl.push(n),e.enabled&&this.enableDOMBreakpoint(n),this.dispatchEventToListeners("DOMBreakpointAdded",n)}}removeDOMBreakpoints(e){const t=[],r=[];for(const n of this.#Cl)e(n)?(t.push(n),n.enabled&&(n.enabled=!1,this.disableDOMBreakpoint(n))):r.push(n);t.length&&(this.#Cl=r,this.saveDOMBreakpoints(),this.dispatchEventToListeners("DOMBreakpointsRemoved",t))}nodeRemoved(e){if(this.suspended)return;const{node:t}=e.data,r=t.children()||[];this.removeDOMBreakpoints((e=>e.node===t||-1!==r.indexOf(e.node)))}saveDOMBreakpoints(){const e=this.currentURL(),t=this.#Rl.get().filter((t=>t.url!==e));for(const r of this.#Cl)t.push({url:e,path:r.node.path(),type:r.type,enabled:r.enabled});this.#Rl.set(t)}}const _s="breakpoint-marker";class zs{domDebuggerModel;node;type;enabled;constructor(e,t,r,n){this.domDebuggerModel=e,this.node=t,this.type=r,this.enabled=n}}class js{#xl;#Tl;#g;#Ml;#Pl;#Ll;#Al;#El;#tn;#Ol;#Nl;#Dl;constructor(e,r,n,s,i,a,o,l,d,c,h){this.#xl=e,this.#Tl=r,this.#g=n,this.#Ml=s,this.#Pl=i,this.#Ll=a,this.#Al=o,this.#El=l||o,this.#tn=d;const u=d.script();this.#Ol=u?u.contentURL():t.DevToolsPath.EmptyUrlString,this.#Nl=c,this.#Dl=h||"Raw"}domDebuggerModel(){return this.#xl}type(){return this.#g}useCapture(){return this.#Ml}passive(){return this.#Pl}once(){return this.#Ll}handler(){return this.#Al}location(){return this.#tn}sourceURL(){return this.#Ol}originalHandler(){return this.#El}canRemove(){return Boolean(this.#Nl)||"FrameworkUser"!==this.#Dl}remove(){if(!this.canRemove())return Promise.resolve(void 0);if("FrameworkUser"!==this.#Dl){function e(e,t,r){this.removeEventListener(e,t,r),this["on"+e]&&(this["on"+e]=void 0)}return this.#Tl.callFunction(e,[Qe.toCallArgument(this.#g),Qe.toCallArgument(this.#El),Qe.toCallArgument(this.#Ml)]).then((()=>{}))}if(this.#Nl){function t(e,t,r,n){this.call(null,e,t,r,n)}return this.#Nl.callFunction(t,[Qe.toCallArgument(this.#g),Qe.toCallArgument(this.#El),Qe.toCallArgument(this.#Ml),Qe.toCallArgument(this.#Pl)]).then((()=>{}))}return Promise.resolve(void 0)}canTogglePassive(){return"FrameworkUser"!==this.#Dl}togglePassive(){return this.#Tl.callFunction((function(e,t,r,n){this.removeEventListener(e,t,{capture:r}),this.addEventListener(e,t,{capture:r,passive:!n})}),[Qe.toCallArgument(this.#g),Qe.toCallArgument(this.#El),Qe.toCallArgument(this.#Ml),Qe.toCallArgument(this.#Pl)]).then((()=>{}))}origin(){return this.#Dl}markAsFramework(){this.#Dl="Framework"}isScrollBlockingType(){return"touchstart"===this.#g||"touchmove"===this.#g||"mousewheel"===this.#g||"wheel"===this.#g}}class Vs extends $n{#g;constructor(e,t){super(e,t),this.#g=t}type(){return this.#g}}class Ws extends $n{eventTargetNames;constructor(e,t,r){super(r,e),this.eventTargetNames=t}setEnabled(e){if(this.enabled()!==e){super.setEnabled(e);for(const e of je.instance().models(qs))this.updateOnModel(e)}}updateOnModel(e){for(const t of this.eventTargetNames)this.enabled()?e.agent.invoke_setEventListenerBreakpoint({eventName:this.name,targetName:t}):e.agent.invoke_removeEventListenerBreakpoint({eventName:this.name,targetName:t})}static listener="listener:"}let Gs;class Ks{#Fl;#Bl;#Ul;#Hl;constructor(){this.#Fl=e.Settings.Settings.instance().createLocalSetting("xhr-breakpoints",[]),this.#Bl=new Map;for(const e of this.#Fl.get())this.#Bl.set(e.url,e.enabled);this.#Ul=[],this.#Ul.push(new Vs("trusted-type-violation","trustedtype-sink-violation")),this.#Ul.push(new Vs("trusted-type-violation","trustedtype-policy-violation")),this.#Hl=[],this.createEventListenerBreakpoints("media",["play","pause","playing","canplay","canplaythrough","seeking","seeked","timeupdate","ended","ratechange","durationchange","volumechange","loadstart","progress","suspend","abort","error","emptied","stalled","loadedmetadata","loadeddata","waiting"],["audio","video"]),this.createEventListenerBreakpoints("picture-in-picture",["enterpictureinpicture","leavepictureinpicture"],["video"]),this.createEventListenerBreakpoints("picture-in-picture",["resize"],["PictureInPictureWindow"]),this.createEventListenerBreakpoints("picture-in-picture",["enter"],["documentPictureInPicture"]),this.createEventListenerBreakpoints("clipboard",["copy","cut","paste","beforecopy","beforecut","beforepaste"],["*"]),this.createEventListenerBreakpoints("control",["resize","scroll","scrollend","zoom","focus","blur","select","change","submit","reset"],["*"]),this.createEventListenerBreakpoints("device",["deviceorientation","devicemotion"],["*"]),this.createEventListenerBreakpoints("dom-mutation",["DOMActivate","DOMFocusIn","DOMFocusOut","DOMAttrModified","DOMCharacterDataModified","DOMNodeInserted","DOMNodeInsertedIntoDocument","DOMNodeRemoved","DOMNodeRemovedFromDocument","DOMSubtreeModified","DOMContentLoaded"],["*"]),this.createEventListenerBreakpoints("drag-drop",["drag","dragstart","dragend","dragenter","dragover","dragleave","drop"],["*"]),this.createEventListenerBreakpoints("keyboard",["keydown","keyup","keypress","input"],["*"]),this.createEventListenerBreakpoints("load",["load","beforeunload","unload","abort","error","hashchange","popstate","navigate","navigatesuccess","navigateerror","currentchange","navigateto","navigatefrom","finish","dispose"],["*"]),this.createEventListenerBreakpoints("mouse",["auxclick","click","dblclick","mousedown","mouseup","mouseover","mousemove","mouseout","mouseenter","mouseleave","mousewheel","wheel","contextmenu"],["*"]),this.createEventListenerBreakpoints("pointer",["pointerover","pointerout","pointerenter","pointerleave","pointerdown","pointerup","pointermove","pointercancel","gotpointercapture","lostpointercapture","pointerrawupdate"],["*"]),this.createEventListenerBreakpoints("touch",["touchstart","touchmove","touchend","touchcancel"],["*"]),this.createEventListenerBreakpoints("worker",["message","messageerror"],["*"]),this.createEventListenerBreakpoints("xhr",["readystatechange","load","loadstart","loadend","abort","error","progress","timeout"],["xmlhttprequest","xmlhttprequestupload"]),je.instance().observeModels(qs,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return Gs&&!t||(Gs=new Ks),Gs}cspViolationBreakpoints(){return this.#Ul.slice()}createEventListenerBreakpoints(e,t,r){for(const n of t)this.#Hl.push(new Ws(n,r,e))}resolveEventListenerBreakpoint({eventName:e,targetName:t}){const r="listener:";if(!e.startsWith(r))return null;e=e.substring(9),t=(t||"*").toLowerCase();let n=null;for(const r of this.#Hl)e&&r.name===e&&-1!==r.eventTargetNames.indexOf(t)&&(n=r),!n&&e&&r.name===e&&-1!==r.eventTargetNames.indexOf("*")&&(n=r);return n}eventListenerBreakpoints(){return this.#Hl.slice()}updateCSPViolationBreakpoints(){const e=this.#Ul.filter((e=>e.enabled())).map((e=>e.type()));for(const t of je.instance().models(qs))this.updateCSPViolationBreakpointsForModel(t,e)}updateCSPViolationBreakpointsForModel(e,t){e.agent.invoke_setBreakOnCSPViolation({violationTypes:t})}xhrBreakpoints(){return this.#Bl}saveXHRBreakpoints(){const e=[];for(const t of this.#Bl.keys())e.push({url:t,enabled:this.#Bl.get(t)||!1});this.#Fl.set(e)}addXHRBreakpoint(e,t){if(this.#Bl.set(e,t),t)for(const t of je.instance().models(qs))t.agent.invoke_setXHRBreakpoint({url:e});this.saveXHRBreakpoints()}removeXHRBreakpoint(e){const t=this.#Bl.get(e);if(this.#Bl.delete(e),t)for(const t of je.instance().models(qs))t.agent.invoke_removeXHRBreakpoint({url:e});this.saveXHRBreakpoints()}toggleXHRBreakpoint(e,t){this.#Bl.set(e,t);for(const r of je.instance().models(qs))t?r.agent.invoke_setXHRBreakpoint({url:e}):r.agent.invoke_removeXHRBreakpoint({url:e});this.saveXHRBreakpoints()}modelAdded(e){for(const t of this.#Bl.keys())this.#Bl.get(t)&&e.agent.invoke_setXHRBreakpoint({url:t});for(const t of this.#Hl)t.enabled()&&t.updateOnModel(e);const t=this.#Ul.filter((e=>e.enabled())).map((e=>e.type()));this.updateCSPViolationBreakpointsForModel(e,t)}modelRemoved(e){}}c.register(qs,{capabilities:2,autostart:!1});var Qs=Object.freeze({__proto__:null,DOMDebuggerModel:qs,DOMBreakpoint:zs,EventListener:js,CSPViolationBreakpoint:Vs,DOMEventListenerBreakpoint:Ws,DOMDebuggerManager:Ks});class $s extends c{agent;constructor(e){super(e),this.agent=e.eventBreakpointsAgent()}}class Xs extends $n{setEnabled(e){if(this.enabled()!==e){super.setEnabled(e);for(const e of je.instance().models($s))this.updateOnModel(e)}}updateOnModel(e){this.enabled()?e.agent.invoke_setInstrumentationBreakpoint({eventName:this.name}):e.agent.invoke_removeInstrumentationBreakpoint({eventName:this.name})}static instrumentationPrefix="instrumentation:"}let Js;class Ys{#Hl=[];constructor(){this.createInstrumentationBreakpoints("auction-worklet",["beforeBidderWorkletBiddingStart","beforeBidderWorkletReportingStart","beforeSellerWorkletScoringStart","beforeSellerWorkletReportingStart"]),this.createInstrumentationBreakpoints("animation",["requestAnimationFrame","cancelAnimationFrame","requestAnimationFrame.callback"]),this.createInstrumentationBreakpoints("canvas",["canvasContextCreated","webglErrorFired","webglWarningFired"]),this.createInstrumentationBreakpoints("geolocation",["Geolocation.getCurrentPosition","Geolocation.watchPosition"]),this.createInstrumentationBreakpoints("notification",["Notification.requestPermission"]),this.createInstrumentationBreakpoints("parse",["Element.setInnerHTML","Document.write"]),this.createInstrumentationBreakpoints("script",["scriptFirstStatement","scriptBlockedByCSP"]),this.createInstrumentationBreakpoints("shared-storage-worklet",["sharedStorageWorkletScriptFirstStatement"]),this.createInstrumentationBreakpoints("timer",["setTimeout","clearTimeout","setInterval","clearInterval","setTimeout.callback","setInterval.callback"]),this.createInstrumentationBreakpoints("window",["DOMWindow.close"]),this.createInstrumentationBreakpoints("web-audio",["audioContextCreated","audioContextClosed","audioContextResumed","audioContextSuspended"]),je.instance().observeModels($s,this)}static instance(e={forceNew:null}){const{forceNew:t}=e;return Js&&!t||(Js=new Ys),Js}createInstrumentationBreakpoints(e,t){for(const r of t)this.#Hl.push(new Xs(e,r))}eventListenerBreakpoints(){return this.#Hl.slice()}resolveEventListenerBreakpoint({eventName:e}){if(!e.startsWith(Xs.instrumentationPrefix))return null;const t=e.substring(Xs.instrumentationPrefix.length);return this.#Hl.find((e=>e.name===t))||null}modelAdded(e){for(const t of this.#Hl)t.enabled()&&t.updateOnModel(e)}modelRemoved(e){}}c.register($s,{capabilities:524288,autostart:!1});var Zs=Object.freeze({__proto__:null,EventBreakpointsModel:$s,EventBreakpointsManager:Ys}),ei=Object.freeze({__proto__:null});let ti;class ri extends e.ObjectWrapper.ObjectWrapper{#ql;#_l;#De;#zl;constructor(){super(),this.#ql=new Map,this.#_l=new Map,this.#De=new Set,je.instance().observeModels(ar,this),this.#zl=0}static instance({forceNew:e}={forceNew:!1}){return ti&&!e||(ti=new ri),ti}observeIsolates(e){if(this.#De.has(e))throw new Error("Observer can only be registered once");this.#De.size||this.poll(),this.#De.add(e);for(const t of this.#ql.values())e.isolateAdded(t)}unobserveIsolates(e){this.#De.delete(e),this.#De.size||++this.#zl}modelAdded(e){this.modelAddedInternal(e)}async modelAddedInternal(e){this.#_l.set(e,null);const t=await e.isolateId();if(!this.#_l.has(e))return;if(!t)return void this.#_l.delete(e);this.#_l.set(e,t);let r=this.#ql.get(t);if(r||(r=new ii(t),this.#ql.set(t,r)),r.modelsInternal.add(e),1===r.modelsInternal.size)for(const e of this.#De)e.isolateAdded(r);else for(const e of this.#De)e.isolateChanged(r)}modelRemoved(e){const t=this.#_l.get(e);if(this.#_l.delete(e),!t)return;const r=this.#ql.get(t);if(r)if(r.modelsInternal.delete(e),r.modelsInternal.size)for(const e of this.#De)e.isolateChanged(r);else{for(const e of this.#De)e.isolateRemoved(r);this.#ql.delete(t)}}isolateByModel(e){return this.#ql.get(this.#_l.get(e)||"")||null}isolates(){return this.#ql.values()}async poll(){const e=this.#zl;for(;e===this.#zl;)await Promise.all(Array.from(this.isolates(),(e=>e.update()))),await new Promise((e=>window.setTimeout(e,si)))}}const ni=12e4,si=2e3;class ii{#Pe;modelsInternal;#jl;#Vl;constructor(e){this.#Pe=e,this.modelsInternal=new Set,this.#jl=0;const t=ni/si;this.#Vl=new ai(t)}id(){return this.#Pe}models(){return this.modelsInternal}runtimeModel(){return this.modelsInternal.values().next().value||null}heapProfilerModel(){const e=this.runtimeModel();return e&&e.heapProfilerModel()}async update(){const e=this.runtimeModel(),t=e&&await e.heapUsage();t&&(this.#jl=t.usedSize,this.#Vl.add(this.#jl),ri.instance().dispatchEventToListeners("MemoryChanged",this))}samplesCount(){return this.#Vl.count()}usedHeapSize(){return this.#jl}usedHeapSizeGrowRate(){return this.#Vl.fitSlope()}isMainThread(){const e=this.runtimeModel();return!!e&&"main"===e.target().id()}}class ai{#Wl;#Gl;#hn;#Kl;#Ql;#$l;#Xl;#Jl;#Yl;constructor(e){this.#Wl=0|e,this.reset()}reset(){this.#Gl=Date.now(),this.#hn=0,this.#Kl=[],this.#Ql=[],this.#$l=0,this.#Xl=0,this.#Jl=0,this.#Yl=0}count(){return this.#Kl.length}add(e,t){const r="number"==typeof t?t:Date.now()-this.#Gl,n=e;if(this.#Kl.length===this.#Wl){const e=this.#Kl[this.#hn],t=this.#Ql[this.#hn];this.#$l-=e,this.#Xl-=t,this.#Jl-=e*e,this.#Yl-=e*t}this.#$l+=r,this.#Xl+=n,this.#Jl+=r*r,this.#Yl+=r*n,this.#Kl[this.#hn]=r,this.#Ql[this.#hn]=n,this.#hn=(this.#hn+1)%this.#Wl}fitSlope(){const e=this.count();return e<2?0:(this.#Yl-this.#$l*this.#Xl/e)/(this.#Jl-this.#$l*this.#$l/e)}}var oi=Object.freeze({__proto__:null,IsolateManager:ri,MemoryTrendWindowMs:ni,Isolate:ii,MemoryTrend:ai});class li extends c{#Zl=!1;#wr=!1;constructor(e){super(e),this.ensureEnabled()}async ensureEnabled(){if(this.#wr)return;this.#wr=!0,this.target().registerAuditsDispatcher(this);const e=this.target().auditsAgent();await e.invoke_enable()}issueAdded(e){this.dispatchEventToListeners("IssueAdded",{issuesModel:this,inspectorIssue:e.issue})}dispose(){super.dispose(),this.#Zl=!0}getTargetIfNotDisposed(){return this.#Zl?null:this.target()}}c.register(li,{capabilities:32768,autostart:!0});var di=Object.freeze({__proto__:null,IssuesModel:li});var ci=Object.freeze({__proto__:null,StickyPositionConstraint:class{#ed;#td;#rd;#nd;constructor(e,t){this.#ed=t.stickyBoxRect,this.#td=t.containingBlockRect,this.#rd=null,e&&t.nearestLayerShiftingStickyBox&&(this.#rd=e.layerById(t.nearestLayerShiftingStickyBox)),this.#nd=null,e&&t.nearestLayerShiftingContainingBlock&&(this.#nd=e.layerById(t.nearestLayerShiftingContainingBlock))}stickyBoxRect(){return this.#ed}containingBlockRect(){return this.#td}nearestLayerShiftingStickyBox(){return this.#rd}nearestLayerShiftingContainingBlock(){return this.#nd}},LayerTreeBase:class{#e;#ir;layersById;#sd;#id;#ad;#od;constructor(e){this.#e=e,this.#ir=e?e.model(tn):null,this.layersById=new Map,this.#sd=null,this.#id=null,this.#ad=new Map}target(){return this.#e}root(){return this.#sd}setRoot(e){this.#sd=e}contentRoot(){return this.#id}setContentRoot(e){this.#id=e}forEachLayer(e,t){return!(!t&&!(t=this.root()))&&(e(t)||t.children().some(this.forEachLayer.bind(this,e)))}layerById(e){return this.layersById.get(e)||null}async resolveBackendNodeIds(e){if(!e.size||!this.#ir)return;const t=await this.#ir.pushNodesByBackendIdsToFrontend(e);if(t)for(const e of t.keys())this.#ad.set(e,t.get(e)||null)}backendNodeIdToNode(){return this.#ad}setViewportSize(e){this.#od=e}viewportSize(){return this.#od}nodeForId(e){return this.#ir?this.#ir.nodeForId(e):null}}});class hi{id;url;startTime;loadTime;contentLoadTime;mainRequest;constructor(e){this.id=++hi.lastIdentifier,this.url=e.url(),this.startTime=e.startTime,this.mainRequest=e}static forRequest(e){return ui.get(e)||null}bindRequest(e){ui.set(e,this)}static lastIdentifier=0}const ui=new WeakMap;var gi=Object.freeze({__proto__:null,PageLoad:hi});class pi extends c{layerTreeAgent;constructor(e){super(e),this.layerTreeAgent=e.layerTreeAgent()}async loadSnapshotFromFragments(e){const{snapshotId:t}=await this.layerTreeAgent.invoke_loadSnapshot({tiles:e});return t?new mi(this,t):null}loadSnapshot(e){const t={x:0,y:0,picture:e};return this.loadSnapshotFromFragments([t])}async makeSnapshot(e){const{snapshotId:t}=await this.layerTreeAgent.invoke_makeSnapshot({layerId:e});return t?new mi(this,t):null}}class mi{#ld;#Si;#dd;constructor(e,t){this.#ld=e,this.#Si=t,this.#dd=1}release(){console.assert(this.#dd>0,"release is already called on the object"),--this.#dd||this.#ld.layerTreeAgent.invoke_releaseSnapshot({snapshotId:this.#Si})}addReference(){++this.#dd,console.assert(this.#dd>0,"Referencing a dead object")}async replay(e,t,r){return(await this.#ld.layerTreeAgent.invoke_replaySnapshot({snapshotId:this.#Si,fromStep:t,toStep:r,scale:e||1})).dataURL}async profile(e){return(await this.#ld.layerTreeAgent.invoke_profileSnapshot({snapshotId:this.#Si,minRepeatCount:5,minDuration:1,clipRect:e||void 0})).timings}async commandLog(){const e=await this.#ld.layerTreeAgent.invoke_snapshotCommandLog({snapshotId:this.#Si});return e.commandLog?e.commandLog.map(((e,t)=>new fi(e,t))):null}}class fi{method;params;commandIndex;constructor(e,t){this.method=e.method,this.params=e.params,this.commandIndex=t}}c.register(pi,{capabilities:2,autostart:!1});var bi=Object.freeze({__proto__:null,PaintProfilerModel:pi,PaintProfilerSnapshot:mi,PaintProfilerLogItem:fi});class yi extends c{#Yn;#cd;#hd;constructor(e){super(e),this.#Yn=e.performanceAgent(),this.#cd=new Map([["TaskDuration","CumulativeTime"],["ScriptDuration","CumulativeTime"],["LayoutDuration","CumulativeTime"],["RecalcStyleDuration","CumulativeTime"],["LayoutCount","CumulativeCount"],["RecalcStyleCount","CumulativeCount"]]),this.#hd=new Map}enable(){return this.#Yn.invoke_enable({})}disable(){return this.#Yn.invoke_disable()}async requestMetrics(){const e=await this.#Yn.invoke_getMetrics()||[],r=new Map,n=performance.now();for(const s of e.metrics){let e,i=this.#hd.get(s.name);switch(i||(i={lastValue:void 0,lastTimestamp:void 0},this.#hd.set(s.name,i)),this.#cd.get(s.name)){case"CumulativeTime":e=i.lastTimestamp&&i.lastValue?t.NumberUtilities.clamp(1e3*(s.value-i.lastValue)/(n-i.lastTimestamp),0,1):0,i.lastValue=s.value,i.lastTimestamp=n;break;case"CumulativeCount":e=i.lastTimestamp&&i.lastValue?Math.max(0,1e3*(s.value-i.lastValue)/(n-i.lastTimestamp)):0,i.lastValue=s.value,i.lastTimestamp=n;break;default:e=s.value}r.set(s.name,e)}return{metrics:r,timestamp:n}}}c.register(yi,{capabilities:2,autostart:!1});var vi=Object.freeze({__proto__:null,PerformanceMetricsModel:yi});class Ii extends c{agent;loaderIds=[];targetJustAttached=!0;lastPrimaryPageModel=null;documents=new Map;constructor(e){super(e),e.registerPreloadDispatcher(new ki(this)),this.agent=e.preloadAgent(),this.agent.invoke_enable();const t=e.targetInfo();void 0!==t&&"prerender"===t.subtype&&(this.lastPrimaryPageModel=je.instance().primaryPageTarget()?.model(Ii)||null),je.instance().addModelListener(mn,gn.PrimaryPageChanged,this.onPrimaryPageChanged,this)}dispose(){super.dispose(),je.instance().removeModelListener(mn,gn.PrimaryPageChanged,this.onPrimaryPageChanged,this),this.agent.invoke_disable()}ensureDocumentPreloadingData(e){void 0===this.documents.get(e)&&this.documents.set(e,new wi)}currentLoaderId(){if(this.targetJustAttached)return null;if(0===this.loaderIds.length)throw new Error("unreachable");return this.loaderIds[this.loaderIds.length-1]}currentDocument(){const e=this.currentLoaderId();return null===e?null:this.documents.get(e)||null}getRuleSetById(e){return this.currentDocument()?.ruleSets.getById(e)||null}getAllRuleSets(){return this.currentDocument()?.ruleSets.getAll()||[]}getPreloadCountsByRuleSetId(){const e=new Map;for(const{value:t}of this.getPreloadingAttempts(null))for(const n of[null,...t.ruleSetIds]){void 0===e.get(n)&&e.set(n,new Map);const s=e.get(n);r(s);const i=s.get(t.status)||0;s.set(t.status,i+1)}return e}getPreloadingAttemptById(e){const t=this.currentDocument();return null===t?null:t.preloadingAttempts.getById(e,t.sources)||null}getPreloadingAttempts(e){const t=this.currentDocument();return null===t?[]:t.preloadingAttempts.getAll(e,t.sources)}getPreloadingAttemptsOfPreviousPage(){if(this.loaderIds.length<=1)return[];const e=this.documents.get(this.loaderIds[this.loaderIds.length-2]);return void 0===e?[]:e.preloadingAttempts.getAll(null,e.sources)}onPrimaryPageChanged(e){const{frame:t,type:r}=e.data;if(null===this.lastPrimaryPageModel&&"Activation"===r)return;if(null!==this.lastPrimaryPageModel&&"Activation"!==r)return;if(null!==this.lastPrimaryPageModel&&"Activation"===r){this.loaderIds=this.lastPrimaryPageModel.loaderIds;for(const[e,t]of this.lastPrimaryPageModel.documents.entries())this.ensureDocumentPreloadingData(e),this.documents.get(e)?.mergePrevious(t)}this.lastPrimaryPageModel=null;const n=t.loaderId;this.loaderIds.push(n),this.loaderIds=this.loaderIds.slice(-2),this.ensureDocumentPreloadingData(n);for(const e of this.documents.keys())this.loaderIds.includes(e)||this.documents.delete(e);this.dispatchEventToListeners("ModelUpdated")}onRuleSetUpdated(e){const t=e.ruleSet,r=t.loaderId;null===this.currentLoaderId()&&(this.loaderIds=[r],this.targetJustAttached=!1),this.ensureDocumentPreloadingData(r),this.documents.get(r)?.ruleSets.upsert(t),this.dispatchEventToListeners("ModelUpdated")}onRuleSetRemoved(e){const t=e.id;for(const e of this.documents.values())e.ruleSets.delete(t);this.dispatchEventToListeners("ModelUpdated")}onPreloadingAttemptSourcesUpdated(e){const t=e.loaderId;this.ensureDocumentPreloadingData(t);const r=this.documents.get(t);void 0!==r&&(r.sources.update(e.preloadingAttemptSources),r.preloadingAttempts.maybeRegisterNotTriggered(r.sources),this.dispatchEventToListeners("ModelUpdated"))}onPrefetchStatusUpdated(e){const t=e.key.loaderId;this.ensureDocumentPreloadingData(t);const r={action:"Prefetch",key:e.key,status:Ci(e.status),prefetchStatus:e.prefetchStatus||null,requestId:e.requestId};this.documents.get(t)?.preloadingAttempts.upsert(r),this.dispatchEventToListeners("ModelUpdated")}onPrerenderStatusUpdated(e){const t=e.key.loaderId;this.ensureDocumentPreloadingData(t);const r={action:"Prerender",key:e.key,status:Ci(e.status),prerenderStatus:e.prerenderStatus||null,disallowedMojoInterface:e.disallowedMojoInterface||null,mismatchedHeaders:e.mismatchedHeaders||null};this.documents.get(t)?.preloadingAttempts.upsert(r),this.dispatchEventToListeners("ModelUpdated")}onPreloadEnabledStateUpdated(e){this.dispatchEventToListeners("WarningsUpdated",e)}}c.register(Ii,{capabilities:2,autostart:!1});class ki{model;constructor(e){this.model=e}ruleSetUpdated(e){this.model.onRuleSetUpdated(e)}ruleSetRemoved(e){this.model.onRuleSetRemoved(e)}preloadingAttemptSourcesUpdated(e){this.model.onPreloadingAttemptSourcesUpdated(e)}prefetchStatusUpdated(e){this.model.onPrefetchStatusUpdated(e)}prerenderStatusUpdated(e){this.model.onPrerenderStatusUpdated(e)}preloadEnabledStateUpdated(e){this.model.onPreloadEnabledStateUpdated(e)}}class wi{ruleSets=new Si;preloadingAttempts=new xi;sources=new Ti;mergePrevious(e){if(!this.ruleSets.isEmpty()||!this.sources.isEmpty())throw new Error("unreachable");this.ruleSets=e.ruleSets,this.preloadingAttempts.mergePrevious(e.preloadingAttempts),this.sources=e.sources}}class Si{map=new Map;isEmpty(){return 0===this.map.size}getById(e){return this.map.get(e)||null}getAll(){return Array.from(this.map.entries()).map((([e,t])=>({id:e,value:t})))}upsert(e){this.map.set(e.id,e)}delete(e){this.map.delete(e)}}function Ci(e){switch(e){case"Pending":return"Pending";case"Running":return"Running";case"Ready":return"Ready";case"Success":return"Success";case"Failure":return"Failure";case"NotSupported":return"NotSupported"}throw new Error("unreachable")}function Ri(e){let t,r;switch(e.action){case"Prefetch":t="Prefetch";break;case"Prerender":t="Prerender"}switch(e.targetHint){case void 0:r="undefined";break;case"Blank":r="Blank";break;case"Self":r="Self"}return`${e.loaderId}:${t}:${e.url}:${r}`}class xi{map=new Map;enrich(e,t){let r=[],n=[];return null!==t&&(r=t.ruleSetIds,n=t.nodeIds),{...e,ruleSetIds:r,nodeIds:n}}getById(e,t){const r=this.map.get(e)||null;return null===r?null:this.enrich(r,t.getById(e))}getAll(e,t){return[...this.map.entries()].map((([e,r])=>({id:e,value:this.enrich(r,t.getById(e))}))).filter((({value:t})=>!e||t.ruleSetIds.includes(e)))}upsert(e){const t=Ri(e.key);this.map.set(t,e)}maybeRegisterNotTriggered(e){for(const[t,{key:r}]of e.entries()){if(void 0!==this.map.get(t))continue;let e;switch(r.action){case"Prefetch":e={action:"Prefetch",key:r,status:"NotTriggered",prefetchStatus:null,requestId:""};break;case"Prerender":e={action:"Prerender",key:r,status:"NotTriggered",prerenderStatus:null,disallowedMojoInterface:null,mismatchedHeaders:null}}this.map.set(t,e)}}mergePrevious(e){for(const[t,r]of this.map.entries())e.map.set(t,r);this.map=e.map}}class Ti{map=new Map;entries(){return this.map.entries()}isEmpty(){return 0===this.map.size}getById(e){return this.map.get(e)||null}update(e){this.map=new Map(e.map((e=>[Ri(e.key),e])))}}var Mi=Object.freeze({__proto__:null,PreloadingModel:Ii});class Pi extends c{#wr;#Yn;metadataCached=null;constructor(e){super(e),this.#wr=!1,this.#Yn=e.reactNativeApplicationAgent(),e.registerReactNativeApplicationDispatcher(this)}ensureEnabled(){this.#wr||(this.#Yn.invoke_enable(),this.#wr=!0)}metadataUpdated(e){this.metadataCached=e,this.dispatchEventToListeners("MetadataUpdated",e)}}c.register(Pi,{capabilities:0,autostart:!0});var Li=Object.freeze({__proto__:null,ReactNativeApplicationModel:Pi});class Ai extends c{#Yn;#ud;#gd;constructor(e){super(e),this.#Yn=e.pageAgent(),this.#ud=null,this.#gd=null,e.registerPageDispatcher(this)}startScreencast(e,t,r,n,s,i,a){this.#ud=i,this.#gd=a,this.#Yn.invoke_startScreencast({format:e,quality:t,maxWidth:r,maxHeight:n,everyNthFrame:s})}stopScreencast(){this.#ud=null,this.#gd=null,this.#Yn.invoke_stopScreencast()}async captureScreenshot(e,t,r,n){const s={format:e,quality:t,fromSurface:!0};switch(r){case"fromClip":s.captureBeyondViewport=!0,s.clip=n;break;case"fullpage":s.captureBeyondViewport=!0;break;case"fromViewport":s.captureBeyondViewport=!1;break;default:throw new Error("Unexpected or unspecified screnshotMode")}await Wr.muteHighlight();const i=await this.#Yn.invoke_captureScreenshot(s);return await Wr.unmuteHighlight(),i.data}async fetchLayoutMetrics(){const e=await this.#Yn.invoke_getLayoutMetrics();return e.getError()?null:{viewportX:e.cssVisualViewport.pageX,viewportY:e.cssVisualViewport.pageY,viewportScale:e.cssVisualViewport.scale,contentWidth:e.cssContentSize.width,contentHeight:e.cssContentSize.height}}screencastFrame({data:e,metadata:t,sessionId:r}){this.#Yn.invoke_screencastFrameAck({sessionId:r}),this.#ud&&this.#ud.call(null,e,t)}screencastVisibilityChanged({visible:e}){this.#gd&&this.#gd.call(null,e)}backForwardCacheNotUsed(e){}domContentEventFired(e){}loadEventFired(e){}lifecycleEvent(e){}navigatedWithinDocument(e){}frameAttached(e){}frameNavigated(e){}documentOpened(e){}frameDetached(e){}frameStartedLoading(e){}frameStoppedLoading(e){}frameRequestedNavigation(e){}frameScheduledNavigation(e){}frameClearedScheduledNavigation(e){}frameResized(){}javascriptDialogOpening(e){}javascriptDialogClosed(e){}interstitialShown(){}interstitialHidden(){}windowOpen(e){}fileChooserOpened(e){}compilationCacheProduced(e){}downloadWillBegin(e){}downloadProgress(){}prefetchStatusUpdated(e){}prerenderStatusUpdated(e){}}c.register(Ai,{capabilities:64,autostart:!1});var Ei=Object.freeze({__proto__:null,ScreenCaptureModel:Ai});class Oi extends c{enabled=!1;storageAgent;storageKeyManager;bucketsById=new Map;trackedStorageKeys=new Set;constructor(e){super(e),e.registerStorageDispatcher(this),this.storageAgent=e.storageAgent(),this.storageKeyManager=e.model(un)}getBuckets(){return new Set(this.bucketsById.values())}getBucketsForStorageKey(e){const t=[...this.bucketsById.values()];return new Set(t.filter((({bucket:t})=>t.storageKey===e)))}getDefaultBucketForStorageKey(e){return[...this.bucketsById.values()].find((({bucket:t})=>t.storageKey===e&&void 0===t.name))??null}getBucketById(e){return this.bucketsById.get(e)??null}getBucketByName(e,t){if(!t)return this.getDefaultBucketForStorageKey(e);return[...this.bucketsById.values()].find((({bucket:r})=>r.storageKey===e&&r.name===t))??null}deleteBucket(e){this.storageAgent.invoke_deleteStorageBucket({bucket:e})}enable(){if(!this.enabled){if(this.storageKeyManager){this.storageKeyManager.addEventListener("StorageKeyAdded",this.storageKeyAdded,this),this.storageKeyManager.addEventListener("StorageKeyRemoved",this.storageKeyRemoved,this);for(const e of this.storageKeyManager.storageKeys())this.addStorageKey(e)}this.enabled=!0}}storageKeyAdded(e){this.addStorageKey(e.data)}storageKeyRemoved(e){this.removeStorageKey(e.data)}addStorageKey(e){if(this.trackedStorageKeys.has(e))throw new Error("Can't call addStorageKey for a storage key if it has already been added.");this.trackedStorageKeys.add(e),this.storageAgent.invoke_setStorageBucketTracking({storageKey:e,enable:!0})}removeStorageKey(e){if(!this.trackedStorageKeys.has(e))throw new Error("Can't call removeStorageKey for a storage key if it hasn't already been added.");const t=this.getBucketsForStorageKey(e);for(const e of t)this.bucketRemoved(e);this.trackedStorageKeys.delete(e),this.storageAgent.invoke_setStorageBucketTracking({storageKey:e,enable:!1})}bucketAdded(e){this.bucketsById.set(e.id,e),this.dispatchEventToListeners("BucketAdded",{model:this,bucketInfo:e})}bucketRemoved(e){this.bucketsById.delete(e.id),this.dispatchEventToListeners("BucketRemoved",{model:this,bucketInfo:e})}bucketChanged(e){this.dispatchEventToListeners("BucketChanged",{model:this,bucketInfo:e})}bucketInfosAreEqual(e,t){return e.bucket.storageKey===t.bucket.storageKey&&e.id===t.id&&e.bucket.name===t.bucket.name&&e.expiration===t.expiration&&e.quota===t.quota&&e.persistent===t.persistent&&e.durability===t.durability}storageBucketCreatedOrUpdated({bucketInfo:e}){const t=this.getBucketById(e.id);t?this.bucketInfosAreEqual(t,e)||this.bucketChanged(e):this.bucketAdded(e)}storageBucketDeleted({bucketId:e}){const t=this.getBucketById(e);if(!t)throw new Error(`Received an event that Storage Bucket '${e}' was deleted, but it wasn't in the StorageBucketsModel.`);this.bucketRemoved(t)}attributionReportingTriggerRegistered(e){}interestGroupAccessed(e){}interestGroupAuctionEventOccurred(e){}interestGroupAuctionNetworkRequestCreated(e){}indexedDBListUpdated(e){}indexedDBContentUpdated(e){}cacheStorageListUpdated(e){}cacheStorageContentUpdated(e){}sharedStorageAccessed(e){}attributionReportingSourceRegistered(e){}}c.register(Oi,{capabilities:8192,autostart:!1});var Ni=Object.freeze({__proto__:null,StorageBucketsModel:Oi});const Di={serviceworkercacheagentError:"`ServiceWorkerCacheAgent` error deleting cache entry {PH1} in cache: {PH2}"},Fi=i.i18n.registerUIStrings("core/sdk/ServiceWorkerCacheModel.ts",Di),Bi=i.i18n.getLocalizedString.bind(void 0,Fi);class Ui extends c{cacheAgent;#pd;#md;#fd=new Map;#bd=new Set;#yd=new Set;#vd=new e.Throttler.Throttler(2e3);#wr=!1;#Id=!1;constructor(e){super(e),e.registerStorageDispatcher(this),this.cacheAgent=e.cacheStorageAgent(),this.#pd=e.storageAgent(),this.#md=e.model(Oi)}enable(){if(!this.#wr){this.#md.addEventListener("BucketAdded",this.storageBucketAdded,this),this.#md.addEventListener("BucketRemoved",this.storageBucketRemoved,this);for(const e of this.#md.getBuckets())this.addStorageBucket(e.bucket);this.#wr=!0}}clearForStorageKey(e){for(const[t,r]of this.#fd.entries())r.storageKey===e&&(this.#fd.delete(t),this.cacheRemoved(r));for(const t of this.#md.getBucketsForStorageKey(e))this.loadCacheNames(t.bucket)}refreshCacheNames(){for(const e of this.#fd.values())this.cacheRemoved(e);this.#fd.clear();const e=this.#md.getBuckets();for(const t of e)this.loadCacheNames(t.bucket)}async deleteCache(e){const t=await this.cacheAgent.invoke_deleteCache({cacheId:e.cacheId});t.getError()?console.error(`ServiceWorkerCacheAgent error deleting cache ${e.toString()}: ${t.getError()}`):(this.#fd.delete(e.cacheId),this.cacheRemoved(e))}async deleteCacheEntry(t,r){const n=await this.cacheAgent.invoke_deleteEntry({cacheId:t.cacheId,request:r});n.getError()&&e.Console.Console.instance().error(Bi(Di.serviceworkercacheagentError,{PH1:t.toString(),PH2:String(n.getError())}))}loadCacheData(e,t,r,n,s){this.requestEntries(e,t,r,n,s)}loadAllCacheData(e,t,r){this.requestAllEntries(e,t,r)}caches(){const e=new Array;for(const t of this.#fd.values())e.push(t);return e}dispose(){for(const e of this.#fd.values())this.cacheRemoved(e);this.#fd.clear(),this.#wr&&(this.#md.removeEventListener("BucketAdded",this.storageBucketAdded,this),this.#md.removeEventListener("BucketRemoved",this.storageBucketRemoved,this))}addStorageBucket(e){this.loadCacheNames(e),this.#bd.has(e.storageKey)||(this.#bd.add(e.storageKey),this.#pd.invoke_trackCacheStorageForStorageKey({storageKey:e.storageKey}))}removeStorageBucket(e){let t=0;for(const[r,n]of this.#fd.entries())e.storageKey===n.storageKey&&t++,n.inBucket(e)&&(t--,this.#fd.delete(r),this.cacheRemoved(n));0===t&&(this.#bd.delete(e.storageKey),this.#pd.invoke_untrackCacheStorageForStorageKey({storageKey:e.storageKey}))}async loadCacheNames(e){const t=await this.cacheAgent.invoke_requestCacheNames({storageBucket:e});t.getError()||this.updateCacheNames(e,t.caches)}updateCacheNames(e,t){const r=new Set,n=new Map,s=new Map;for(const e of t){const t=e.storageBucket??this.#md.getDefaultBucketForStorageKey(e.storageKey)?.bucket;if(!t)continue;const s=new Hi(this,t,e.cacheName,e.cacheId);r.add(s.cacheId),this.#fd.has(s.cacheId)||(n.set(s.cacheId,s),this.#fd.set(s.cacheId,s))}this.#fd.forEach((function(t){t.inBucket(e)&&!r.has(t.cacheId)&&(s.set(t.cacheId,t),this.#fd.delete(t.cacheId))}),this),n.forEach(this.cacheAdded,this),s.forEach(this.cacheRemoved,this)}storageBucketAdded({data:{bucketInfo:{bucket:e}}}){this.addStorageBucket(e)}storageBucketRemoved({data:{bucketInfo:{bucket:e}}}){this.removeStorageBucket(e)}cacheAdded(e){this.dispatchEventToListeners("CacheAdded",{model:this,cache:e})}cacheRemoved(e){this.dispatchEventToListeners("CacheRemoved",{model:this,cache:e})}async requestEntries(e,t,r,n,s){const i=await this.cacheAgent.invoke_requestEntries({cacheId:e.cacheId,skipCount:t,pageSize:r,pathFilter:n});i.getError()?console.error("ServiceWorkerCacheAgent error while requesting entries: ",i.getError()):s(i.cacheDataEntries,i.returnCount)}async requestAllEntries(e,t,r){const n=await this.cacheAgent.invoke_requestEntries({cacheId:e.cacheId,pathFilter:t});n.getError()?console.error("ServiceWorkerCacheAgent error while requesting entries: ",n.getError()):r(n.cacheDataEntries,n.returnCount)}cacheStorageListUpdated({bucketId:e}){const t=this.#md.getBucketById(e)?.bucket;t&&(this.#yd.add(t),this.#vd.schedule((()=>{const e=Array.from(this.#yd,(e=>this.loadCacheNames(e)));return this.#yd.clear(),Promise.all(e)}),this.#Id))}cacheStorageContentUpdated({bucketId:e,cacheName:t}){const r=this.#md.getBucketById(e)?.bucket;r&&this.dispatchEventToListeners("CacheStorageContentUpdated",{storageBucket:r,cacheName:t})}attributionReportingTriggerRegistered(e){}indexedDBListUpdated(e){}indexedDBContentUpdated(e){}interestGroupAuctionEventOccurred(e){}interestGroupAccessed(e){}interestGroupAuctionNetworkRequestCreated(e){}sharedStorageAccessed(e){}storageBucketCreatedOrUpdated(e){}storageBucketDeleted(e){}setThrottlerSchedulesAsSoonAsPossibleForTest(){this.#Id=!0}attributionReportingSourceRegistered(e){}}class Hi{#un;storageKey;storageBucket;cacheName;cacheId;constructor(e,t,r,n){this.#un=e,this.storageBucket=t,this.storageKey=t.storageKey,this.cacheName=r,this.cacheId=n}inBucket(e){return this.storageKey===e.storageKey&&this.storageBucket.name===e.name}equals(e){return this.cacheId===e.cacheId}toString(){return this.storageKey+this.cacheName}async requestCachedResponse(e,t){const r=await this.#un.cacheAgent.invoke_requestCachedResponse({cacheId:this.cacheId,requestURL:e,requestHeaders:t});return r.getError()?null:r.response}}c.register(Ui,{capabilities:8192,autostart:!1});var qi=Object.freeze({__proto__:null,ServiceWorkerCacheModel:Ui,Cache:Hi});const _i={running:"running",starting:"starting",stopped:"stopped",stopping:"stopping",activated:"activated",activating:"activating",installed:"installed",installing:"installing",new:"new",redundant:"redundant",sSS:"{PH1} #{PH2} ({PH3})"},zi=i.i18n.registerUIStrings("core/sdk/ServiceWorkerManager.ts",_i),ji=i.i18n.getLocalizedString.bind(void 0,zi),Vi=i.i18n.getLazilyComputedLocalizedString.bind(void 0,zi);class Wi extends c{#Yn;#kd;#wr;#wd;serviceWorkerNetworkRequestsPanelStatus;constructor(t){super(t),t.registerServiceWorkerDispatcher(new Gi(this)),this.#Yn=t.serviceWorkerAgent(),this.#kd=new Map,this.#wr=!1,this.enable(),this.#wd=e.Settings.Settings.instance().createSetting("service-worker-update-on-reload",!1),this.#wd.get()&&this.forceUpdateSettingChanged(),this.#wd.addChangeListener(this.forceUpdateSettingChanged,this),new Ji(t,this),this.serviceWorkerNetworkRequestsPanelStatus={isOpen:!1,openedAt:0}}async enable(){this.#wr||(this.#wr=!0,await this.#Yn.invoke_enable())}async disable(){this.#wr&&(this.#wr=!1,this.#kd.clear(),await this.#Yn.invoke_enable())}registrations(){return this.#kd}hasRegistrationForURLs(e){for(const t of this.#kd.values())if(e.filter((e=>e&&e.startsWith(t.scopeURL))).length===e.length)return!0;return!1}findVersion(e){for(const t of this.registrations().values()){const r=t.versions.get(e);if(r)return r}return null}deleteRegistration(e){const t=this.#kd.get(e);if(t){if(t.isRedundant())return this.#kd.delete(e),void this.dispatchEventToListeners("RegistrationDeleted",t);t.deleting=!0;for(const e of t.versions.values())this.stopWorker(e.id);this.unregister(t.scopeURL)}}async updateRegistration(e){const t=this.#kd.get(e);t&&await this.#Yn.invoke_updateRegistration({scopeURL:t.scopeURL})}async deliverPushMessage(t,r){const n=this.#kd.get(t);if(!n)return;const s=e.ParsedURL.ParsedURL.extractOrigin(n.scopeURL);await this.#Yn.invoke_deliverPushMessage({origin:s,registrationId:t,data:r})}async dispatchSyncEvent(t,r,n){const s=this.#kd.get(t);if(!s)return;const i=e.ParsedURL.ParsedURL.extractOrigin(s.scopeURL);await this.#Yn.invoke_dispatchSyncEvent({origin:i,registrationId:t,tag:r,lastChance:n})}async dispatchPeriodicSyncEvent(t,r){const n=this.#kd.get(t);if(!n)return;const s=e.ParsedURL.ParsedURL.extractOrigin(n.scopeURL);await this.#Yn.invoke_dispatchPeriodicSyncEvent({origin:s,registrationId:t,tag:r})}async unregister(e){await this.#Yn.invoke_unregister({scopeURL:e})}async startWorker(e){await this.#Yn.invoke_startWorker({scopeURL:e})}async skipWaiting(e){await this.#Yn.invoke_skipWaiting({scopeURL:e})}async stopWorker(e){await this.#Yn.invoke_stopWorker({versionId:e})}async inspectWorker(e){await this.#Yn.invoke_inspectWorker({versionId:e})}workerRegistrationUpdated(e){for(const t of e){let e=this.#kd.get(t.registrationId);e?(e.update(t),e.shouldBeRemoved()?(this.#kd.delete(e.id),this.dispatchEventToListeners("RegistrationDeleted",e)):this.dispatchEventToListeners("RegistrationUpdated",e)):(e=new Xi(t),this.#kd.set(t.registrationId,e),this.dispatchEventToListeners("RegistrationUpdated",e))}}workerVersionUpdated(e){const t=new Set;for(const r of e){const e=this.#kd.get(r.registrationId);e&&(e.updateVersion(r),t.add(e))}for(const e of t)e.shouldBeRemoved()?(this.#kd.delete(e.id),this.dispatchEventToListeners("RegistrationDeleted",e)):this.dispatchEventToListeners("RegistrationUpdated",e)}workerErrorReported(e){const t=this.#kd.get(e.registrationId);t&&(t.errors.push(e),this.dispatchEventToListeners("RegistrationErrorAdded",{registration:t,error:e}))}forceUpdateOnReloadSetting(){return this.#wd}forceUpdateSettingChanged(){const e=this.#wd.get();this.#Yn.invoke_setForceUpdateOnPageLoad({forceUpdateOnPageLoad:e})}}class Gi{#ht;constructor(e){this.#ht=e}workerRegistrationUpdated({registrations:e}){this.#ht.workerRegistrationUpdated(e)}workerVersionUpdated({versions:e}){this.#ht.workerVersionUpdated(e)}workerErrorReported({errorMessage:e}){this.#ht.workerErrorReported(e)}}class Ki{runningStatus;status;lastUpdatedTimestamp;previousState;constructor(e,t,r,n){this.runningStatus=e,this.status=t,this.lastUpdatedTimestamp=n,this.previousState=r}}class Qi{condition;source;id;constructor(e,t,r){this.condition=e,this.source=t,this.id=r}}class $i{id;scriptURL;parsedURL;securityOrigin;scriptLastModified;scriptResponseTime;controlledClients;targetId;routerRules;currentState;registration;constructor(e,t){this.registration=e,this.update(t)}update(t){this.id=t.versionId,this.scriptURL=t.scriptURL;const r=new e.ParsedURL.ParsedURL(t.scriptURL);this.securityOrigin=r.securityOrigin(),this.currentState=new Ki(t.runningStatus,t.status,this.currentState,Date.now()),this.scriptLastModified=t.scriptLastModified,this.scriptResponseTime=t.scriptResponseTime,t.controlledClients?this.controlledClients=t.controlledClients.slice():this.controlledClients=[],this.targetId=t.targetId||null,this.routerRules=null,t.routerRules&&(this.routerRules=this.parseJSONRules(t.routerRules))}isStartable(){return!this.registration.isDeleted&&this.isActivated()&&this.isStopped()}isStoppedAndRedundant(){return"stopped"===this.runningStatus&&"redundant"===this.status}isStopped(){return"stopped"===this.runningStatus}isStarting(){return"starting"===this.runningStatus}isRunning(){return"running"===this.runningStatus}isStopping(){return"stopping"===this.runningStatus}isNew(){return"new"===this.status}isInstalling(){return"installing"===this.status}isInstalled(){return"installed"===this.status}isActivating(){return"activating"===this.status}isActivated(){return"activated"===this.status}isRedundant(){return"redundant"===this.status}get status(){return this.currentState.status}get runningStatus(){return this.currentState.runningStatus}mode(){return this.isNew()||this.isInstalling()?"installing":this.isInstalled()?"waiting":this.isActivating()||this.isActivated()?"active":"redundant"}parseJSONRules(e){try{const t=JSON.parse(e);if(!Array.isArray(t))return console.error("Parse error: `routerRules` in ServiceWorkerVersion should be an array"),null;const r=[];for(const e of t){const{condition:t,source:n,id:s}=e;if(void 0===t||void 0===n||void 0===s)return console.error("Parse error: Missing some fields of `routerRules` in ServiceWorkerVersion"),null;r.push(new Qi(JSON.stringify(t),JSON.stringify(n),s))}return r}catch(e){return console.error("Parse error: Invalid `routerRules` in ServiceWorkerVersion"),null}}}!function(e){e.RunningStatus={running:Vi(_i.running),starting:Vi(_i.starting),stopped:Vi(_i.stopped),stopping:Vi(_i.stopping)},e.Status={activated:Vi(_i.activated),activating:Vi(_i.activating),installed:Vi(_i.installed),installing:Vi(_i.installing),new:Vi(_i.new),redundant:Vi(_i.redundant)}}($i||($i={}));class Xi{#Sd;id;scopeURL;securityOrigin;isDeleted;versions;deleting;errors;constructor(e){this.update(e),this.versions=new Map,this.deleting=!1,this.errors=[]}update(t){this.#Sd=Symbol("fingerprint"),this.id=t.registrationId,this.scopeURL=t.scopeURL;const r=new e.ParsedURL.ParsedURL(t.scopeURL);this.securityOrigin=r.securityOrigin(),this.isDeleted=t.isDeleted}fingerprint(){return this.#Sd}versionsByMode(){const e=new Map;for(const t of this.versions.values())e.set(t.mode(),t);return e}updateVersion(e){this.#Sd=Symbol("fingerprint");let t=this.versions.get(e.versionId);return t?(t.update(e),t):(t=new $i(this,e),this.versions.set(e.versionId,t),t)}isRedundant(){for(const e of this.versions.values())if(!e.isStoppedAndRedundant())return!1;return!0}shouldBeRemoved(){return this.isRedundant()&&(!this.errors.length||this.deleting)}canBeRemoved(){return this.isDeleted||this.deleting}clearErrors(){this.#Sd=Symbol("fingerprint"),this.errors=[]}}class Ji{#er;#Cd;#Rd;constructor(e,t){this.#er=e,this.#Cd=t,this.#Rd=new Map,t.addEventListener("RegistrationUpdated",this.registrationsUpdated,this),t.addEventListener("RegistrationDeleted",this.registrationsUpdated,this),je.instance().addModelListener(ar,lr.ExecutionContextCreated,this.executionContextCreated,this)}registrationsUpdated(){this.#Rd.clear();const e=this.#Cd.registrations().values();for(const t of e)for(const e of t.versions.values())e.targetId&&this.#Rd.set(e.targetId,e);this.updateAllContextLabels()}executionContextCreated(e){const t=e.data,r=this.serviceWorkerTargetId(t.target());r&&this.updateContextLabel(t,this.#Rd.get(r)||null)}serviceWorkerTargetId(e){return e.parentTarget()!==this.#er||e.type()!==Ue.ServiceWorker?null:e.id()}updateAllContextLabels(){for(const e of je.instance().targets()){const t=this.serviceWorkerTargetId(e);if(!t)continue;const r=this.#Rd.get(t)||null,n=e.model(ar),s=n?n.executionContexts():[];for(const e of s)this.updateContextLabel(e,r)}}updateContextLabel(t,r){if(!r)return void t.setLabel("");const n=e.ParsedURL.ParsedURL.fromString(t.origin),s=n?n.lastPathComponentWithFragment():t.name,i=$i.Status[r.status];t.setLabel(ji(_i.sSS,{PH1:s,PH2:r.id,PH3:i()}))}}c.register(Wi,{capabilities:16384,autostart:!0});var Yi=Object.freeze({__proto__:null,ServiceWorkerManager:Wi,ServiceWorkerVersionState:Ki,ServiceWorkerRouterRule:Qi,get ServiceWorkerVersion(){return $i},ServiceWorkerRegistration:Xi});function Zi(e){return"kind"in e}function ea(e,t){if(!(void 0===e||e<0))return t[e]}function ta(e){switch(e){case 1:return"global";case 2:return"function";case 3:return"class";case 4:return"block";default:throw new Error(`Unknown scope kind ${e}`)}}var ra=Object.freeze({__proto__:null,decodeOriginalScopes:function(e,t){return e.map((e=>function(e,t){const r=[];let n=0;for(const s of function*(e){const t=new _t(e);let r=0;for(;t.hasNext();){","===t.peek()&&t.next();const[e,n]=[t.nextVLQ(),t.nextVLQ()];if(0===e&&n<r)throw new Error("Malformed original scope encoding: start/end items must be ordered w.r.t. source positions");if(r=n,!t.hasNext()||","===t.peek()){yield{line:e,column:n};continue}const s={line:e,column:n,kind:t.nextVLQ(),flags:t.nextVLQ(),variables:[]};if(1&s.flags&&(s.name=t.nextVLQ()),2&s.flags){const e=t.nextVLQ();for(let r=0;r<e;++r)s.variables.push(t.nextVLQ())}yield s}}(e)){n+=s.line;const{column:e}=s;if(Zi(s)){const i=ta(s.kind),a=ea(s.name,t),o=s.variables.map((e=>t[e]));r.push({start:{line:n,column:e},end:{line:n,column:e},kind:i,name:a,variables:o,children:[]})}else{const t=r.pop();if(!t)throw new Error('Scope items not nested properly: encountered "end" item without "start" item');if(t.end={line:n,column:e},0===r.length)return t;r[r.length-1].children.push(t)}}throw new Error("Malformed original scope encoding")}(e,t)))}});class na extends c{#Yn;constructor(e){super(e),this.#Yn=e.webAuthnAgent(),e.registerWebAuthnDispatcher(new sa(this))}setVirtualAuthEnvEnabled(e){return e?this.#Yn.invoke_enable({enableUI:!0}):this.#Yn.invoke_disable()}async addAuthenticator(e){return(await this.#Yn.invoke_addVirtualAuthenticator({options:e})).authenticatorId}async removeAuthenticator(e){await this.#Yn.invoke_removeVirtualAuthenticator({authenticatorId:e})}async setAutomaticPresenceSimulation(e,t){await this.#Yn.invoke_setAutomaticPresenceSimulation({authenticatorId:e,enabled:t})}async getCredentials(e){return(await this.#Yn.invoke_getCredentials({authenticatorId:e})).credentials}async removeCredential(e,t){await this.#Yn.invoke_removeCredential({authenticatorId:e,credentialId:t})}credentialAdded(e){this.dispatchEventToListeners("CredentialAdded",e)}credentialAsserted(e){this.dispatchEventToListeners("CredentialAsserted",e)}}class sa{#un;constructor(e){this.#un=e}credentialAdded(e){this.#un.credentialAdded(e)}credentialAsserted(e){this.#un.credentialAsserted(e)}}c.register(na,{capabilities:65536,autostart:!1});var ia=Object.freeze({__proto__:null,WebAuthnModel:na});export{Gn as AccessibilityModel,Qn as AutofillModel,As as CPUProfileDataModel,ys as CPUProfilerModel,Hs as CPUThrottlingManager,G as CSSContainerQuery,H as CSSFontFace,Q as CSSLayer,Oe as CSSMatchedStyles,Y as CSSMedia,E as CSSMetadata,rr as CSSModel,ce as CSSProperty,oe as CSSPropertyParser,_ as CSSQuery,Se as CSSRule,ee as CSSScope,ue as CSSStyleDeclaration,He as CSSStyleSheetHeader,pe as CSSSupports,Xn as CategorizedBreakpoint,os as ChildTargetManager,gs as CompilerSourceMappingContentProvider,rs as Connections,Ps as ConsoleModel,B as Cookie,In as CookieModel,Sn as CookieParser,Qs as DOMDebuggerModel,an as DOMModel,Fr as DebuggerModel,Ds as EmulationModel,Zs as EventBreakpointsModel,ei as FrameAssociated,Ke as FrameManager,ir as HeapProfilerModel,at as IOModel,oi as IsolateManager,di as IssuesModel,ci as LayerTreeBase,Is as LogModel,xt as NetworkManager,jn as NetworkRequest,Ur as OverlayColorGenerator,Xr as OverlayModel,qr as OverlayPersistentHighlighter,gi as PageLoad,Nt as PageResourceLoader,bi as PaintProfiler,vi as PerformanceMetricsModel,Mi as PreloadingModel,N as ProfileTreeModel,Li as ReactNativeApplicationModel,st as RemoteObject,dn as Resource,yn as ResourceTreeModel,hr as RuntimeModel,h as SDKModel,Ei as ScreenCaptureModel,vr as Script,hn as SecurityOriginManager,xn as ServerSentEventProtocol,En as ServerTiming,qi as ServiceWorkerCacheModel,Yi as ServiceWorkerManager,jt as SourceMap,Gt as SourceMapManager,ra as SourceMapScopes,Ni as StorageBucketsModel,pn as StorageKeyManager,_e as Target,Ve as TargetManager,ia as WebAuthnModel};
