import*as t from"../../../core/platform/platform.js";import*as e from"../helpers/helpers.js";import*as n from"../types/types.js";const o=new Map,s=new Map;const a=new Set(["non_blocking","potentially_blocking"]);function r(t,e){return!a.has(t.args.data.renderBlocking)&&t.args.data.frame===e}class i{#t;#e=new Map;constructor(t){this.#t=t}async rootCausesForEvent(e,n){const o=this.#e.get(n);if(o)return o;const s=e.LayoutShifts.clusters.flatMap((t=>t.events));s.forEach((e=>function(e,n){t.MapUtilities.getWithDefault(e,n,(()=>({unsizedMedia:[],iframes:[],fontChanges:[],renderBlockingRequests:[],scriptStackTrace:[]})))}(this.#e,e))),await this.blameShifts(s,e);const a=this.#e.get(n);return a||null}async blameShifts(t,e){await this.linkShiftsToLayoutInvalidations(t,e),this.linkShiftsToLayoutEvents(t,e)}async linkShiftsToLayoutInvalidations(e,n){const{prePaintEvents:o,layoutInvalidationEvents:s,scheduleStyleInvalidationEvents:a,backendNodeIds:r}=n.LayoutShifts,i=[...s,...a],u=await this.#t.pushNodesByBackendIdsToFrontend(r),c=new Map;for(let t=0;t<r.length;t++)c.set(r[t],u[t]);const l=d(e,o);for(const e of i){const s=t.ArrayUtilities.nearestIndexFromBeginning(o,(t=>t.ts>e.ts));if(null===s)continue;const a=o[s],r=l.get(a);if(!r)continue;const i=this.getFontChangeRootCause(e,a,n),u=this.getRenderBlockRootCause(e,a,n),d=c.get(e.args.data.nodeId),f=void 0!==d?await this.#t.getNode(d):null;let h=null,g=null;if(f&&e.args.data.reason&&(h=await this.getUnsizedMediaRootCause(e.args.data.reason,f),g=this.getIframeRootCause(e.args.data.reason,f)),h||g||i||u)for(const n of r){const o=t.MapUtilities.getWithDefault(this.#e,n,(()=>({unsizedMedia:[],iframes:[],fontChanges:[],renderBlockingRequests:[],scriptStackTrace:[]})));h&&!o.unsizedMedia.some((t=>t.node.nodeId===h?.node.nodeId))&&n.args.frame===e.args.data.frame&&o.unsizedMedia.push(h),g&&!o.iframes.some((t=>t.iframe.nodeId===g?.iframe.nodeId))&&o.iframes.push(g),i&&(o.fontChanges=i),u&&(o.renderBlockingRequests=u)}}}linkShiftsToLayoutEvents(e,n){const{prePaintEvents:o}=n.LayoutShifts,s=d(e,o),a=n.Renderer.allTraceEntries.filter((({name:t})=>"Layout"===t));for(const e of a){const a=t.ArrayUtilities.nearestIndexFromBeginning(o,(t=>t.ts>e.ts+(e.dur||0)));if(null===a)continue;const r=o[a],i=s.get(r);if(!i)continue;const u=n.Renderer.entryToNode.get(e),c=u?n.Initiators.eventToInitiator.get(u.entry):null,l=c?.args?.data?.stackTrace;if(l)for(const e of i){const n=t.MapUtilities.getWithDefault(this.#e,e,(()=>({unsizedMedia:[],iframes:[],fontChanges:[],renderBlockingRequests:[],scriptStackTrace:[]})));0===n.scriptStackTrace.length&&(n.scriptStackTrace=l)}}}async getUnsizedMediaRootCause(t,e){if("Size changed"!==t)return null;const n=await this.#t.getComputedStyleForNode(e.nodeId),o=new Map(n.map((t=>[t.name,t.value])));if(o&&!await async function(t,e){const n=t.localName,o=function(t){const e=/^url\("([^"]+)"\)$/,n=t.get("background-image");if(!n)return!1;return e.test(n)}(e);if("img"!==n&&"video"!==n&&!o)return!1;return!function(t){const e=t.get("position");if(!e)return!1;return"fixed"===e||"absolute"===e}(e)}(e,o))return null;const s=await this.getNodeAuthoredDimensions(e);if(function(t){const{height:e,width:n,aspectRatio:o}=t,s=Boolean(e&&c(e)),a=Boolean(n&&c(n)),r=Boolean(o&&c(o)),i=(s||a)&&r;return s&&a||i}(s))return null;return{node:e,authoredDimensions:s,computedDimensions:o?function(t){const e={};return e.height=t.get("height"),e.width=t.get("width"),e.aspectRatio=t.get("aspect-ratio"),e}(o):{}}}getIframeRootCause(t,e){if("IFRAME"!==e.nodeName&&"Style changed"!==t&&"Added to layout"!==t)return null;const n=u(e);return n?{iframe:n}:null}requestsInInvalidationWindow(o,s){const a=s.NetworkRequests.byTime.sort(((t,e)=>t.ts+t.dur-(e.ts+e.dur))),r=t.ArrayUtilities.nearestIndexFromEnd(a,(t=>t.ts+t.dur<o.ts));if(null===r)return[];const i=e.Timing.secondsToMicroseconds(n.Timing.Seconds(.5)),u=[];for(let t=r;t>-1;t--){const e=a[t],n=e.ts+e.dur;if(!(o.ts-n<i))break;{const t={request:e},n=this.#t.getInitiatorForRequest(e.args.data.url);t.initiator=n||void 0,u.push(t)}}return u}getFontChangeRootCause(t,e,n){if("Fonts changed"!==t.args.data.reason)return null;const s=o.get(e);if(void 0!==s)return s;const a=this.getFontRequestsInInvalidationWindow(this.requestsInInvalidationWindow(t,n));return o.set(e,a),a}getFontRequestsInInvalidationWindow(t){const e=[];for(let n=0;n<t.length;n++){const o=t[n];if(!o.request.args.data.mimeType.startsWith("font"))continue;const s=this.#t.fontFaceForSource(o.request.args.data.url);s&&"optional"!==s.fontDisplay&&(o.fontFace=s,e.push(o))}return e}getRenderBlockRootCause(t,e,n){const o=s.get(e);if(void 0!==o)return o;const a=function(t){const e=[];for(let n=0;n<t.length;n++){const o=t[n].request.args.data.frame;r(t[n].request,o)&&e.push(t[n])}return e}(this.requestsInInvalidationWindow(t,n));return s.set(e,a),a}async nodeMatchedStylesPropertyGetter(t){const e=await this.#t.getMatchedStylesForNode(t.nodeId);return function(t){let n=e.inlineStyle?.cssProperties.find((e=>e.name===t));if(n)return n.value;for(const{rule:n}of e.matchedCSSRules||[]){const e=n.style.cssProperties.find((e=>e.name===t));if(e)return e.value}return n=e.attributesStyle?.cssProperties.find((e=>e.name===t)),n?n.value:null}}async getNodeAuthoredDimensions(t){const e={},n=await this.nodeMatchedStylesPropertyGetter(t);if(!n)return e;const o=t.attributes||[],s=[];for(let t=0;t<o.length;t+=2)s.push({name:o[t],value:o[t+1]});const a=s.find((t=>"height"===t.name&&l(t))),r=s.find((t=>"width"===t.name&&l(t))),i=n("aspect-ratio")||void 0;if(a&&r&&i)return{height:a.value,width:r.value,aspectRatio:i};return{height:n("height")||void 0,width:n("width")||void 0,aspectRatio:i}}}function u(t){if("IFRAME"===t.nodeName)return t;const e=t.children;if(!e)return null;for(const t of e){const e=u(t);if(e)return e}return null}function c(t){return!["auto","initial","unset","inherit"].includes(t)}function l(t){return parseInt(t.value,10)>=0}function d(e,n){const o=new Map;for(const s of n){const n=t.ArrayUtilities.nearestIndexFromBeginning(e,(t=>t.ts>=s.ts));if(null!==n)for(let a=n;a<e.length;a++){const n=e[a];if(n.ts>=s.ts&&n.ts<=s.ts+s.dur){t.MapUtilities.getWithDefault(o,s,(()=>[])).push(n)}if(n.ts>s.ts+s.dur)break}}return o}var f=Object.freeze({__proto__:null,RootCauses:class{layoutShifts;constructor(t){this.layoutShifts=new i(t)}}});export{f as RootCauses};
