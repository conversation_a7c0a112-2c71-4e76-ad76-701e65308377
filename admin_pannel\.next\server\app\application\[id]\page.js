/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/application/[id]/page";
exports.ids = ["app/application/[id]/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapplication%2F%5Bid%5D%2Fpage&page=%2Fapplication%2F%5Bid%5D%2Fpage&appPaths=%2Fapplication%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fapplication%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapplication%2F%5Bid%5D%2Fpage&page=%2Fapplication%2F%5Bid%5D%2Fpage&appPaths=%2Fapplication%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fapplication%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'application',\n        {\n        children: [\n        '[id]',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/application/[id]/page.tsx */ \"(rsc)/./app/application/[id]/page.tsx\")), \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/application/[id]/layout.tsx */ \"(rsc)/./app/application/[id]/layout.tsx\")), \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        \n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/application/[id]/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/application/[id]/page\",\n        pathname: \"/application/[id]\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapplication%2F%5Bid%5D%2Fpage&page=%2Fapplication%2F%5Bid%5D%2Fpage&appPaths=%2Fapplication%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fapplication%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Capplication%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Capplication%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/application/[id]/page.tsx */ \"(ssr)/./app/application/[id]/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1BST0RVQ1RJT04lNUMlNUNQUk9KRUNUXzAxJTVDJTVDYWRtaW5fcGFubmVsJTVDJTVDYXBwJTVDJTVDYXBwbGljYXRpb24lNUMlNUMlNUJpZCU1RCU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSwwS0FBeUgiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZG1pbl9wYW5uZWwvPzc4NzUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJDOlxcXFxQcm9qZWN0c1xcXFxQUk9EVUNUSU9OXFxcXFBST0pFQ1RfMDFcXFxcYWRtaW5fcGFubmVsXFxcXGFwcFxcXFxhcHBsaWNhdGlvblxcXFxbaWRdXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Capplication%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5CclientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5CclientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/clientLayout.tsx */ \"(ssr)/./app/clientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1BST0RVQ1RJT04lNUMlNUNQUk9KRUNUXzAxJTVDJTVDYWRtaW5fcGFubmVsJTVDJTVDYXBwJTVDJTVDY2xpZW50TGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDUFJPRFVDVElPTiU1QyU1Q1BST0pFQ1RfMDElNUMlNUNhZG1pbl9wYW5uZWwlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDUFJPRFVDVElPTiU1QyU1Q1BST0pFQ1RfMDElNUMlNUNhZG1pbl9wYW5uZWwlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0pBQTJJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWRtaW5fcGFubmVsLz8wYmYxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFByb2plY3RzXFxcXFBST0RVQ1RJT05cXFxcUFJPSkVDVF8wMVxcXFxhZG1pbl9wYW5uZWxcXFxcYXBwXFxcXGNsaWVudExheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5CclientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./app/application/[id]/page.tsx":
/*!***************************************!*\
  !*** ./app/application/[id]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CarDetails)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Paper_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Paper,Snackbar!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Paper_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Paper,Snackbar!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Paper_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Paper,Snackbar!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Snackbar/Snackbar.js\");\n/* harmony import */ var _barrel_optimize_names_Alert_Button_Paper_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Alert,Button,Paper,Snackbar!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Alert/Alert.js\");\n/* harmony import */ var swiper_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! swiper/react */ \"(ssr)/./node_modules/swiper/swiper-react.mjs\");\n/* harmony import */ var swiper_modules__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! swiper/modules */ \"(ssr)/./node_modules/swiper/modules/index.mjs\");\n/* harmony import */ var _context_loadingContext__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../context/loadingContext */ \"(ssr)/./app/context/loadingContext.tsx\");\n/* harmony import */ var swiper_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! swiper/css */ \"(ssr)/./node_modules/swiper/swiper.css\");\n/* harmony import */ var swiper_css_navigation__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! swiper/css/navigation */ \"(ssr)/./node_modules/swiper/modules/navigation.css\");\n/* harmony import */ var swiper_css_pagination__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! swiper/css/pagination */ \"(ssr)/./node_modules/swiper/modules/pagination.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\nfunction CarDetails() {\n    const { id } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [car, setCar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [snackbar, setSnackbar] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        open: false,\n        message: \"\",\n        severity: \"success\"\n    });\n    const { showLoading, hideLoading } = (0,_context_loadingContext__WEBPACK_IMPORTED_MODULE_5__.useLoading)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchCarDetails();\n    }, [\n        id\n    ]);\n    const fetchCarDetails = async ()=>{\n        try {\n            showLoading(\"Loading car details...\");\n            const response = await fetch(`${\"http://localhost:5000\"}/cars/${id}`);\n            if (!response.ok) {\n                throw new Error(`Failed to fetch car details: ${response.statusText}`);\n            }\n            const data = await response.json();\n            if (data.success) {\n                setCar(data.data);\n            } else {\n                throw new Error(data.message || \"Failed to fetch car details\");\n            }\n        } catch (error) {\n            setSnackbar({\n                open: true,\n                message: error instanceof Error ? error.message : \"Failed to fetch car details\",\n                severity: \"error\"\n            });\n        } finally{\n            hideLoading();\n        }\n    };\n    const updateCarStatus = async (newStatus)=>{\n        try {\n            showLoading(`${newStatus === \"approved\" ? \"Approving\" : newStatus === \"rejected\" ? \"Rejecting\" : \"Marking as sold\"} car...`);\n            const response = await fetch(`${\"http://localhost:5000\"}/cars/${id}/status`, {\n                method: \"PUT\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    status: newStatus\n                })\n            });\n            const data = await response.json();\n            if (data.success) {\n                setCar((prev)=>prev ? {\n                        ...prev,\n                        carStatus: newStatus\n                    } : null);\n                // Create a more detailed success message based on the status\n                let statusMessage = \"\";\n                if (newStatus === \"sold\") {\n                    statusMessage = \". Car has been removed from user's onSaleCars and added to soldCars.\";\n                } else if (newStatus === \"approved\") {\n                    statusMessage = \". Car has been approved and is now visible to buyers.\";\n                } else if (newStatus === \"rejected\") {\n                    statusMessage = \". Car has been rejected and removed from user's onSaleCars.\";\n                }\n                setSnackbar({\n                    open: true,\n                    message: `Car successfully marked as ${newStatus}${statusMessage}`,\n                    severity: \"success\"\n                });\n                console.log(`Car ${id} status updated to ${newStatus}. User collections updated.`);\n            } else {\n                throw new Error(data.message || \"Failed to update status\");\n            }\n        } catch (error) {\n            setSnackbar({\n                open: true,\n                message: error instanceof Error ? error.message : \"Failed to update status\",\n                severity: \"error\"\n            });\n        } finally{\n            hideLoading();\n        }\n    };\n    if (!car) {\n        return null; // Loading overlay will be shown by LoadingProvider\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Paper_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                elevation: 3,\n                className: \"m-6 p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center mb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-2xl font-bold\",\n                                children: \"Car Details\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Paper_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        variant: \"contained\",\n                                        color: \"success\",\n                                        onClick: ()=>updateCarStatus(\"approved\"),\n                                        disabled: car.carStatus === \"approved\" || car.carStatus === \"sold\",\n                                        children: \"Approve\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Paper_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        variant: \"contained\",\n                                        color: \"error\",\n                                        onClick: ()=>updateCarStatus(\"rejected\"),\n                                        disabled: car.carStatus === \"rejected\" || car.carStatus === \"sold\",\n                                        children: \"Reject\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Paper_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        variant: \"contained\",\n                                        color: \"secondary\",\n                                        onClick: ()=>updateCarStatus(\"sold\"),\n                                        disabled: car.carStatus === \"sold\" || car.carStatus === \"rejected\",\n                                        children: \"Mark as Sold\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                        lineNumber: 156,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: `px-3 py-1 rounded-full text-sm font-medium ${car.carStatus === \"approved\" ? \"bg-green-100 text-green-800\" : car.carStatus === \"rejected\" ? \"bg-red-100 text-red-800\" : car.carStatus === \"sold\" ? \"bg-blue-100 text-blue-800\" : \"bg-yellow-100 text-yellow-800\"}`,\n                            children: car.carStatus.toUpperCase()\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                            lineNumber: 194,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                        lineNumber: 193,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.Swiper, {\n                            className: \"w-full max-w-2xl mx-auto\",\n                            slidesPerView: 1,\n                            navigation: true,\n                            modules: [\n                                swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Navigation,\n                                swiper_modules__WEBPACK_IMPORTED_MODULE_4__.Pagination\n                            ],\n                            pagination: {\n                                clickable: true\n                            },\n                            children: car.images?.map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(swiper_react__WEBPACK_IMPORTED_MODULE_3__.SwiperSlide, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                        src: image,\n                                        alt: `${car.carBrand} ${car.carModel}`,\n                                        className: \"w-full h-[400px] object-cover rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 222,\n                                        columnNumber: 17\n                                    }, this)\n                                }, index, false, {\n                                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 221,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                        lineNumber: 210,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-2 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Basic Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Brand\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 239,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: car.carBrand\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 238,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 243,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: car.carModel\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 244,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Year\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 247,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: car.modelYear\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 248,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Registration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 251,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: car.registrationNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 252,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 250,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Fuel Type\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 255,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: car.fuelType\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 256,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Transmission\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 259,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: car.transmissionType\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 260,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Kilometers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 263,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            car.km.toLocaleString(),\n                                                            \" km\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 264,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 262,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Price\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 267,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: [\n                                                            \"₹\",\n                                                            car.exceptedPrice.toLocaleString()\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 268,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 266,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 237,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-xl font-semibold mb-4\",\n                                        children: \"Owner Information\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 277,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Name\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: car.ownerName\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 279,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Phone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 284,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: car.phoneNumber\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 283,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Location\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: car.location\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 287,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-600\",\n                                                        children: \"Posted Date\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 292,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium\",\n                                                        children: new Date(car.postedDate).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 293,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 291,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 278,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                        lineNumber: 233,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-xl font-semibold mb-4\",\n                                children: \"Description\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-700 whitespace-pre-line\",\n                                children: car.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                lineNumber: 155,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Paper_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                open: snackbar.open,\n                autoHideDuration: 6000,\n                onClose: ()=>setSnackbar((prev)=>({\n                            ...prev,\n                            open: false\n                        })),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Alert_Button_Paper_Snackbar_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    onClose: ()=>setSnackbar((prev)=>({\n                                ...prev,\n                                open: false\n                            })),\n                    severity: snackbar.severity,\n                    children: snackbar.message\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                    lineNumber: 313,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\page.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/application/[id]/page.tsx\n");

/***/ }),

/***/ "(ssr)/./app/clientLayout.tsx":
/*!******************************!*\
  !*** ./app/clientLayout.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_authContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context/authContext */ \"(ssr)/./app/context/authContext.tsx\");\n/* harmony import */ var _context_loadingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./context/loadingContext */ \"(ssr)/./app/context/loadingContext.tsx\");\n/* harmony import */ var _components_navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/navbar */ \"(ssr)/./app/components/navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_authContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_loadingContext__WEBPACK_IMPORTED_MODULE_3__.LoadingProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\clientLayout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\clientLayout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\clientLayout.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\clientLayout.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\clientLayout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY2xpZW50TGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDMEI7QUFDMkI7QUFDTTtBQUNsQjtBQUUxQixTQUFTSSxhQUFhLEVBQ25DQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0osOERBQVlBO2tCQUNYLDRFQUFDQyxvRUFBZUE7c0JBQ2QsNEVBQUNJO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0osMERBQU1BOzs7OztrQ0FDUCw4REFBQ0s7d0JBQUtELFdBQVU7a0NBQXFCRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUsvQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FkbWluX3Bhbm5lbC8uL2FwcC9jbGllbnRMYXlvdXQudHN4PzQ0Y2MiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSBcIi4vY29udGV4dC9hdXRoQ29udGV4dFwiO1xyXG5pbXBvcnQgeyBMb2FkaW5nUHJvdmlkZXIgfSBmcm9tIFwiLi9jb250ZXh0L2xvYWRpbmdDb250ZXh0XCI7XHJcbmltcG9ydCBOYXZiYXIgZnJvbSBcIi4vY29tcG9uZW50cy9uYXZiYXJcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxBdXRoUHJvdmlkZXI+XHJcbiAgICAgIDxMb2FkaW5nUHJvdmlkZXI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxyXG4gICAgICAgICAgPE5hdmJhciAvPlxyXG4gICAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG9cIj57Y2hpbGRyZW59PC9tYWluPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L0xvYWRpbmdQcm92aWRlcj5cclxuICAgIDwvQXV0aFByb3ZpZGVyPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQXV0aFByb3ZpZGVyIiwiTG9hZGluZ1Byb3ZpZGVyIiwiTmF2YmFyIiwiQ2xpZW50TGF5b3V0IiwiY2hpbGRyZW4iLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/clientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/LoadingOverlay.tsx":
/*!*******************************************!*\
  !*** ./app/components/LoadingOverlay.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingOverlay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CircularProgress!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LoadingOverlay({ isLoading, message = \"Loading...\" }) {\n    if (!isLoading) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black/30 backdrop-blur-sm\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\LoadingOverlay.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 bg-white p-6 rounded-lg shadow-xl flex flex-col items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\LoadingOverlay.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\LoadingOverlay.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\LoadingOverlay.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\LoadingOverlay.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Mb2FkaW5nT3ZlcmxheS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUMwQjtBQUN1QjtBQU9sQyxTQUFTRSxlQUFlLEVBQ3JDQyxTQUFTLEVBQ1RDLFVBQVUsWUFBWSxFQUNGO0lBQ3BCLElBQUksQ0FBQ0QsV0FBVyxPQUFPO0lBRXZCLHFCQUNFLDhEQUFDRTtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7Ozs7OzswQkFDZiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTCw0RkFBZ0JBOzs7OztrQ0FDakIsOERBQUNNO3dCQUFFRCxXQUFVO2tDQUFpQkY7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUl0QyIsInNvdXJjZXMiOlsid2VicGFjazovL2FkbWluX3Bhbm5lbC8uL2FwcC9jb21wb25lbnRzL0xvYWRpbmdPdmVybGF5LnRzeD8wZDQxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IENpcmN1bGFyUHJvZ3Jlc3MgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5cclxuaW50ZXJmYWNlIExvYWRpbmdPdmVybGF5UHJvcHMge1xyXG4gIGlzTG9hZGluZzogYm9vbGVhbjtcclxuICBtZXNzYWdlPzogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nT3ZlcmxheSh7XHJcbiAgaXNMb2FkaW5nLFxyXG4gIG1lc3NhZ2UgPSBcIkxvYWRpbmcuLi5cIixcclxufTogTG9hZGluZ092ZXJsYXlQcm9wcykge1xyXG4gIGlmICghaXNMb2FkaW5nKSByZXR1cm4gbnVsbDtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ibGFjay8zMCBiYWNrZHJvcC1ibHVyLXNtXCI+PC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBiZy13aGl0ZSBwLTYgcm91bmRlZC1sZyBzaGFkb3cteGwgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cclxuICAgICAgICA8Q2lyY3VsYXJQcm9ncmVzcyAvPlxyXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDBcIj57bWVzc2FnZX08L3A+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDaXJjdWxhclByb2dyZXNzIiwiTG9hZGluZ092ZXJsYXkiLCJpc0xvYWRpbmciLCJtZXNzYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/LoadingOverlay.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/navbar.tsx":
/*!***********************************!*\
  !*** ./app/components/navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Button,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/AppBar/AppBar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Button,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Toolbar/Toolbar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Button,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Button,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Button,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Dashboard,DirectionsCar,Forum,Logout,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Dashboard,DirectionsCar,Forum,Logout,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/DirectionsCar.js\");\n/* harmony import */ var _barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Dashboard,DirectionsCar,Forum,Logout,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Dashboard,DirectionsCar,Forum,Logout,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Forum.js\");\n/* harmony import */ var _barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dashboard,DirectionsCar,Forum,Logout,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Logout.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_authContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/authContext */ \"(ssr)/./app/context/authContext.tsx\");\n/* harmony import */ var _context_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/loadingContext */ \"(ssr)/./app/context/loadingContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Navbar() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isAuthenticated, logout } = (0,_context_authContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showLoading } = (0,_context_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading)();\n    const navItems = [\n        {\n            text: \"Dashboard\",\n            path: \"/\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                lineNumber: 22,\n                columnNumber: 43\n            }, this)\n        },\n        {\n            text: \"Cars\",\n            path: \"/cars\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                lineNumber: 23,\n                columnNumber: 42\n            }, this)\n        },\n        {\n            text: \"Users\",\n            path: \"/users\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                lineNumber: 24,\n                columnNumber: 44\n            }, this)\n        },\n        {\n            text: \"Chats\",\n            path: \"/chats\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                lineNumber: 25,\n                columnNumber: 44\n            }, this)\n        }\n    ];\n    const handleNavigation = (path)=>{\n        showLoading(`Loading ${path === \"/\" ? \"dashboard\" : path.slice(1)}...`);\n        router.push(path);\n    };\n    const handleLogout = ()=>{\n        showLoading(\"Logging out...\");\n        logout();\n    };\n    if (!isAuthenticated || pathname === \"/login\") {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        position: \"static\",\n        color: \"default\",\n        elevation: 1,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    variant: \"h6\",\n                    component: \"div\",\n                    sx: {\n                        flexGrow: 0,\n                        mr: 4\n                    },\n                    children: \"Cars Hub Admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    sx: {\n                        flexGrow: 1,\n                        display: \"flex\",\n                        gap: 2\n                    },\n                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            startIcon: item.icon,\n                            onClick: ()=>handleNavigation(item.path),\n                            variant: pathname === item.path ? \"contained\" : \"text\",\n                            color: pathname === item.path ? \"primary\" : \"inherit\",\n                            children: item.text\n                        }, item.path, false, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 22\n                    }, void 0),\n                    onClick: handleLogout,\n                    color: \"inherit\",\n                    children: \"Logout\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/authContext.tsx":
/*!*************************************!*\
  !*** ./app/context/authContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if there's an active session on mount\n        const authToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"adminAuthToken\");\n        if (authToken) {\n            setIsAuthenticated(true);\n        } else if (window.location.pathname !== \"/login\") {\n            router.push(\"/login\");\n        }\n    }, []);\n    const login = async (username, password)=>{\n        // For demo purposes, we'll use hardcoded credentials\n        // In production, this should make an API call to validate credentials\n        if (username === \"admin\" && password === \"admin123\") {\n            // Set cookie with 1 day expiration\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set(\"adminAuthToken\", \"demo-token\", {\n                expires: 1\n            });\n            setIsAuthenticated(true);\n            return true;\n        }\n        return false;\n    };\n    const logout = ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"adminAuthToken\");\n        setIsAuthenticated(false);\n        router.push(\"/login\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            isAuthenticated,\n            login,\n            logout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\context\\\\authContext.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/authContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/loadingContext.tsx":
/*!****************************************!*\
  !*** ./app/context/loadingContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingProvider: () => (/* binding */ LoadingProvider),\n/* harmony export */   useLoading: () => (/* binding */ useLoading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_LoadingOverlay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/LoadingOverlay */ \"(ssr)/./app/components/LoadingOverlay.tsx\");\n/* __next_internal_client_entry_do_not_use__ LoadingProvider,useLoading auto */ \n\n\n\nconst LoadingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction LoadingProvider({ children }) {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // const searchParams = useSearchParams();\n    // Show loading during navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsLoading(true);\n        const timeout = setTimeout(()=>setIsLoading(false), 500); // Minimum loading time\n        return ()=>clearTimeout(timeout);\n    }, [\n        pathname\n    ]);\n    const showLoading = (msg)=>{\n        setMessage(msg);\n        setIsLoading(true);\n    };\n    const hideLoading = ()=>{\n        setIsLoading(false);\n        setMessage(undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContext.Provider, {\n        value: {\n            showLoading,\n            hideLoading\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingOverlay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isLoading: isLoading,\n                message: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\context\\\\loadingContext.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\context\\\\loadingContext.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\nfunction useLoading() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LoadingContext);\n    if (context === undefined) {\n        throw new Error(\"useLoading must be used within a LoadingProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/loadingContext.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f89e4b1cf322\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZG1pbl9wYW5uZWwvLi9hcHAvZ2xvYmFscy5jc3M/OTkwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY4OWU0YjFjZjMyMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/application/[id]/layout.tsx":
/*!*****************************************!*\
  !*** ./app/application/[id]/layout.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ApplicationIdLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n\nfunction ApplicationIdLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\application\\\\[id]\\\\layout.tsx\",\n        lineNumber: 4,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBwbGljYXRpb24vW2lkXS9sYXlvdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBZSxTQUFTQSxvQkFBb0IsRUFDMUNDLFFBQVEsRUFDZ0M7SUFDeEMscUJBQU8sOERBQUNDO2tCQUFTRDs7Ozs7O0FBQ25CIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWRtaW5fcGFubmVsLy4vYXBwL2FwcGxpY2F0aW9uL1tpZF0vbGF5b3V0LnRzeD8yN2Q5Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcGxpY2F0aW9uSWRMYXlvdXQoe1xyXG4gIGNoaWxkcmVuLFxyXG59OiBSZWFkb25seTx7IGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGUgfT4pIHtcclxuICByZXR1cm4gPHNlY3Rpb24+e2NoaWxkcmVufTwvc2VjdGlvbj47XHJcbn1cclxuIl0sIm5hbWVzIjpbIkFwcGxpY2F0aW9uSWRMYXlvdXQiLCJjaGlsZHJlbiIsInNlY3Rpb24iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/application/[id]/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/application/[id]/page.tsx":
/*!***************************************!*\
  !*** ./app/application/[id]/page.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Projects\PRODUCTION\PROJECT_01\admin_pannel\app\application\[id]\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/clientLayout.tsx":
/*!******************************!*\
  !*** ./app/clientLayout.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Projects\PRODUCTION\PROJECT_01\admin_pannel\app\clientLayout.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* reexport safe */ _metadata__WEBPACK_IMPORTED_MODULE_2__.metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _metadata__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./metadata */ \"(rsc)/./app/metadata.ts\");\n/* harmony import */ var _clientLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./clientLayout */ \"(rsc)/./app/clientLayout.tsx\");\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clientLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBS01BO0FBSmlCO0FBQ2U7QUFDSTtBQUl0QjtBQUVMLFNBQVNHLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXUiwySkFBZTtzQkFDOUIsNEVBQUNFLHFEQUFZQTswQkFBRUU7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJdkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZG1pbl9wYW5uZWwvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcbmltcG9ydCB7IG1ldGFkYXRhIH0gZnJvbSBcIi4vbWV0YWRhdGFcIjtcbmltcG9ydCBDbGllbnRMYXlvdXQgZnJvbSBcIi4vY2xpZW50TGF5b3V0XCI7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcblxuZXhwb3J0IHsgbWV0YWRhdGEgfTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPENsaWVudExheW91dD57Y2hpbGRyZW59PC9DbGllbnRMYXlvdXQ+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJDbGllbnRMYXlvdXQiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/metadata.ts":
/*!*************************!*\
  !*** ./app/metadata.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\nconst metadata = {\n    title: \"Cars Hub Admin\",\n    description: \"Admin panel for Cars Hub application\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbWV0YWRhdGEudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUVPLE1BQU1BLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWRtaW5fcGFubmVsLy4vYXBwL21ldGFkYXRhLnRzPzI3NzIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcclxuICB0aXRsZTogXCJDYXJzIEh1YiBBZG1pblwiLFxyXG4gIGRlc2NyaXB0aW9uOiBcIkFkbWluIHBhbmVsIGZvciBDYXJzIEh1YiBhcHBsaWNhdGlvblwiLFxyXG59O1xyXG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/metadata.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL2FkbWluX3Bhbm5lbC8uL2FwcC9mYXZpY29uLmljbz83N2Y2Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/react-transition-group","vendor-chunks/prop-types","vendor-chunks/stylis","vendor-chunks/react-is","vendor-chunks/js-cookie","vendor-chunks/hoist-non-react-statics","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/object-assign","vendor-chunks/clsx","vendor-chunks/swiper"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapplication%2F%5Bid%5D%2Fpage&page=%2Fapplication%2F%5Bid%5D%2Fpage&appPaths=%2Fapplication%2F%5Bid%5D%2Fpage&pagePath=private-next-app-dir%2Fapplication%2F%5Bid%5D%2Fpage.tsx&appDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();