{"name": "friends-cars", "version": "1.0.0", "description": "Car resale platform with React Native app, Next.js admin panel, and Node.js backend", "private": true, "scripts": {"preinstall": "node -e \"console.log('🚀 Setting up Friends Cars monorepo...')\"", "postinstall": "npm run check", "setup": "npm run setup:server && npm run setup:carapp && npm run setup:admin && npm run copy:firebase", "setup:server": "cd SERVER && npm install", "setup:carapp": "cd CarApp && npm install --legacy-peer-deps", "setup:admin": "cd admin_pannel && npm install", "copy:firebase": "node -e \"const fs=require('fs'); try{fs.copyFileSync('SERVER/carshu-1e768-firebase-adminsdk-fbsvc-3503135aec.json', 'admin_pannel/carshu-1e768-firebase-adminsdk-fbsvc-3503135aec.json'); console.log('✅ Firebase certificate copied to admin_pannel')}catch(e){console.log('⚠️ Firebase certificate not found in SERVER directory')}\"", "dev": "concurrently -n \"SERVER,ADMIN,CARAPP\" -c \"blue,green,yellow\" \"npm run dev:server\" \"npm run dev:admin\" \"npm run dev:carapp\"", "dev:server": "cd SERVER && npm start", "dev:admin": "cd admin_pannel && npm run dev", "dev:carapp": "cd CarApp && npm start", "dev:server-only": "npm run dev:server", "dev:admin-only": "npm run dev:admin", "dev:carapp-only": "npm run dev:carapp", "start": "npm run dev", "check": "node check-setup.js", "lint": "npm run lint:server && npm run lint:carapp && npm run lint:admin", "lint:server": "cd SERVER && (npm run lint || echo 'No lint script in SERVER')", "lint:carapp": "cd CarApp && npm run lint", "lint:admin": "cd admin_pannel && (npm run lint || echo 'No lint script in admin_pannel')", "clean": "npm run clean:server && npm run clean:carapp && npm run clean:admin", "clean:server": "node -e \"const fs=require('fs'); try{fs.rmSync('SERVER/node_modules',{recursive:true,force:true})}catch(e){} try{fs.unlinkSync('SERVER/package-lock.json')}catch(e){}\"", "clean:carapp": "node -e \"const fs=require('fs'); try{fs.rmSync('CarApp/node_modules',{recursive:true,force:true})}catch(e){} try{fs.unlinkSync('CarApp/package-lock.json')}catch(e){}\"", "clean:admin": "node -e \"const fs=require('fs'); try{fs.rmSync('admin_pannel/node_modules',{recursive:true,force:true})}catch(e){} try{fs.unlinkSync('admin_pannel/package-lock.json')}catch(e){}\"", "clean:all": "npm run clean && node -e \"const fs=require('fs'); try{fs.rmSync('node_modules',{recursive:true,force:true})}catch(e){} try{fs.unlinkSync('package-lock.json')}catch(e){}\"", "reinstall": "npm run clean && npm run setup", "reinstall:fresh": "npm run clean:all && npm install && npm run setup", "update": "npm run update:server && npm run update:carapp && npm run update:admin", "update:server": "cd SERVER && npm update", "update:carapp": "cd CarApp && npm update", "update:admin": "cd admin_pannel && npm update", "test": "npm run test:carapp", "test:all": "npm run test:server && npm run test:carapp && npm run test:admin", "test:server": "cd SERVER && (npm test || echo 'No tests in SERVER')", "test:carapp": "cd CarApp && npm test", "test:admin": "cd admin_pannel && (npm test || echo 'No tests in admin_pannel')", "build": "npm run build:admin && npm run build:carapp", "build:server": "cd SERVER && (npm run build || echo 'No build script in SERVER')", "build:admin": "cd admin_pannel && npm run build", "build:carapp": "cd CarApp && expo build:android", "build:carapp:ios": "cd CarApp && expo build:ios", "audit": "npm audit && npm run audit:server && npm run audit:carapp && npm run audit:admin", "audit:server": "cd SERVER && npm audit", "audit:carapp": "cd CarApp && npm audit", "audit:admin": "cd admin_pannel && npm audit", "outdated": "npm outdated && npm run outdated:server && npm run outdated:carapp && npm run outdated:admin", "outdated:server": "cd SERVER && npm outdated", "outdated:carapp": "cd CarApp && npm outdated", "outdated:admin": "cd admin_pannel && npm outdated"}, "devDependencies": {"concurrently": "^8.2.2"}, "keywords": ["react-native", "expo", "nextjs", "nodejs", "car-resale", "mobile-app"], "author": "RoughCipher", "license": "MIT", "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "workspaces": ["CarApp", "admin_pannel", "SERVER"]}