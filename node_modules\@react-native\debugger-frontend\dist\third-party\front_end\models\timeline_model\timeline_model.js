import*as e from"../trace/trace.js";import*as t from"../../core/common/common.js";import*as r from"../../core/i18n/i18n.js";import*as n from"../../core/platform/platform.js";import*as s from"../../core/sdk/sdk.js";const a={threadS:"Thread {PH1}",workerS:"`Worker` — {PH1}",dedicatedWorker:"Dedicated `Worker`",workerSS:"`Worker`: {PH1} — {PH2}"},i=r.i18n.registerUIStrings("models/timeline_model/TimelineModel.ts",a),o=r.i18n.getLocalizedString.bind(void 0,i);class l{isGenericTraceInternal;tracksInternal;namedTracks;inspectedTargetEventsInternal;sessionId;mainFrameNodeId;pageFrames;workerIdByThread;requestsFromBrowser;mainFrame;minimumRecordTimeInternal;maximumRecordTimeInternal;lastScheduleStyleRecalculation;paintImageEventByPixelRefId;lastPaintForLayer;currentScriptEvent;eventStack;browserFrameTracking;persistentIds;legacyCurrentPage;currentTaskLayoutAndRecalcEvents;tracingModelInternal;mainFrameLayerTreeId;#e=!1;constructor(){this.minimumRecordTimeInternal=0,this.maximumRecordTimeInternal=0,this.reset(),this.resetProcessingState(),this.currentTaskLayoutAndRecalcEvents=[],this.tracingModelInternal=null}static forEachEvent(t,r,n,s,a,i,o,d=!0){a=a||0,i=i||1/0;const c=[];for(let h=l.topLevelEventEndingAfter(t,a);h<t.length;++h){const l=t[h],{endTime:m,startTime:u,duration:p}=e.Legacy.timesForEventInMilliseconds(l),g=e.Legacy.phaseForEvent(l);if((m||u)<a)continue;if(u>=i)break;if(d&&e.Types.TraceEvents.isAsyncPhase(g)||e.Types.TraceEvents.isFlowPhase(g))continue;let I=c[c.length-1],f=I&&e.Legacy.timesForEventInMilliseconds(I).endTime;for(;I&&void 0!==f&&f<=u;)c.pop(),n(I),I=c[c.length-1],f=I&&e.Legacy.timesForEventInMilliseconds(I).endTime;o&&!o(l)||(p?(r(l),c.push(l)):s&&s(l,c[c.length-1]||null))}for(;c.length;){const e=c.pop();e&&n(e)}}static topLevelEventEndingAfter(t,r){let s=n.ArrayUtilities.upperBound(t,r,((t,r)=>t-e.Legacy.timesForEventInMilliseconds(r).startTime))-1;for(;s>0&&!e.Legacy.TracingModel.isTopLevelEvent(t[s]);)s--;return Math.max(s,0)}isMarkerEvent(e){switch(e.name){case d.TimeStamp:return!0;case d.MarkFirstPaint:case d.MarkFCP:return Boolean(this.mainFrame)&&e.args.frame===this.mainFrame.frameId&&Boolean(e.args.data);case d.MarkDOMContent:case d.MarkLoad:case d.MarkLCPCandidate:case d.MarkLCPInvalidate:return Boolean(e.args.data.isOutermostMainFrame??e.args.data.isMainFrame);default:return!1}}isInteractiveTimeEvent(e){return e.name===d.InteractiveTime}isLayoutShiftEvent(e){return e.name===d.LayoutShift}static globalEventId(e,t){const r=e.args.data||e.args.beginData,n=r&&r[t];return n?`${e.thread.process().id()}.${n}`:""}static eventFrameId(e){const t=e.args.data||e.args.beginData;return t&&t.frame||null}targetByEvent(t){let r;if(t instanceof e.Legacy.Event)r=t.thread;else{const e=this.tracingModelInternal?.getProcessById(t.pid);r=e?.threadById(t.tid)}if(!r)return null;const n=this.workerIdByThread.get(r),a=s.TargetManager.TargetManager.instance().primaryPageTarget();return n?s.TargetManager.TargetManager.instance().targetById(n):a}isFreshRecording(){return this.#e}setEvents(t,r=!1){if(this.#e=r,this.reset(),this.resetProcessingState(),this.tracingModelInternal=t,this.minimumRecordTimeInternal=t.minimumRecordTime(),this.maximumRecordTimeInternal=t.maximumRecordTime(),this.processSyncBrowserEvents(t),this.browserFrameTracking)this.processThreadsForBrowserFrames(t);else{const e=this.processMetadataEvents(t);this.isGenericTraceInternal=!e,e?this.processMetadataAndThreads(t,e):this.processGenericTrace(t)}this.inspectedTargetEventsInternal.sort(e.Legacy.Event.compareStartTime),this.processAsyncBrowserEvents(t),this.resetProcessingState()}processGenericTrace(t){let r=e.Legacy.TracingModel.browserMainThread(t);!r&&t.sortedProcesses().length&&(r=t.sortedProcesses()[0].sortedThreads()[0]);for(const e of t.sortedProcesses())for(const n of e.sortedThreads())this.processThreadEvents(t,n,n===r,!1,!0,null)}processMetadataAndThreads(e,t){let r=0;for(let s=0,a=t.page.length;s<a;s++){const i=t.page[s],o=i.thread.process(),d=s+1<a?t.page[s+1].startTime:1/0;if(r!==d){this.legacyCurrentPage=i.args.data&&i.args.data.page;for(const r of o.sortedThreads()){let s=null;if(r.name()===l.WorkerThreadName||r.name()===l.WorkerThreadNameLegacy){const e=t.workers.find((e=>{if(e.args.data.workerThreadId!==r.id())return!1;if(e.args.data.sessionId===this.sessionId)return!0;const t=l.eventFrameId(e);return!!t&&Boolean(this.pageFrames.get(t))}));if(!e)continue;const a=e.args.data.workerId;a&&this.workerIdByThread.set(r,a),s=e.args.data.url||n.DevToolsPath.EmptyUrlString}this.processThreadEvents(e,r,r===i.thread,Boolean(s),!0,s)}r=d}}}processThreadsForBrowserFrames(e){const t=new Map;for(const e of this.pageFrames.values())for(let r=0;r<e.processes.length;r++){const n=e.processes[r].processId;let s=t.get(n);s||(s=[],t.set(n,s));const a=r===e.processes.length-1?e.deletedTime||1/0:e.processes[r+1].time;s.push({from:e.processes[r].time,to:a,main:!e.parent,url:e.processes[r].url})}const r=e.devToolsMetadataEvents();for(const s of e.sortedProcesses()){const a=t.get(s.id());if(!a)continue;a.sort(((e,t)=>e.from-t.from||e.to-t.to));let i=null,o=null,d=!1;for(const e of a)e.main&&(d=!0),e.url&&(e.main&&(o=e.url),i=e.url);for(const t of s.sortedThreads())if(t.name()===l.RendererMainThreadName)this.processThreadEvents(e,t,!0,!1,d,d?o:i);else if(t.name()===l.WorkerThreadName||t.name()===l.WorkerThreadNameLegacy){const a=r.find((e=>{if(e.name!==l.DevToolsMetadataEvent.TracingSessionIdForWorker)return!1;if(e.thread.process()!==s)return!1;if(e.args.data.workerThreadId!==t.id())return!1;const r=l.eventFrameId(e);return!!r&&Boolean(this.pageFrames.get(r))}));if(!a)continue;this.workerIdByThread.set(t,a.args.data.workerId||""),this.processThreadEvents(e,t,!1,!0,!1,a.args.data.url||n.DevToolsPath.EmptyUrlString)}else this.processThreadEvents(e,t,!1,!1,!1,null)}}processMetadataEvents(r){const n=r.devToolsMetadataEvents(),s=[],a=[];for(const e of n)if(e.name===l.DevToolsMetadataEvent.TracingStartedInPage){s.push(e),e.args.data&&e.args.data.persistentIds&&(this.persistentIds=!0);(e.args.data&&e.args.data.frames||[]).forEach((t=>this.addPageFrame(e,t))),this.mainFrame=this.rootFrames()[0]}else e.name===l.DevToolsMetadataEvent.TracingSessionIdForWorker?a.push(e):e.name===l.DevToolsMetadataEvent.TracingStartedInBrowser&&(console.assert(!this.mainFrameNodeId,"Multiple sessions in trace"),this.mainFrameNodeId=e.args.frameTreeNodeId);if(!s.length)return null;const i=s[0].args.sessionId||s[0].args.data.sessionId;this.sessionId=i;const o=new Set;const d={page:s.filter((function(e){let t=e.args;t.data&&(t=t.data);const r=t.sessionId;return r===i||(o.add(r),!1)})).sort(e.Legacy.Event.compareStartTime),workers:a.sort(e.Legacy.Event.compareStartTime)};return o.size&&t.Console.Console.instance().error("Timeline recording was started in more than one page simultaneously. Session id mismatch: "+this.sessionId+" and "+[...o]+"."),d}processSyncBrowserEvents(t){const r=e.Legacy.TracingModel.browserMainThread(t);r&&r.events().forEach(this.processBrowserEvent,this)}processAsyncBrowserEvents(t){const r=e.Legacy.TracingModel.browserMainThread(t);r&&this.processAsyncEvents(r)}resetProcessingState(){this.lastScheduleStyleRecalculation={},this.paintImageEventByPixelRefId={},this.lastPaintForLayer={},this.currentScriptEvent=null,this.eventStack=[],this.browserFrameTracking=!1,this.persistentIds=!1,this.legacyCurrentPage=null}processThreadEvents(t,r,s,i,l,m){const u=new h;u.name=r.name()||o(a.threadS,{PH1:r.id()}),u.type=c.Other,u.thread=r,s?(u.type=c.MainThread,u.url=m||n.DevToolsPath.EmptyUrlString,u.forMainFrame=l):i?(u.type=c.Worker,u.url=m||n.DevToolsPath.EmptyUrlString,u.name=u.url?o(a.workerS,{PH1:u.url}):o(a.dedicatedWorker)):r.name().startsWith("CompositorTileWorker")&&(u.type=c.Raster),this.tracksInternal.push(u);const p=r.events();this.eventStack=[];const g=this.eventStack;if(i){const e=p.find((e=>e.name===d.Profile));if(e){const t=this.targetByEvent(e);t&&(u.name=o(a.workerSS,{PH1:t.name(),PH2:u.url}))}}for(let t=0;t<p.length;t++){const r=p[t];let n=g[g.length-1];for(;n&&void 0!==n.endTime&&n.endTime<=r.startTime;)g.pop(),n=g[g.length-1];if(this.processEvent(r)){if(!e.Types.TraceEvents.isAsyncPhase(r.phase)&&r.duration){if(g.length){const e=g[g.length-1];e&&(e.selfTime-=r.duration,e.selfTime<0&&(e.selfTime=0))}r.selfTime=r.duration,g.length||u.tasks.push(r),g.push(r)}u.events.push(r),this.inspectedTargetEventsInternal.push(r)}}this.processAsyncEvents(r)}processAsyncEvents(t){const r=t.asyncEvents(),s=new Map;for(let e=0;e<r.length;++e){const t=r[e];t.name!==d.Animation||(a=c.Animation,s.has(a)||s.set(a,[]),s.get(a)).push(t)}var a;for(const[r,a]of s){const s=this.ensureNamedTrack(r);s.thread=t,s.asyncEvents=n.ArrayUtilities.mergeOrdered(s.asyncEvents,a,e.Legacy.Event.compareStartTime)}}processEvent(t){const r=this.eventStack;r.length||(this.currentTaskLayoutAndRecalcEvents=[]),this.currentScriptEvent&&void 0!==this.currentScriptEvent.endTime&&t.startTime>this.currentScriptEvent.endTime&&(this.currentScriptEvent=null);const n=t.args.data||t.args.beginData||{},s=u.forEvent(t);n.stackTrace&&(s.stackTrace=n.stackTrace.map((e=>{if(t.name!==d.JSSample&&t.name!==d.JSSystemSample&&t.name!==d.JSIdleSample){const t={...e};return--t.lineNumber,--t.columnNumber,t}return e})));let a=l.eventFrameId(t);const i=r[r.length-1];switch(!a&&i&&(a=u.forEvent(i).frameId),s.frameId=a||this.mainFrame&&this.mainFrame.frameId||"",t.name){case d.ResourceSendRequest:case d.WebSocketCreate:if(!(r[r.length-1]instanceof e.Legacy.PayloadEvent))break;s.url=n.url;break;case d.ScheduleStyleRecalculation:if(!(t instanceof e.Legacy.PayloadEvent))break;this.lastScheduleStyleRecalculation[n.frame]=t.rawPayload();break;case d.UpdateLayoutTree:case d.RecalculateStyles:this.currentScriptEvent&&this.currentTaskLayoutAndRecalcEvents.push(t);break;case d.Layout:{const e=t.args?.beginData?.frame;if(!e)break;if(t.args.endData)if(t.args.endData.layoutRoots)for(let e=0;e<t.args.endData.layoutRoots.length;++e)s.backendNodeIds.push(t.args.endData.layoutRoots[e].nodeId);else s.backendNodeIds.push(t.args.endData.rootNode);this.currentScriptEvent&&this.currentTaskLayoutAndRecalcEvents.push(t);break}case d.FunctionCall:"string"==typeof n.scriptName&&(n.url=n.scriptName),"number"==typeof n.scriptLine&&(n.lineNumber=n.scriptLine);case d.EvaluateScript:case d.CompileScript:case d.CacheScript:"number"==typeof n.lineNumber&&--n.lineNumber,"number"==typeof n.columnNumber&&--n.columnNumber;case d.RunMicrotasks:this.currentScriptEvent||(this.currentScriptEvent=t);break;case d.SetLayerTreeId:{if(this.sessionId&&n.sessionId&&this.sessionId===n.sessionId){this.mainFrameLayerTreeId=n.layerTreeId;break}const e=l.eventFrameId(t),r=e?this.pageFrames.get(e):null;if(!r||r.parent)return!1;this.mainFrameLayerTreeId=n.layerTreeId;break}case d.Paint:{if("nodeId"in n&&s.backendNodeIds.push(n.nodeId),!n.layerId)break;const e=n.layerId;this.lastPaintForLayer[e]=t;break}case d.ScrollLayer:s.backendNodeIds.push(n.nodeId);break;case d.PaintImage:s.backendNodeIds.push(n.nodeId),s.url=n.url;break;case d.DecodeImage:case d.ResizeImage:{let e=this.findAncestorEvent(d.PaintImage);if(!e){const t=this.findAncestorEvent(d.DecodeLazyPixelRef);e=t&&this.paintImageEventByPixelRefId[t.args.LazyPixelRef]}if(!e)break;const t=u.forEvent(e);s.backendNodeIds.push(t.backendNodeIds[0]),s.url=t.url;break}case d.DrawLazyPixelRef:{const e=this.findAncestorEvent(d.PaintImage);if(!e)break;this.paintImageEventByPixelRefId[t.args.LazyPixelRef]=e;const r=u.forEvent(e);s.backendNodeIds.push(r.backendNodeIds[0]),s.url=r.url;break}case d.FrameStartedLoading:if(s.frameId!==t.args.frame)return!1;break;case d.MarkLCPCandidate:s.backendNodeIds.push(n.nodeId);break;case d.MarkDOMContent:case d.MarkLoad:{const e=l.eventFrameId(t);if(!e||!this.pageFrames.has(e))return!1;break}case d.CommitLoad:{if(this.browserFrameTracking)break;const e=l.eventFrameId(t),r=Boolean(n.isOutermostMainFrame??n.isMainFrame),s=e?this.pageFrames.get(e):null;if(s)s.update(t.startTime,n);else if(this.persistentIds){if(r)return!1;if(!this.addPageFrame(t,n))return!1}else if(n.page&&n.page!==this.legacyCurrentPage)return!1;if(r&&e){const t=this.pageFrames.get(e);t&&(this.mainFrame=t)}break}}return!0}processBrowserEvent(t){if(t.name!==d.ResourceWillSendRequest){if(t.hasCategory(e.Legacy.DevToolsMetadataEventCategory)&&t.args.data){const e=t.args.data;if(t.name===l.DevToolsMetadataEvent.TracingStartedInBrowser){if(!e.persistentIds)return;this.browserFrameTracking=!0,this.mainFrameNodeId=e.frameTreeNodeId;return void(e.frames||[]).forEach((e=>{const t=e.parent&&this.pageFrames.get(e.parent);if(e.parent&&!t)return;let r=this.pageFrames.get(e.frame);r||(r=new m(e),this.pageFrames.set(r.frameId,r),t?t.addChild(r):this.mainFrame=r),r.update(this.minimumRecordTimeInternal,e)}))}if(t.name===l.DevToolsMetadataEvent.FrameCommittedInBrowser&&this.browserFrameTracking){let r=this.pageFrames.get(e.frame);if(!r){const t=e.parent&&this.pageFrames.get(e.parent);r=new m(e),this.pageFrames.set(r.frameId,r),t&&t.addChild(r)}return void r.update(t.startTime,e)}if(t.name===l.DevToolsMetadataEvent.ProcessReadyInBrowser&&this.browserFrameTracking){const t=this.pageFrames.get(e.frame);return void(t&&t.processReady(e.processPseudoId,e.processId))}if(t.name===l.DevToolsMetadataEvent.FrameDeletedInBrowser&&this.browserFrameTracking){const r=this.pageFrames.get(e.frame);return void(r&&(r.deletedTime=t.startTime))}}}else{const e=t.args?.data?.requestId;"string"==typeof e&&this.requestsFromBrowser.set(e,t)}}ensureNamedTrack(e){let t=this.namedTracks.get(e);return t||(t=new h,t.type=e,this.tracksInternal.push(t),this.namedTracks.set(e,t),t)}findAncestorEvent(e){for(let t=this.eventStack.length-1;t>=0;--t){const r=this.eventStack[t];if(r.name===e)return r}return null}addPageFrame(e,t){const r=t.parent&&this.pageFrames.get(t.parent);if(t.parent&&!r)return!1;const n=new m(t);return this.pageFrames.set(n.frameId,n),n.update(e.startTime,t),r&&r.addChild(n),!0}reset(){this.isGenericTraceInternal=!1,this.tracksInternal=[],this.namedTracks=new Map,this.inspectedTargetEventsInternal=[],this.sessionId=null,this.mainFrameNodeId=null,this.workerIdByThread=new WeakMap,this.pageFrames=new Map,this.requestsFromBrowser=new Map,this.minimumRecordTimeInternal=0,this.maximumRecordTimeInternal=0}isGenericTrace(){return this.isGenericTraceInternal}tracingModel(){return this.tracingModelInternal}minimumRecordTime(){return this.minimumRecordTimeInternal}maximumRecordTime(){return this.maximumRecordTimeInternal}inspectedTargetEvents(){return this.inspectedTargetEventsInternal}tracks(){return this.tracksInternal}rootFrames(){return Array.from(this.pageFrames.values()).filter((e=>!e.parent))}pageURL(){return this.mainFrame&&this.mainFrame.url||n.DevToolsPath.EmptyUrlString}pageFrameById(e){return e&&this.pageFrames.get(e)||null}}var d,c;!function(e){e.Task="RunTask",e.Program="Program",e.EventDispatch="EventDispatch",e.GPUTask="GPUTask",e.Animation="Animation",e.RequestMainThreadFrame="RequestMainThreadFrame",e.BeginFrame="BeginFrame",e.NeedsBeginFrameChanged="NeedsBeginFrameChanged",e.BeginMainThreadFrame="BeginMainThreadFrame",e.ActivateLayerTree="ActivateLayerTree",e.DrawFrame="DrawFrame",e.DroppedFrame="DroppedFrame",e.HitTest="HitTest",e.ScheduleStyleRecalculation="ScheduleStyleRecalculation",e.RecalculateStyles="RecalculateStyles",e.UpdateLayoutTree="UpdateLayoutTree",e.InvalidateLayout="InvalidateLayout",e.Layerize="Layerize",e.Layout="Layout",e.LayoutShift="LayoutShift",e.UpdateLayer="UpdateLayer",e.UpdateLayerTree="UpdateLayerTree",e.PaintSetup="PaintSetup",e.Paint="Paint",e.PaintImage="PaintImage",e.PrePaint="PrePaint",e.Rasterize="Rasterize",e.RasterTask="RasterTask",e.ScrollLayer="ScrollLayer",e.Commit="Commit",e.CompositeLayers="CompositeLayers",e.ComputeIntersections="IntersectionObserverController::computeIntersections",e.InteractiveTime="InteractiveTime",e.ParseHTML="ParseHTML",e.ParseAuthorStyleSheet="ParseAuthorStyleSheet",e.TimerInstall="TimerInstall",e.TimerRemove="TimerRemove",e.TimerFire="TimerFire",e.XHRReadyStateChange="XHRReadyStateChange",e.XHRLoad="XHRLoad",e.CompileScript="v8.compile",e.CompileCode="V8.CompileCode",e.OptimizeCode="V8.OptimizeCode",e.EvaluateScript="EvaluateScript",e.CacheScript="v8.produceCache",e.CompileModule="v8.compileModule",e.EvaluateModule="v8.evaluateModule",e.CacheModule="v8.produceModuleCache",e.WasmStreamFromResponseCallback="v8.wasm.streamFromResponseCallback",e.WasmCompiledModule="v8.wasm.compiledModule",e.WasmCachedModule="v8.wasm.cachedModule",e.WasmModuleCacheHit="v8.wasm.moduleCacheHit",e.WasmModuleCacheInvalid="v8.wasm.moduleCacheInvalid",e.FrameStartedLoading="FrameStartedLoading",e.CommitLoad="CommitLoad",e.MarkLoad="MarkLoad",e.MarkDOMContent="MarkDOMContent",e.MarkFirstPaint="firstPaint",e.MarkFCP="firstContentfulPaint",e.MarkLCPCandidate="largestContentfulPaint::Candidate",e.MarkLCPInvalidate="largestContentfulPaint::Invalidate",e.NavigationStart="navigationStart",e.TimeStamp="TimeStamp",e.ConsoleTime="ConsoleTime",e.UserTiming="UserTiming",e.EventTiming="EventTiming",e.ResourceWillSendRequest="ResourceWillSendRequest",e.ResourceSendRequest="ResourceSendRequest",e.ResourceReceiveResponse="ResourceReceiveResponse",e.ResourceReceivedData="ResourceReceivedData",e.ResourceFinish="ResourceFinish",e.ResourceMarkAsCached="ResourceMarkAsCached",e.RunMicrotasks="RunMicrotasks",e.FunctionCall="FunctionCall",e.GCEvent="GCEvent",e.MajorGC="MajorGC",e.MinorGC="MinorGC",e.JSFrame="JSFrame",e.JSSample="JSSample",e.JSIdleFrame="JSIdleFrame",e.JSIdleSample="JSIdleSample",e.JSSystemFrame="JSSystemFrame",e.JSSystemSample="JSSystemSample",e.JSRoot="JSRoot",e.V8Sample="V8Sample",e.JitCodeAdded="JitCodeAdded",e.JitCodeMoved="JitCodeMoved",e.StreamingCompileScript="v8.parseOnBackground",e.StreamingCompileScriptWaiting="v8.parseOnBackgroundWaiting",e.StreamingCompileScriptParsing="v8.parseOnBackgroundParsing",e.BackgroundDeserialize="v8.deserializeOnBackground",e.FinalizeDeserialization="V8.FinalizeDeserialization",e.V8Execute="V8.Execute",e.UpdateCounters="UpdateCounters",e.RequestAnimationFrame="RequestAnimationFrame",e.CancelAnimationFrame="CancelAnimationFrame",e.FireAnimationFrame="FireAnimationFrame",e.RequestIdleCallback="RequestIdleCallback",e.CancelIdleCallback="CancelIdleCallback",e.FireIdleCallback="FireIdleCallback",e.WebSocketCreate="WebSocketCreate",e.WebSocketSendHandshakeRequest="WebSocketSendHandshakeRequest",e.WebSocketReceiveHandshakeResponse="WebSocketReceiveHandshakeResponse",e.WebSocketDestroy="WebSocketDestroy",e.EmbedderCallback="EmbedderCallback",e.SetLayerTreeId="SetLayerTreeId",e.TracingStartedInPage="TracingStartedInPage",e.TracingSessionIdForWorker="TracingSessionIdForWorker",e.StartProfiling="CpuProfiler::StartProfiling",e.DecodeImage="Decode Image",e.ResizeImage="Resize Image",e.DrawLazyPixelRef="Draw LazyPixelRef",e.DecodeLazyPixelRef="Decode LazyPixelRef",e.LazyPixelRef="LazyPixelRef",e.LayerTreeHostImplSnapshot="cc::LayerTreeHostImpl",e.PictureSnapshot="cc::Picture",e.DisplayItemListSnapshot="cc::DisplayItemList",e.InputLatencyMouseMove="InputLatency::MouseMove",e.InputLatencyMouseWheel="InputLatency::MouseWheel",e.ImplSideFling="InputHandlerProxy::HandleGestureFling::started",e.GCCollectGarbage="BlinkGC.AtomicPhase",e.CryptoDoEncrypt="DoEncrypt",e.CryptoDoEncryptReply="DoEncryptReply",e.CryptoDoDecrypt="DoDecrypt",e.CryptoDoDecryptReply="DoDecryptReply",e.CryptoDoDigest="DoDigest",e.CryptoDoDigestReply="DoDigestReply",e.CryptoDoSign="DoSign",e.CryptoDoSignReply="DoSignReply",e.CryptoDoVerify="DoVerify",e.CryptoDoVerifyReply="DoVerifyReply",e.CpuProfile="CpuProfile",e.Profile="Profile",e.AsyncTask="AsyncTask"}(d||(d={})),function(e){e.Category={Console:"blink.console",UserTiming:"blink.user_timing",Loading:"loading"},e.WorkerThreadName="DedicatedWorker thread",e.WorkerThreadNameLegacy="DedicatedWorker Thread",e.RendererMainThreadName="CrRendererMain",e.BrowserMainThreadName="CrBrowserMain",e.UtilityMainThreadNameSuffix="CrUtilityMain",e.DevToolsMetadataEvent={TracingStartedInBrowser:"TracingStartedInBrowser",TracingStartedInPage:"TracingStartedInPage",TracingSessionIdForWorker:"TracingSessionIdForWorker",FrameCommittedInBrowser:"FrameCommittedInBrowser",ProcessReadyInBrowser:"ProcessReadyInBrowser",FrameDeletedInBrowser:"FrameDeletedInBrowser"},e.Thresholds={LongTask:50,Handler:150,RecurringHandler:50,ForcedLayout:30,IdleCallbackAddon:5}}(l||(l={}));class h{name;type;forMainFrame;url;events;asyncEvents;tasks;eventsForTreeViewInternal;thread;constructor(){this.name="",this.type=c.Other,this.forMainFrame=!1,this.url=n.DevToolsPath.EmptyUrlString,this.events=[],this.asyncEvents=[],this.tasks=[],this.eventsForTreeViewInternal=null,this.thread=null}eventsForTreeView(){if(this.eventsForTreeViewInternal)return this.eventsForTreeViewInternal;const t=[];function r(){const e=t[t.length-1];if(void 0!==e){const t=e.endTime;if(void 0!==t)return t}throw new Error("End time does not exist on event.")}this.eventsForTreeViewInternal=[...this.events];for(const n of this.asyncEvents){const s=n.startTime;let a=n.endTime;for(void 0===a&&(a=s);t.length&&s>=r();)t.pop();if(t.length&&a>r()){this.eventsForTreeViewInternal=[...this.events];break}const i=new e.Legacy.ConstructedEvent(n.categoriesString,n.name,"X",s,n.thread);i.setEndTime(a),i.addArgs(n.args),this.eventsForTreeViewInternal.push(i),t.push(i)}return this.eventsForTreeViewInternal}}!function(e){e.MainThread="MainThread",e.Worker="Worker",e.Animation="Animation",e.Raster="Raster",e.Experience="Experience",e.Other="Other"}(c||(c={}));class m{frameId;url;name;children;parent;processes;deletedTime;ownerNode;constructor(e){this.frameId=e.frame,this.url=e.url||n.DevToolsPath.EmptyUrlString,this.name=e.name,this.children=[],this.parent=null,this.processes=[],this.deletedTime=null,this.ownerNode=null}update(e,t){this.url=t.url||"",this.name=t.name,t.processId?this.processes.push({time:e,processId:t.processId,processPseudoId:"",url:t.url||""}):this.processes.push({time:e,processId:-1,processPseudoId:t.processPseudoId,url:t.url||""})}processReady(e,t){for(const r of this.processes)r.processPseudoId===e&&(r.processPseudoId="",r.processId=t)}addChild(e){this.children.push(e),e.parent=this}}class u{url;backendNodeIds;stackTrace;frameId;constructor(){this.url=null,this.backendNodeIds=[],this.stackTrace=null,this.frameId=null}topFrame(){return this.stackTrace&&this.stackTrace[0]||null}static forEvent(t){return t instanceof e.Legacy.PayloadEvent?u.forTraceEventData(t.rawPayload()):t instanceof e.Legacy.Event?p(t):u.forTraceEventData(t)}static forTraceEventData(e){return p(e)}static reset(){g=new Map}}function p(e){let t=g.get(e);return t||(t=new u,g.set(e,t)),t}let g=new Map;var I=Object.freeze({__proto__:null,get TimelineModelImpl(){return l},get RecordType(){return d},Track:h,get TrackType(){return c},PageFrame:m,EventOnTimelineData:u});class f{}class T extends f{visibleTypes;constructor(e){super(),this.visibleTypes=new Set(e)}accept(e){return this.visibleTypes.has(T.eventType(e))}static eventType(t){return e.Legacy.eventHasCategory(t,l.Category.Console)?d.ConsoleTime:e.Legacy.eventHasCategory(t,l.Category.UserTiming)?d.UserTiming:e.Legacy.eventIsFromNewEngine(t)&&e.Types.TraceEvents.isProfileCall(t)?d.JSFrame:t.name}}var v=Object.freeze({__proto__:null,TimelineModelFilter:f,TimelineVisibleEventsFilter:T,TimelineInvisibleEventsFilter:class extends f{invisibleTypes;constructor(e){super(),this.invisibleTypes=new Set(e)}accept(e){return!this.invisibleTypes.has(T.eventType(e))}},ExclusiveNameFilter:class extends f{excludeNames;constructor(e){super(),this.excludeNames=new Set(e)}accept(e){return!this.excludeNames.has(e.name)}}});class y extends s.LayerTreeBase.LayerTreeBase{tileById;paintProfilerModel;constructor(e){super(e),this.tileById=new Map,this.paintProfilerModel=e&&e.model(s.PaintProfiler.PaintProfilerModel)}async setLayers(e,t,r){const n=new Set;if(e)this.extractNodeIdsToResolve(n,{},e);else if(t)for(let e=0;e<t.length;++e)this.extractNodeIdsToResolve(n,{},t[e]);await this.resolveBackendNodeIds(n);const s=this.layersById;if(this.layersById=new Map,this.setContentRoot(null),e){const t=this.innerSetLayers(s,e);this.setRoot(t)}else if(t){const e=t.map(this.innerSetLayers.bind(this,s)),r=this.contentRoot();if(!r)throw new Error("Content root is not set.");this.setRoot(r);for(let t=0;t<e.length;++t)e[t].id()!==r.id()&&r.addChild(e[t])}this.setPaints(r)}setTiles(e){this.tileById=new Map;for(const t of e)this.tileById.set(t.id,t)}pictureForRasterTile(e){const r=this.tileById.get("cc::Tile/"+e);if(!r)return t.Console.Console.instance().error(`Tile ${e} is missing`),Promise.resolve(null);const n=this.layerById(r.layer_id);return n?n.pictureForRect(r.content_rect):(t.Console.Console.instance().error(`Layer ${r.layer_id} for tile ${e} is not found`),Promise.resolve(null))}setPaints(e){for(let t=0;t<e.length;++t){const r=this.layersById.get(e[t].layerId());r&&r.addPaintEvent(e[t])}}innerSetLayers(e,t){let r=e.get(t.layer_id);r?r.reset(t):r=new S(this.paintProfilerModel,t),this.layersById.set(t.layer_id,r),t.owner_node&&r.setNode(this.backendNodeIdToNode().get(t.owner_node)||null),!this.contentRoot()&&r.drawsContent()&&this.setContentRoot(r);for(let n=0;t.children&&n<t.children.length;++n)r.addChild(this.innerSetLayers(e,t.children[n]));return r}extractNodeIdsToResolve(e,t,r){const n=r.owner_node;n&&!this.backendNodeIdToNode().has(n)&&e.add(n);for(let n=0;r.children&&n<r.children.length;++n)this.extractNodeIdsToResolve(e,t,r.children[n])}}class S{parentLayerId;parentInternal;layerId;nodeInternal;offsetXInternal;offsetYInternal;widthInternal;heightInternal;childrenInternal;quadInternal;scrollRectsInternal;gpuMemoryUsageInternal;paints;compositingReasons;compositingReasonIds;drawsContentInternal;paintProfilerModel;constructor(e,t){this.parentLayerId=null,this.parentInternal=null,this.layerId="",this.nodeInternal=null,this.offsetXInternal=-1,this.offsetYInternal=-1,this.widthInternal=-1,this.heightInternal=-1,this.childrenInternal=[],this.quadInternal=[],this.scrollRectsInternal=[],this.gpuMemoryUsageInternal=-1,this.paints=[],this.compositingReasons=[],this.compositingReasonIds=[],this.drawsContentInternal=!1,this.paintProfilerModel=e,this.reset(t)}reset(e){this.nodeInternal=null,this.layerId=String(e.layer_id),this.offsetXInternal=e.position[0],this.offsetYInternal=e.position[1],this.widthInternal=e.bounds.width,this.heightInternal=e.bounds.height,this.childrenInternal=[],this.parentLayerId=null,this.parentInternal=null,this.quadInternal=e.layer_quad||[],this.createScrollRects(e),this.compositingReasons=e.compositing_reasons||[],this.compositingReasonIds=e.compositing_reason_ids||[],this.drawsContentInternal=Boolean(e.draws_content),this.gpuMemoryUsageInternal=e.gpu_memory_usage,this.paints=[]}id(){return this.layerId}parentId(){return this.parentLayerId}parent(){return this.parentInternal}isRoot(){return!this.parentId()}children(){return this.childrenInternal}addChild(e){const t=e;t.parentInternal&&console.assert(!1,"Child already has a parent"),this.childrenInternal.push(t),t.parentInternal=this,t.parentLayerId=this.layerId}setNode(e){this.nodeInternal=e}node(){return this.nodeInternal}nodeForSelfOrAncestor(){let e=this;for(;e;e=e.parent())if(e.node())return e.node();return null}offsetX(){return this.offsetXInternal}offsetY(){return this.offsetYInternal}width(){return this.widthInternal}height(){return this.heightInternal}transform(){return null}quad(){return this.quadInternal}anchorPoint(){return[.5,.5,0]}invisible(){return!1}paintCount(){return 0}lastPaintRect(){return null}scrollRects(){return this.scrollRectsInternal}stickyPositionConstraint(){return null}gpuMemoryUsage(){return this.gpuMemoryUsageInternal}snapshots(){return this.paints.map((async e=>{if(!this.paintProfilerModel)return null;const t=await async function(e,t){const r=t.picture();if(!r||!e)return null;const n=await e.loadSnapshot(r.serializedPicture);return n?{rect:r.rect,snapshot:n}:null}(this.paintProfilerModel,e);if(!t)return null;return{rect:{x:t.rect[0],y:t.rect[1],width:t.rect[2],height:t.rect[3]},snapshot:t.snapshot}}))}async pictureForRect(e){return Promise.all(this.paints.map((e=>e.picture()))).then((r=>{const n=r.filter((r=>{return r&&(n=r.rect,s=e,t(n[0],n[0]+n[2],s[0],s[0]+s[2])&&t(n[1],n[1]+n[3],s[1],s[1]+s[3]));var n,s})).map((e=>({x:e.rect[0],y:e.rect[1],picture:e.serializedPicture})));if(!n.length||!this.paintProfilerModel)return null;const s=n.reduce(((e,t)=>Math.min(e,t.x)),1/0),a=n.reduce(((e,t)=>Math.min(e,t.y)),1/0),i={x:e[0]-s,y:e[1]-a,width:e[2],height:e[3]};return this.paintProfilerModel.loadSnapshotFromFragments(n).then((e=>e?{rect:i,snapshot:e}:null))}));function t(e,t,r,n){return console.assert(e<=t&&r<=n,"segments should be specified as ordered pairs"),t>r&&e<n}}scrollRectsFromParams(e,t){return{rect:{x:e[0],y:e[1],width:e[2],height:e[3]},type:t}}createScrollRects(e){const t=[];e.non_fast_scrollable_region&&t.push(this.scrollRectsFromParams(e.non_fast_scrollable_region,"NonFastScrollable")),e.touch_event_handler_region&&t.push(this.scrollRectsFromParams(e.touch_event_handler_region,"TouchEventHandler")),e.wheel_event_handler_region&&t.push(this.scrollRectsFromParams(e.wheel_event_handler_region,"WheelEventHandler")),e.scroll_event_handler_region&&t.push(this.scrollRectsFromParams(e.scroll_event_handler_region,"RepaintsOnScroll")),this.scrollRectsInternal=t}addPaintEvent(e){this.paints.push(e)}requestCompositingReasons(){return Promise.resolve(this.compositingReasons)}requestCompositingReasonIds(){return Promise.resolve(this.compositingReasonIds)}drawsContent(){return this.drawsContentInternal}}var C=Object.freeze({__proto__:null,TracingLayerTree:y,TracingFrameLayerTree:class{#t;#r;#n=[];constructor(e,t){this.#t=e,this.#r=t.entry,this.#n=t.paints}async layerTreePromise(){const e=this.#r.args.snapshot,t=e.device_viewport_size,r=e.active_tiles,n=e.active_tree.root_layer,s=e.active_tree.layers,a=new y(this.#t);return a.setViewportSize(t),a.setTiles(r),await a.setLayers(n,s,this.#n||[]),a}paints(){return this.#n}},TracingLayer:S});const k={threadS:"Thread {PH1}"},F=r.i18n.registerUIStrings("models/timeline_model/TimelineJSProfile.ts",k),R=r.i18n.getLocalizedString.bind(void 0,F);class E{static isNativeRuntimeFrame(e){return"native V8Runtime"===e.url}static nativeGroup(e){return e.startsWith("Parse")?"Parse":e.startsWith("Compile")||e.startsWith("Recompile")?"Compile":null}static createFakeTraceFromCpuProfile(e,t,r,n){const s=[];return r&&a("TracingStartedInPage",{data:{sessionId:"1"}},0,0,"M"),n||(n=R(k.threadS,{PH1:t})),a("thread_name",{name:n},0,0,"M","__metadata"),e?(a(d.JSRoot,{},e.startTime,e.endTime-e.startTime,"X","toplevel"),a("CpuProfile",{data:{cpuProfile:e}},e.endTime,0,"I"),s):s;function a(e,r,n,a,i,o){const l={cat:o||"disabled-by-default-devtools.timeline",name:e,ph:i||"X",pid:1,tid:t,ts:n,args:r};return a&&(l.dur=a),s.push(l),l}}}var P=Object.freeze({__proto__:null,TimelineJSProfileProcessor:E});class M{totalTime;selfTime;id;event;parent;groupId;isGroupNodeInternal;depth;constructor(e,t){this.totalTime=0,this.selfTime=0,this.id=e,this.event=t,this.groupId="",this.isGroupNodeInternal=!1,this.depth=0}isGroupNode(){return this.isGroupNodeInternal}hasChildren(){throw"Not implemented"}setHasChildren(e){throw"Not implemented"}children(){throw"Not implemented"}searchTree(e,t){t=t||[],this.event&&e(this.event)&&t.push(this);for(const r of this.children().values())r.searchTree(e,t);return t}}class w extends M{root;hasChildrenInternal;childrenInternal;parent;constructor(e,t,r){super(e,t),this.root=r&&r.root,this.hasChildrenInternal=!1,this.childrenInternal=null,this.parent=r}hasChildren(){return this.hasChildrenInternal}setHasChildren(e){this.hasChildrenInternal=e}children(){return this.childrenInternal||this.buildChildren()}buildChildren(){const t=[];for(let e=this;e.parent&&!e.isGroupNode();e=e.parent)t.push(e);t.reverse();const r=new Map,n=this,s=this.root;if(!s)return this.childrenInternal=r,this.childrenInternal;const a=s.startTime,i=s.endTime,o=s.doNotAggregate?function(e){++h,m===t.length&&h<=t.length+2&&p(e,0);--h}:void 0,d=s.doNotAggregate?void 0:D,c=s.getEventGroupIdCallback();let h=0,m=0,u=null;function p(e,s){if(h===t.length+2){if(!u)return;return u.setHasChildren(!0),void(u.selfTime-=s)}let a,i="";d?(a=d(e),i=c?c(e):"",i&&(a+="/"+i)):a=Symbol("uniqueId");let o=r.get(a);o||(o=new w(a,e,n),o.groupId=i,r.set(a,o)),o.selfTime+=s,o.totalTime+=s,u=o}return l.forEachEvent(s.events,(function(r){const{startTime:n,endTime:s}=e.Legacy.timesForEventInMilliseconds(r);if(++h,h>t.length+2)return;if(!function(r){const{endTime:n}=e.Legacy.timesForEventInMilliseconds(r);if(m===t.length)return!0;if(m!==h-1)return!1;if(!n)return!1;if(!d)return r===t[m].event&&++m,!1;let s=d(r);const a=c?c(r):"";a&&(s+="/"+a);s===t[m].id&&++m;return!1}(r))return;const o=(void 0!==s?Math.min(s,i):i)-Math.max(a,n);o<0&&console.error("Negative event duration");p(r,o)}),(function(e){--h,m>h&&(m=h)}),o,a,i,s.filter,!1),this.childrenInternal=r,r}getRoot(){return this.root}}class L extends M{childrenInternal;isGroupNodeInternal;constructor(e,t,r){super(e,r),this.childrenInternal=new Map,this.parent=t,this.isGroupNodeInternal=!0}addChild(e,t,r){this.childrenInternal.set(e.id,e),this.selfTime+=t,this.totalTime+=r,e.parent=this}hasChildren(){return!0}children(){return this.childrenInternal}}class b extends M{parent;root;depth;cachedChildren;hasChildrenInternal;constructor(e,t,r,n,s){super(t,r),this.parent=s,this.root=e,this.depth=(s.depth||0)+1,this.cachedChildren=null,this.hasChildrenInternal=n}hasChildren(){return this.hasChildrenInternal}setHasChildren(e){this.hasChildrenInternal=e}children(){if(this.cachedChildren)return this.cachedChildren;const t=[0],r=[],n=[],s=new Map,a=this.root.startTime,i=this.root.endTime;let o=a;const d=this;return l.forEachEvent(this.root.events,(function(s){const{startTime:o,endTime:l}=e.Legacy.timesForEventInMilliseconds(s),d=(void 0!==l?Math.min(l,i):i)-Math.max(o,a);d<0&&console.assert(!1,"Negative duration of an event");t[t.length-1]-=d,t.push(d);const c=D(s);r.push(c),n.push(s)}),(function(a){const{startTime:l,endTime:c}=e.Legacy.timesForEventInMilliseconds(a),h=t.pop(),m=r.pop();let u;for(n.pop(),u=d;u.depth>1;u=u.parent)if(u.id!==r[r.length+1-u.depth])return;if(u.id!==m||r.length<d.depth)return;const p=r[r.length-d.depth];if(u=s.get(p),!u){const e=n[n.length-d.depth],t=n.length>d.depth;u=new b(d.root,p,e,t,d),s.set(p,u)}const g=void 0!==c?Math.min(c,i):i,I=g-Math.max(l,o);u.selfTime+=h||0,u.totalTime+=I,o=g}),void 0,a,i,this.root.filter,!1),this.cachedChildren=this.root.filterChildren(s),this.cachedChildren}searchTree(e,t){return t=t||[],this.event&&e(this.event)&&t.push(this),t}}function N(t){if(e.Types.TraceEvents.isProfileCall(t))return t.callFrame;const r=t.args?.data?.stackTrace?.[0];return r?{...r,scriptId:String(r.scriptId)}:null}function D(t){if(e.Legacy.eventIsFromNewEngine(t)&&e.Types.TraceEvents.isProfileCall(t)){return`f:${E.isNativeRuntimeFrame(t.callFrame)?E.nativeGroup(t.callFrame.functionName):t.callFrame.functionName}@${t.callFrame.scriptId||t.callFrame.url||""}`}return t.name===d.TimeStamp?`${t.name}:${t.args.data.message}`:t.name}var B=Object.freeze({__proto__:null,Node:M,TopDownNode:w,TopDownRootNode:class extends w{filter;events;startTime;endTime;eventGroupIdCallback;doNotAggregate;totalTime;selfTime;constructor(e,t,r,n,s,a){super("",null,null),this.root=this,this.events=e,this.filter=e=>t.every((t=>t.accept(e))),this.startTime=r,this.endTime=n,this.eventGroupIdCallback=a,this.doNotAggregate=s,this.totalTime=n-r,this.selfTime=this.totalTime}children(){return this.childrenInternal||this.grouppedTopNodes()}grouppedTopNodes(){const e=super.children();for(const t of e.values())this.selfTime-=t.totalTime;if(!this.eventGroupIdCallback)return e;const t=new Map;for(const r of e.values()){const e=this.eventGroupIdCallback(r.event);let n=t.get(e);n||(n=new L(e,this,r.event),t.set(e,n)),n.addChild(r,r.selfTime,r.totalTime)}return this.childrenInternal=t,t}getEventGroupIdCallback(){return this.eventGroupIdCallback}},BottomUpRootNode:class extends M{childrenInternal;events;textFilter;filter;startTime;endTime;eventGroupIdCallback;totalTime;constructor(e,t,r,n,s,a){super("",null),this.childrenInternal=null,this.events=e,this.textFilter=t,this.filter=e=>r.every((t=>t.accept(e))),this.startTime=n,this.endTime=s,this.eventGroupIdCallback=a,this.totalTime=s-n}hasChildren(){return!0}filterChildren(e){for(const[t,r]of e)r.event&&r.depth<=1&&!this.textFilter.accept(r.event)&&e.delete(t);return e}children(){return this.childrenInternal||(this.childrenInternal=this.filterChildren(this.grouppedTopNodes())),this.childrenInternal}ungrouppedTopNodes(){const t=this,r=this.startTime,n=this.endTime,s=new Map,a=[n-r],i=[],o=new Map;l.forEachEvent(this.events,(function(t){const{startTime:s,endTime:l}=e.Legacy.timesForEventInMilliseconds(t),d=(void 0!==l?Math.min(l,n):n)-Math.max(s,r);a[a.length-1]-=d,a.push(d);const c=D(t),h=!o.has(c);h&&o.set(c,d);i.push(h)}),(function(e){const r=D(e);let n=s.get(r);n||(n=new b(t,r,e,!1,t),s.set(r,n));n.selfTime+=a.pop()||0,i.pop()&&(n.totalTime+=o.get(r)||0,o.delete(r));i.length&&n.setHasChildren(!0)}),void 0,r,n,this.filter,!1),this.selfTime=a.pop()||0;for(const e of s)e[1].selfTime<=0&&s.delete(e[0]);return s}grouppedTopNodes(){const e=this.ungrouppedTopNodes();if(!this.eventGroupIdCallback)return e;const t=new Map;for(const r of e.values()){const e=this.eventGroupIdCallback(r.event);let n=t.get(e);n||(n=new L(e,this,r.event),t.set(e,n)),n.addChild(r,r.selfTime,r.selfTime)}return t}},GroupNode:L,BottomUpNode:b,eventURL:function(t){const r=t.args.data||t.args.beginData;if(r&&r.url)return r.url;if(!e.Legacy.eventIsFromNewEngine(t))return null;let n=N(t);for(;n;){const e=n.url;if(e)return e;n=n.parent}return null},eventStackFrame:N,generateEventID:D});export{P as TimelineJSProfile,I as TimelineModel,v as TimelineModelFilter,B as TimelineProfileTree,C as TracingLayerTree};
