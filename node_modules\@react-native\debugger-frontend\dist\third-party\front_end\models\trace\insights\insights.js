import"../handlers/handlers.js";import*as e from"../helpers/helpers.js";var n;!function(e){e.NO_FP="NO_FP"}(n||(n={}));var r=Object.freeze({__proto__:null,get InsightWarning(){return n}});var t=Object.freeze({__proto__:null,deps:function(){return["NetworkRequests","PageLoadMetrics"]},generateInsight:function(r,t){const a=r.PageLoadMetrics.metricScoresByFrameId.get(t.frameId)?.get(t.navigationId)?.get("FP")?.event?.ts;if(!a)return{renderBlockingRequests:[],warnings:[n.NO_FP]};const i=[];for(const n of r.NetworkRequests.byTime){if(n.args.data.frame!==t.frameId)continue;if("blocking"!==n.args.data.renderBlocking&&"in_body_parser_blocking"!==n.args.data.renderBlocking)continue;if(n.args.data.syntheticData.finishTime>a)continue;const s=e.Trace.getNavigationForTraceEvent(n,t.frameId,r.Meta.navigationsByFrameId);s?.args.data?.navigationId===t.navigationId&&i.push(n)}return{renderBlockingRequests:i}}}),a=Object.freeze({__proto__:null,RenderBlocking:t});export{a as InsightRunners,r as Types};
