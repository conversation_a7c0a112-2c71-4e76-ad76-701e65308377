import*as t from"../types/types.js";import*as e from"../../../core/platform/platform.js";import*as n from"../../../core/common/common.js";function i(t,e){const n=t.ts,i=e.ts;if(n<i)return-1;if(n>i)return 1;const r=n+(t.dur??0),o=i+(e.dur??0);return r>o?-1:r<o?1:0}function r(t,e){const n=[];let r=0,o=0;for(;r<t.length&&o<e.length;){const s=t[r],a=e[o],c=i(s,a);c<=0&&(n.push(s),r++),1===c&&(n.push(a),o++)}for(;r<t.length;)n.push(t[r++]);for(;o<e.length;)n.push(e[o++]);return n}function o(t,n,i){const r=i.get(n);if(!r||""===n)return null;const o=e.ArrayUtilities.nearestIndexFromEnd(r,(e=>e.ts<=t.ts));return null===o?null:r[o]}function s(t){return t.id??t.id2?.global??t.id2?.local}function a(e,n,i,r){return{cat:"",name:"ProfileCall",nodeId:e.id,args:{},ph:"X",pid:i,tid:r,ts:n,dur:t.Timing.MicroSeconds(0),selfTime:t.Timing.MicroSeconds(0),callFrame:e.callFrame}}function c(t){const n=new Map;for(const i of t){const t=l(i);if(void 0===t)continue;const r=e.MapUtilities.getWithDefault(n,t,(()=>({begin:null,end:null}))),o="b"===i.ph,s="e"===i.ph;o?r.begin=i:s&&(r.end=i)}return n}function l(t){const e=s(t);return e&&`${t.cat}:${e}:${t.name}`}function d(e){const n=[];for(const[r,o]of e.entries()){const e=o.begin,s=o.end;if(!e||!s)continue;const a={beginEvent:e,endEvent:s};if(i=a,!Boolean(l(i.beginEvent))||l(i.beginEvent)!==l(i.endEvent))continue;const c={cat:s.cat,ph:s.ph,pid:s.pid,tid:s.tid,id:r,name:e.name,dur:t.Timing.MicroSeconds(s.ts-e.ts),ts:e.ts,args:{data:a}};c.dur<0||n.push(c)}var i;return n.sort(((t,e)=>t.ts-e.ts))}var u=Object.freeze({__proto__:null,stackTraceForEvent:function(e){return t.TraceEvents.isSyntheticInvalidation(e)?e.stackTrace||null:e.args?.data?.stackTrace?e.args.data.stackTrace:t.TraceEvents.isTraceEventUpdateLayoutTree(e)&&e.args.beginData?.stackTrace||null},extractOriginFromTrace:function(t){const e=n.ParsedURL.ParsedURL.fromString(t);return e?e.host.startsWith("www.")?e.host.slice(4):e.host:null},addEventToProcessThread:function(t,e){const{tid:n,pid:i}=t;let r=e.get(i);r||(r=new Map);let o=r.get(n);o||(o=[]),o.push(t),r.set(t.tid,o),e.set(t.pid,r)},sortTraceEventsInPlace:function(t){t.sort(i)},mergeEventsInOrder:r,getNavigationForTraceEvent:o,extractId:s,activeURLForFrameAtTime:function(t,e,n){const i=n.get(t);if(!i)return null;for(const t of i.values())for(const n of t)if(!(n.window.min>e||n.window.max<e))return n.frame.url;return null},makeProfileCall:a,matchBeginningAndEndEvents:c,createSortedSyntheticEvents:d,createMatchedSortedSyntheticEvents:function(t){return d(c(t))}});const m=e=>t.Timing.MicroSeconds(1e3*e),f=e=>t.Timing.MilliSeconds(1e3*e),h=e=>t.Timing.MilliSeconds(e/1e3),p=e=>t.Timing.Seconds(e/1e3/1e3);function g(t){if(t<1e3)return 0;const e=t/1e3;if(e<1e3)return 1;return e/1e3<60?2:3}const S={style:"unit",unit:"millisecond",unitDisplay:"narrow"},T=t=>JSON.stringify(t),k=t=>new Intl.NumberFormat(void 0,t?JSON.parse(t):{}),v=new Map;function M(e){return{startTime:e.ts,endTime:t.Timing.MicroSeconds(e.ts+(e.dur||t.Timing.MicroSeconds(0))),duration:t.Timing.MicroSeconds(e.dur||0),selfTime:t.TraceEvents.isSyntheticTraceEntry(e)?t.Timing.MicroSeconds(e.selfTime||0):t.Timing.MicroSeconds(e.dur||0)}}e.MapUtilities.getWithDefault(v,T({style:"decimal"}),k),e.MapUtilities.getWithDefault(v,T(S),k),e.MapUtilities.getWithDefault(v,T({...S,unit:"second"}),k),e.MapUtilities.getWithDefault(v,T({...S,unit:"minute"}),k);var E=Object.freeze({__proto__:null,millisecondsToMicroseconds:m,secondsToMilliseconds:f,secondsToMicroseconds:t=>m(f(t)),microSecondsToMilliseconds:h,microSecondsToSeconds:p,detectBestTimeUnit:g,formatMicrosecondsTime:function(t,n={}){n.format||(n.format=g(t));const i=t/1e3,r=i/1e3,o={...S,...n};switch(n.format){case 0:return`${e.MapUtilities.getWithDefault(v,T({style:"decimal"}),k).format(t)}μs`;case 1:return e.MapUtilities.getWithDefault(v,T(o),k).format(i);case 2:return e.MapUtilities.getWithDefault(v,T({...o,unit:"second"}),k).format(r);default:{const t=e.MapUtilities.getWithDefault(v,T({...o,unit:"minute"}),k),n=e.MapUtilities.getWithDefault(v,T({...o,unit:"second"}),k),i=r/60,[s,a,c]=t.formatToParts(i);let l=0;return a&&c&&(l=Math.round(60*Number(`0.${c.value}`))),`${t.format(Number(s.value))} ${n.format(l)}`}}},timeStampForEventAdjustedByClosestNavigation:function(e,n,i,r){let s=e.ts-n.min;if(e.args?.data?.navigationId){const t=i.get(e.args.data.navigationId);t&&(s=e.ts-t.ts)}else if(e.args?.data?.frame){const t=o(e,e.args.data.frame,r);t&&(s=e.ts-t.ts)}return t.Timing.MicroSeconds(s)},eventTimingsMicroSeconds:M,eventTimingsMilliSeconds:function(t){const e=M(t);return{startTime:h(e.startTime),endTime:h(e.endTime),duration:h(e.duration),selfTime:h(e.selfTime)}},eventTimingsSeconds:function(t){const e=M(t);return{startTime:p(e.startTime),endTime:p(e.endTime),duration:p(e.duration),selfTime:p(e.selfTime)}},traceWindowMilliSeconds:function(t){return{min:h(t.min),max:h(t.max),range:h(t.range)}},traceWindowMillisecondsToMicroSeconds:function(t){return{min:m(t.min),max:m(t.max),range:m(t.range)}},traceWindowFromMilliSeconds:function(e,n){return{min:m(e),max:m(n),range:t.Timing.MicroSeconds(m(n)-m(e))}},traceWindowFromMicroSeconds:function(e,n){return{min:e,max:n,range:t.Timing.MicroSeconds(n-e)}}});class J{#t=[];#e=[];#n;#i;#r=[];#o=!1;#s;#a=new Map;#c;constructor(e,n,i,r){this.#s=e,this.#i=i,this.#n=n,this.#c=r||t.Configuration.DEFAULT}buildProfileCalls(e){const n=r(e,this.callsFromProfileSamples()),i=[];for(let e=0;e<n.length;e++){const r=n[e];if("I"===r.ph)continue;if(0===i.length){if(t.TraceEvents.isProfileCall(r)){this.#l(r);continue}i.push(r),this.#d(r);continue}const o=i.at(-1);if(void 0===o)continue;r.ts>=o.ts+(o.dur||0)?(this.#u(o),i.pop(),e--):t.TraceEvents.isProfileCall(r)?this.#l(r,o):(this.#d(r),i.push(r))}for(;i.length;){const t=i.pop();t&&this.#u(t)}return this.#t}#d(t){"RunMicrotasks"!==t.name&&"RunTask"!==t.name||(this.#r=[],this.#m(0,t.ts),this.#o=!1),this.#o&&(this.#m(this.#r.pop()||0,t.ts),this.#o=!1),this.#f(t),this.#r.push(this.#e.length)}#l(e,n){if(n&&t.TraceEvents.isJSInvocationEvent(n)||this.#o)this.#f(e);else if(t.TraceEvents.isProfileCall(e)&&0===this.#e.length){this.#o=!0;const t=this.#e.length;this.#f(e),this.#r.push(t)}}#u(e){const n=t.Timing.MicroSeconds(e.ts+(e.dur||0));this.#m(this.#r.pop()||0,n)}callsFromProfileSamples(){const e=this.#s.samples,n=this.#s.timestamps;if(!e)return[];const i=[];let r;for(let o=0;o<e.length;o++){const e=this.#s.nodeByIndex(o),s=m(t.Timing.MilliSeconds(n[o]));if(!e)continue;const c=a(e,s,this.#n,this.#i);i.push(c),e.id===this.#s.gcNode?.id&&r?this.#a.set(c,r):r=e}return i}#h(t){let e=this.#s.nodeById(t.nodeId);const n=e?.id===this.#s.gcNode?.id;if(n&&(e=this.#a.get(t)||null),!e)return[];const i=new Array(e.depth+1+Number(n));let r=i.length-1;for(n&&(i[r--]=t);e;)i[r--]=a(e,t.ts,this.#n,this.#i),e=e.parent;return i}#f(e){const n=t.TraceEvents.isProfileCall(e)?this.#h(e):this.#e;J.filterStackFrames(n,this.#c);const i=e.ts+(e.dur||0),r=Math.min(n.length,this.#e.length);let o;for(o=this.#r.at(-1)||0;o<r;++o){const e=n[o].callFrame,r=this.#e[o].callFrame;if(!J.framesAreEqual(e,r))break;this.#e[o].dur=t.Timing.MicroSeconds(Math.max(this.#e[o].dur||0,i-this.#e[o].ts))}for(this.#m(o,e.ts);o<n.length;++o){const t=n[o];t.nodeId!==this.#s.programNode?.id&&t.nodeId!==this.#s.root?.id&&t.nodeId!==this.#s.idleNode?.id&&t.nodeId!==this.#s.gcNode?.id&&(this.#e.push(t),this.#t.push(t))}}#m(e,n){if(this.#r.length){const t=this.#r.at(-1);t&&e<t&&(console.error(`Child stack is shallower (${e}) than the parent stack (${t}) at ${n}`),e=t)}this.#e.length<e&&(console.error(`Trying to truncate higher than the current stack size at ${n}`),e=this.#e.length);for(let e=0;e<this.#e.length;++e)this.#e[e].dur=t.Timing.MicroSeconds(Math.max(n-this.#e[e].ts,0));this.#e.length=e}static framesAreEqual(t,e){return t.scriptId===e.scriptId&&t.functionName===e.functionName&&t.lineNumber===e.lineNumber}static showNativeName(t,e){return e&&Boolean(J.nativeGroup(t))}static nativeGroup(t){return t.startsWith("Parse")?"Parse":t.startsWith("Compile")||t.startsWith("Recompile")?"Compile":null}static isNativeRuntimeFrame(t){return"native V8Runtime"===t.url}static filterStackFrames(t,e){if(e.experiments.timelineShowAllEvents)return;let n=null,i=0;for(let r=0;r<t.length;++r){const o=t[r].callFrame,s=J.isNativeRuntimeFrame(o);if(s&&!J.showNativeName(o.functionName,e.experiments.timelineV8RuntimeCallStats))continue;const a=s?J.nativeGroup(o.functionName):null;n&&n===a||(n=a,t[i++]=t[r])}t.length=i}}var I=Object.freeze({__proto__:null,SamplesIntegrator:J});let y=0;const w=()=>++y,x=()=>({roots:new Set,maxDepth:0}),F=(t,e)=>({entry:t,id:e,parent:null,children:[],depth:0});function N(e,n,i,r,o,s){if(!o||function(t,e){const n=t.entry.ts,i=t.entry.ts+(t.entry.dur||0);if(n>=e.min&&n<e.max)return!0;if(i>e.min&&i<=e.max)return!0;if(n<=e.min&&i>=e.max)return!0;return!1}(n,o)){if(void 0!==s){if(t.Timing.MicroSeconds(n.entry.ts+t.Timing.MicroSeconds(n.entry.dur||0))<s)return}i(n.entry);for(const t of n.children)N(e,t,i,r,o,s);r(n.entry)}}var C=Object.freeze({__proto__:null,makeTraceEntryNodeId:w,makeEmptyTraceEntryTree:x,makeEmptyTraceEntryNode:F,treify:function(e,n){const i=new Map,r=[];y=-1;const o=x();for(let s=0;s<e.length;s++){const a=e[s];if(n&&!n.filter.has(a.name))continue;const c=a.dur||0,l=w(),d=F(a,l);if(0===r.length){o.roots.add(d),a.selfTime=t.Timing.MicroSeconds(c),r.push(d),o.maxDepth=Math.max(o.maxDepth,r.length),i.set(a,d);continue}const u=r.at(-1);if(void 0===u)throw new Error("Impossible: no parent node found in the stack");const m=u.entry,f=a.ts,h=m.ts,p=f+c,g=h+(m.dur||0);if(f<h)throw new Error("Impossible: current event starts before the parent event");if(f>=g){r.pop(),s--,y--;continue}p>g||(d.depth=r.length,d.parent=u,u.children.push(d),a.selfTime=t.Timing.MicroSeconds(c),void 0!==m.selfTime&&(m.selfTime=t.Timing.MicroSeconds(m.selfTime-(a.dur||0))),r.push(d),o.maxDepth=Math.max(o.maxDepth,r.length),i.set(a,d))}return{tree:o,entryToNode:i}},walkTreeFromEntry:function(t,e,n,i){const r=t.get(e);r&&N(t,r,n,i)},walkEntireTree:function(t,e,n,i,r,o){for(const s of e.roots)N(t,s,n,i,r,o)}});export{I as SamplesIntegrator,E as Timing,u as Trace,C as TreeHelpers};
