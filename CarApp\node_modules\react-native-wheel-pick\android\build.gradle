buildscript {
    repositories {
        maven { url "https://jitpack.io" }
    }
}

apply plugin: 'com.android.library'

def isNewArchitectureEnabled() {
    return project.hasProperty("newArchEnabled") && project.newArchEnabled == "true"
}

if (isNewArchitectureEnabled()) {
    apply plugin: 'com.facebook.react'
}

android {
    compileSdkVersion 33
    namespace 'com.tron'

    defaultConfig {
        buildConfigField("boolean", "IS_NEW_ARCHITECTURE_ENABLED", isNewArchitectureEnabled().toString())
    }

    buildFeatures {
        buildConfig true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "com.github.AigeStudio:WheelPicker:5913fa15fc"
    implementation 'com.facebook.react:react-native:+'
}
