import*as e from"../platform/platform.js";const t=new URLSearchParams(location.search);let n,s="";class r{constructor(){}static instance(e={forceNew:null}){const{forceNew:t}=e;return n&&!t||(n=new r),n}static removeInstance(){n=void 0}static queryParam(e){return t.get(e)}static setQueryParamForTesting(e,n){t.set(e,n)}static experimentsSetting(){try{return e.StringUtilities.toKebabCaseKeys(JSON.parse(self.localStorage&&self.localStorage.experiments?self.localStorage.experiments:"{}"))}catch(e){return console.error("Failed to parse localStorage['experiments']"),{}}}static setPlatform(e){s=e}static platform(){return s}static isDescriptorEnabled(e){const{experiment:t}=e;if("*"===t)return!0;if(t&&t.startsWith("!")&&o.isEnabled(t.substring(1)))return!1;if(t&&!t.startsWith("!")&&!o.isEnabled(t))return!1;const{condition:n}=e;return!n||n()}loadLegacyModule(e){return import(`../../${e}`)}}class i{#e;#t;#n;#s;#r;constructor(){this.#e=[],this.#t=new Set,this.#n=new Set,this.#s=new Set,this.#r=new Set}allConfigurableExperiments(){const e=[];for(const t of this.#e)this.#n.has(t.name)||e.push(t);return e}setExperimentsSetting(e){self.localStorage&&(self.localStorage.experiments=JSON.stringify(e))}register(t,n,s,r,i){if(this.#t.has(t))throw new Error(`Duplicate registraction of experiment '${t}'`);this.#t.add(t),this.#e.push(new a(this,t,n,Boolean(s),r??e.DevToolsPath.EmptyUrlString,i??e.DevToolsPath.EmptyUrlString))}isEnabled(e){return this.checkExperiment(e),!1!==r.experimentsSetting()[e]&&(!(!this.#n.has(e)&&!this.#s.has(e))||(!!this.#r.has(e)||Boolean(r.experimentsSetting()[e])))}setEnabled(e,t){this.checkExperiment(e);const n=r.experimentsSetting();n[e]=t,this.setExperimentsSetting(n)}enableExperimentsTransiently(e){for(const t of e)this.checkExperiment(t),this.#n.add(t)}enableExperimentsByDefault(e){for(const t of e)this.checkExperiment(t),this.#s.add(t)}setServerEnabledExperiments(e){for(const t of e)this.checkExperiment(t),this.#r.add(t)}enableForTest(e){this.checkExperiment(e),this.#n.add(e)}disableForTest(e){this.checkExperiment(e),this.#n.delete(e)}clearForTest(){this.#e=[],this.#t.clear(),this.#n.clear(),this.#s.clear(),this.#r.clear()}cleanUpStaleExperiments(){const e=r.experimentsSetting(),t={};for(const{name:n}of this.#e)if(e.hasOwnProperty(n)){const s=e[n];(s||this.#s.has(n))&&(t[n]=s)}this.setExperimentsSetting(t)}checkExperiment(e){if(!this.#t.has(e))throw new Error(`Unknown experiment '${e}'`)}}class a{name;title;unstable;docLink;feedbackLink;#e;constructor(e,t,n,s,r,i){this.name=t,this.title=n,this.unstable=s,this.docLink=r,this.feedbackLink=i,this.#e=e}isEnabled(){return this.#e.isEnabled(this.name)}setEnabled(e){this.#e.setEnabled(this.name,e)}}const o=new i;var l,c;!function(e){e.REACT_NATIVE_SPECIFIC_UI="react-native-specific-ui",e.JS_HEAP_PROFILER_ENABLE="js-heap-profiler-enable",e.ENABLE_PERFORMANCE_PANEL="enable-performance-panel"}(l||(l={})),function(e){e.CAN_DOCK="can_dock",e.NOT_SOURCES_HIDE_ADD_FOLDER="!sources.hide_add_folder",e.REACT_NATIVE_UNSTABLE_NETWORK_PANEL="unstable_enableNetworkPanel"}(c||(c={}));const m={canDock:()=>Boolean(r.queryParam("can_dock")),notSourcesHideAddFolder:()=>Boolean(r.queryParam(c.NOT_SOURCES_HIDE_ADD_FOLDER)),reactNativeUnstableNetworkPanel:()=>Boolean(r.queryParam(c.REACT_NATIVE_UNSTABLE_NETWORK_PANEL))};var h=Object.freeze({__proto__:null,getRemoteBase:function(e=self.location.toString()){const t=new URL(e).searchParams.get("remoteBase");if(!t)return null;const n=/\/serve_file\/(@[0-9a-zA-Z]+)\/?$/.exec(t);return n?{base:`devtools://devtools/remote/serve_file/${n[1]}/`,version:n[1]}:null},Runtime:r,ExperimentsSupport:i,Experiment:a,experiments:o,get RNExperimentName(){return l},get ConditionName(){return c},conditions:m});export{h as Runtime};
