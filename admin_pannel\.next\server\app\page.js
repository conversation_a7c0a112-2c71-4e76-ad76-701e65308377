/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5CclientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5CclientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/clientLayout.tsx */ \"(ssr)/./app/clientLayout.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1BST0RVQ1RJT04lNUMlNUNQUk9KRUNUXzAxJTVDJTVDYWRtaW5fcGFubmVsJTVDJTVDYXBwJTVDJTVDY2xpZW50TGF5b3V0LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDUFJPRFVDVElPTiU1QyU1Q1BST0pFQ1RfMDElNUMlNUNhZG1pbl9wYW5uZWwlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZm9udCU1QyU1Q2dvb2dsZSU1QyU1Q3RhcmdldC5jc3MlM0YlN0IlNUMlMjJwYXRoJTVDJTIyJTNBJTVDJTIyYXBwJTVDJTVDJTVDJTVDbGF5b3V0LnRzeCU1QyUyMiUyQyU1QyUyMmltcG9ydCU1QyUyMiUzQSU1QyUyMkludGVyJTVDJTIyJTJDJTVDJTIyYXJndW1lbnRzJTVDJTIyJTNBJTVCJTdCJTVDJTIyc3Vic2V0cyU1QyUyMiUzQSU1QiU1QyUyMmxhdGluJTVDJTIyJTVEJTdEJTVEJTJDJTVDJTIydmFyaWFibGVOYW1lJTVDJTIyJTNBJTVDJTIyaW50ZXIlNUMlMjIlN0QlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1Byb2plY3RzJTVDJTVDUFJPRFVDVElPTiU1QyU1Q1BST0pFQ1RfMDElNUMlNUNhZG1pbl9wYW5uZWwlNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsd0pBQTJJIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWRtaW5fcGFubmVsLz8wYmYxIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkM6XFxcXFByb2plY3RzXFxcXFBST0RVQ1RJT05cXFxcUFJPSkVDVF8wMVxcXFxhZG1pbl9wYW5uZWxcXFxcYXBwXFxcXGNsaWVudExheW91dC50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5CclientLayout.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!***********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \***********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(ssr)/./app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNQcm9qZWN0cyU1QyU1Q1BST0RVQ1RJT04lNUMlNUNQUk9KRUNUXzAxJTVDJTVDYWRtaW5fcGFubmVsJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLHdJQUFzRyIsInNvdXJjZXMiOlsid2VicGFjazovL2FkbWluX3Bhbm5lbC8/YWFkMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFByb2plY3RzXFxcXFBST0RVQ1RJT05cXFxcUFJPSkVDVF8wMVxcXFxhZG1pbl9wYW5uZWxcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CProjects%5C%5CPRODUCTION%5C%5CPROJECT_01%5C%5Cadmin_pannel%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/clientLayout.tsx":
/*!******************************!*\
  !*** ./app/clientLayout.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ClientLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _context_authContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./context/authContext */ \"(ssr)/./app/context/authContext.tsx\");\n/* harmony import */ var _context_loadingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./context/loadingContext */ \"(ssr)/./app/context/loadingContext.tsx\");\n/* harmony import */ var _components_navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/navbar */ \"(ssr)/./app/components/navbar.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction ClientLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_authContext__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_context_loadingContext__WEBPACK_IMPORTED_MODULE_3__.LoadingProvider, {\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"min-h-screen bg-gray-50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_navbar__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\clientLayout.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"container mx-auto\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\clientLayout.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\clientLayout.tsx\",\n                lineNumber: 15,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\clientLayout.tsx\",\n            lineNumber: 14,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\clientLayout.tsx\",\n        lineNumber: 13,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY2xpZW50TGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFDMEI7QUFDMkI7QUFDTTtBQUNsQjtBQUUxQixTQUFTSSxhQUFhLEVBQ25DQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0osOERBQVlBO2tCQUNYLDRFQUFDQyxvRUFBZUE7c0JBQ2QsNEVBQUNJO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0osMERBQU1BOzs7OztrQ0FDUCw4REFBQ0s7d0JBQUtELFdBQVU7a0NBQXFCRjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUsvQyIsInNvdXJjZXMiOlsid2VicGFjazovL2FkbWluX3Bhbm5lbC8uL2FwcC9jbGllbnRMYXlvdXQudHN4PzQ0Y2MiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcclxuaW1wb3J0IHsgQXV0aFByb3ZpZGVyIH0gZnJvbSBcIi4vY29udGV4dC9hdXRoQ29udGV4dFwiO1xyXG5pbXBvcnQgeyBMb2FkaW5nUHJvdmlkZXIgfSBmcm9tIFwiLi9jb250ZXh0L2xvYWRpbmdDb250ZXh0XCI7XHJcbmltcG9ydCBOYXZiYXIgZnJvbSBcIi4vY29tcG9uZW50cy9uYXZiYXJcIjtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIENsaWVudExheW91dCh7XHJcbiAgY2hpbGRyZW4sXHJcbn06IHtcclxuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xyXG59KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxBdXRoUHJvdmlkZXI+XHJcbiAgICAgIDxMb2FkaW5nUHJvdmlkZXI+XHJcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC1zY3JlZW4gYmctZ3JheS01MFwiPlxyXG4gICAgICAgICAgPE5hdmJhciAvPlxyXG4gICAgICAgICAgPG1haW4gY2xhc3NOYW1lPVwiY29udGFpbmVyIG14LWF1dG9cIj57Y2hpbGRyZW59PC9tYWluPlxyXG4gICAgICAgIDwvZGl2PlxyXG4gICAgICA8L0xvYWRpbmdQcm92aWRlcj5cclxuICAgIDwvQXV0aFByb3ZpZGVyPlxyXG4gICk7XHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiQXV0aFByb3ZpZGVyIiwiTG9hZGluZ1Byb3ZpZGVyIiwiTmF2YmFyIiwiQ2xpZW50TGF5b3V0IiwiY2hpbGRyZW4iLCJkaXYiLCJjbGFzc05hbWUiLCJtYWluIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/clientLayout.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/LoadingOverlay.tsx":
/*!*******************************************!*\
  !*** ./app/components/LoadingOverlay.tsx ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoadingOverlay)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CircularProgress!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CircularProgress/CircularProgress.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\nfunction LoadingOverlay({ isLoading, message = \"Loading...\" }) {\n    if (!isLoading) return null;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 z-50 flex items-center justify-center\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute inset-0 bg-black/30 backdrop-blur-sm\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\LoadingOverlay.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative z-10 bg-white p-6 rounded-lg shadow-xl flex flex-col items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CircularProgress_mui_material__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\LoadingOverlay.tsx\",\n                        lineNumber: 20,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-700\",\n                        children: message\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\LoadingOverlay.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\LoadingOverlay.tsx\",\n                lineNumber: 19,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\LoadingOverlay.tsx\",\n        lineNumber: 17,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9Mb2FkaW5nT3ZlcmxheS50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUMwQjtBQUN1QjtBQU9sQyxTQUFTRSxlQUFlLEVBQ3JDQyxTQUFTLEVBQ1RDLFVBQVUsWUFBWSxFQUNGO0lBQ3BCLElBQUksQ0FBQ0QsV0FBVyxPQUFPO0lBRXZCLHFCQUNFLDhEQUFDRTtRQUFJQyxXQUFVOzswQkFDYiw4REFBQ0Q7Z0JBQUlDLFdBQVU7Ozs7OzswQkFDZiw4REFBQ0Q7Z0JBQUlDLFdBQVU7O2tDQUNiLDhEQUFDTCw0RkFBZ0JBOzs7OztrQ0FDakIsOERBQUNNO3dCQUFFRCxXQUFVO2tDQUFpQkY7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUl0QyIsInNvdXJjZXMiOlsid2VicGFjazovL2FkbWluX3Bhbm5lbC8uL2FwcC9jb21wb25lbnRzL0xvYWRpbmdPdmVybGF5LnRzeD8wZDQxIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xyXG5pbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XHJcbmltcG9ydCB7IENpcmN1bGFyUHJvZ3Jlc3MgfSBmcm9tIFwiQG11aS9tYXRlcmlhbFwiO1xyXG5cclxuaW50ZXJmYWNlIExvYWRpbmdPdmVybGF5UHJvcHMge1xyXG4gIGlzTG9hZGluZzogYm9vbGVhbjtcclxuICBtZXNzYWdlPzogc3RyaW5nO1xyXG59XHJcblxyXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBMb2FkaW5nT3ZlcmxheSh7XHJcbiAgaXNMb2FkaW5nLFxyXG4gIG1lc3NhZ2UgPSBcIkxvYWRpbmcuLi5cIixcclxufTogTG9hZGluZ092ZXJsYXlQcm9wcykge1xyXG4gIGlmICghaXNMb2FkaW5nKSByZXR1cm4gbnVsbDtcclxuXHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZml4ZWQgaW5zZXQtMCB6LTUwIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyXCI+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgaW5zZXQtMCBiZy1ibGFjay8zMCBiYWNrZHJvcC1ibHVyLXNtXCI+PC9kaXY+XHJcbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMCBiZy13aGl0ZSBwLTYgcm91bmRlZC1sZyBzaGFkb3cteGwgZmxleCBmbGV4LWNvbCBpdGVtcy1jZW50ZXIgZ2FwLTRcIj5cclxuICAgICAgICA8Q2lyY3VsYXJQcm9ncmVzcyAvPlxyXG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS03MDBcIj57bWVzc2FnZX08L3A+XHJcbiAgICAgIDwvZGl2PlxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDaXJjdWxhclByb2dyZXNzIiwiTG9hZGluZ092ZXJsYXkiLCJpc0xvYWRpbmciLCJtZXNzYWdlIiwiZGl2IiwiY2xhc3NOYW1lIiwicCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./app/components/LoadingOverlay.tsx\n");

/***/ }),

/***/ "(ssr)/./app/components/navbar.tsx":
/*!***********************************!*\
  !*** ./app/components/navbar.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Navbar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Button,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/AppBar/AppBar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Button,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Toolbar/Toolbar.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Button,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Button,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Box/Box.js\");\n/* harmony import */ var _barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AppBar,Box,Button,Toolbar,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Dashboard,DirectionsCar,Forum,Logout,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Dashboard.js\");\n/* harmony import */ var _barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Dashboard,DirectionsCar,Forum,Logout,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/DirectionsCar.js\");\n/* harmony import */ var _barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Dashboard,DirectionsCar,Forum,Logout,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Dashboard,DirectionsCar,Forum,Logout,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Forum.js\");\n/* harmony import */ var _barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Dashboard,DirectionsCar,Forum,Logout,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Logout.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_authContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../context/authContext */ \"(ssr)/./app/context/authContext.tsx\");\n/* harmony import */ var _context_loadingContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../context/loadingContext */ \"(ssr)/./app/context/loadingContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Navbar() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { isAuthenticated, logout } = (0,_context_authContext__WEBPACK_IMPORTED_MODULE_3__.useAuth)();\n    const { showLoading } = (0,_context_loadingContext__WEBPACK_IMPORTED_MODULE_4__.useLoading)();\n    const navItems = [\n        {\n            text: \"Dashboard\",\n            path: \"/\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                lineNumber: 22,\n                columnNumber: 43\n            }, this)\n        },\n        {\n            text: \"Cars\",\n            path: \"/cars\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                lineNumber: 23,\n                columnNumber: 42\n            }, this)\n        },\n        {\n            text: \"Users\",\n            path: \"/users\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                lineNumber: 24,\n                columnNumber: 44\n            }, this)\n        },\n        {\n            text: \"Chats\",\n            path: \"/chats\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                lineNumber: 25,\n                columnNumber: 44\n            }, this)\n        }\n    ];\n    const handleNavigation = (path)=>{\n        showLoading(`Loading ${path === \"/\" ? \"dashboard\" : path.slice(1)}...`);\n        router.push(path);\n    };\n    const handleLogout = ()=>{\n        showLoading(\"Logging out...\");\n        logout();\n    };\n    if (!isAuthenticated || pathname === \"/login\") {\n        return null;\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n        position: \"static\",\n        color: \"default\",\n        elevation: 1,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                    variant: \"h6\",\n                    component: \"div\",\n                    sx: {\n                        flexGrow: 0,\n                        mr: 4\n                    },\n                    children: \"Cars Hub Admin\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                    sx: {\n                        flexGrow: 1,\n                        display: \"flex\",\n                        gap: 2\n                    },\n                    children: navItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                            startIcon: item.icon,\n                            onClick: ()=>handleNavigation(item.path),\n                            variant: pathname === item.path ? \"contained\" : \"text\",\n                            color: pathname === item.path ? \"primary\" : \"inherit\",\n                            children: item.text\n                        }, item.path, false, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AppBar_Box_Button_Toolbar_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                    startIcon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Dashboard_DirectionsCar_Forum_Logout_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 22\n                    }, void 0),\n                    onClick: handleLogout,\n                    color: \"inherit\",\n                    children: \"Logout\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\components\\\\navbar.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/components/navbar.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/authContext.tsx":
/*!*************************************!*\
  !*** ./app/context/authContext.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var js_cookie__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! js-cookie */ \"(ssr)/./node_modules/js-cookie/dist/js.cookie.mjs\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth auto */ \n\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [isAuthenticated, setIsAuthenticated] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if there's an active session on mount\n        const authToken = js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].get(\"adminAuthToken\");\n        if (authToken) {\n            setIsAuthenticated(true);\n        } else if (window.location.pathname !== \"/login\") {\n            router.push(\"/login\");\n        }\n    }, []);\n    const login = async (username, password)=>{\n        // For demo purposes, we'll use hardcoded credentials\n        // In production, this should make an API call to validate credentials\n        if (username === \"admin\" && password === \"admin123\") {\n            // Set cookie with 1 day expiration\n            js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].set(\"adminAuthToken\", \"demo-token\", {\n                expires: 1\n            });\n            setIsAuthenticated(true);\n            return true;\n        }\n        return false;\n    };\n    const logout = ()=>{\n        js_cookie__WEBPACK_IMPORTED_MODULE_3__[\"default\"].remove(\"adminAuthToken\");\n        setIsAuthenticated(false);\n        router.push(\"/login\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: {\n            isAuthenticated,\n            login,\n            logout\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\context\\\\authContext.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/authContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/context/loadingContext.tsx":
/*!****************************************!*\
  !*** ./app/context/loadingContext.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoadingProvider: () => (/* binding */ LoadingProvider),\n/* harmony export */   useLoading: () => (/* binding */ useLoading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_LoadingOverlay__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../components/LoadingOverlay */ \"(ssr)/./app/components/LoadingOverlay.tsx\");\n/* __next_internal_client_entry_do_not_use__ LoadingProvider,useLoading auto */ \n\n\n\nconst LoadingContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction LoadingProvider({ children }) {\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [message, setMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    // const searchParams = useSearchParams();\n    // Show loading during navigation\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        setIsLoading(true);\n        const timeout = setTimeout(()=>setIsLoading(false), 500); // Minimum loading time\n        return ()=>clearTimeout(timeout);\n    }, [\n        pathname\n    ]);\n    const showLoading = (msg)=>{\n        setMessage(msg);\n        setIsLoading(true);\n    };\n    const hideLoading = ()=>{\n        setIsLoading(false);\n        setMessage(undefined);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LoadingContext.Provider, {\n        value: {\n            showLoading,\n            hideLoading\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_LoadingOverlay__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                isLoading: isLoading,\n                message: message\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\context\\\\loadingContext.tsx\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, this),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\context\\\\loadingContext.tsx\",\n        lineNumber: 37,\n        columnNumber: 5\n    }, this);\n}\nfunction useLoading() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LoadingContext);\n    if (context === undefined) {\n        throw new Error(\"useLoading must be used within a LoadingProvider\");\n    }\n    return context;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/context/loadingContext.tsx\n");

/***/ }),

/***/ "(ssr)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Typography/Typography.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Grid/Grid.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Card/Card.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/CardContent/CardContent.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Paper/Paper.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Button/Button.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TableContainer/TableContainer.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/Table/Table.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TableHead/TableHead.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TableRow/TableRow.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TableCell/TableCell.js\");\n/* harmony import */ var _barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Button,Card,CardContent,Grid,Paper,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,Typography!=!@mui/material */ \"(ssr)/./node_modules/@mui/material/TableBody/TableBody.js\");\n/* harmony import */ var _barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Block,CheckCircle,DirectionsCar,Pending,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/DirectionsCar.js\");\n/* harmony import */ var _barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Block,CheckCircle,DirectionsCar,Pending,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Person.js\");\n/* harmony import */ var _barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Block,CheckCircle,DirectionsCar,Pending,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Pending.js\");\n/* harmony import */ var _barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Block,CheckCircle,DirectionsCar,Pending,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/CheckCircle.js\");\n/* harmony import */ var _barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Block,CheckCircle,DirectionsCar,Pending,Person!=!@mui/icons-material */ \"(ssr)/./node_modules/@mui/icons-material/esm/Block.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _context_loadingContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./context/loadingContext */ \"(ssr)/./app/context/loadingContext.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nfunction Dashboard() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { showLoading } = (0,_context_loadingContext__WEBPACK_IMPORTED_MODULE_3__.useLoading)();\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        totalCars: 0,\n        totalUsers: 0,\n        pendingApprovals: 0,\n        approvedCars: 0,\n        rejectedCars: 0,\n        soldCars: 0\n    });\n    const [recentActivity, setRecentActivity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        fetchDashboardData();\n    }, []);\n    const fetchDashboardData = async ()=>{\n        try {\n            const [carsResponse, usersResponse] = await Promise.all([\n                fetch(\"http://localhost:5000\" + \"/cars\"),\n                fetch(\"http://localhost:5000\" + \"/users\")\n            ]);\n            const [carsData, usersData] = await Promise.all([\n                carsResponse.json(),\n                usersResponse.json()\n            ]);\n            const cars = carsData.success ? carsData.data : [];\n            const users = usersData.success ? usersData.data : [];\n            const statsData = {\n                totalCars: cars.length,\n                totalUsers: users.length,\n                pendingApprovals: cars.filter((car)=>car.carStatus === \"pending\").length,\n                approvedCars: cars.filter((car)=>car.carStatus === \"approved\").length,\n                rejectedCars: cars.filter((car)=>car.carStatus === \"rejected\").length,\n                soldCars: cars.filter((car)=>car.carStatus === \"sold\").length\n            };\n            // Get recent activity (last 10 cars)\n            const sortedCars = [\n                ...cars\n            ].sort((a, b)=>new Date(b.postedDate).getTime() - new Date(a.postedDate).getTime()).slice(0, 10);\n            setStats(statsData);\n            setRecentActivity(sortedCars);\n        } catch (error) {\n            console.error(\"Failed to fetch dashboard data:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case \"approved\":\n                return \"bg-green-100 text-green-800\";\n            case \"rejected\":\n                return \"bg-red-100 text-red-800\";\n            default:\n                return \"bg-yellow-100 text-yellow-800\";\n        }\n    };\n    const handleQuickAction = (path, actionName)=>{\n        showLoading(`Loading ${actionName}...`);\n        router.push(path);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xl font-semibold text-gray-700\",\n                children: \"Loading...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n            lineNumber: 125,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                variant: \"h4\",\n                component: \"h1\",\n                gutterBottom: true,\n                children: \"Dashboard\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                container: true,\n                spacing: 3,\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2.4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"bg-blue-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"text-blue-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                color: \"textSecondary\",\n                                                children: \"Total Cars\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 144,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 142,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        variant: \"h4\",\n                                        component: \"h2\",\n                                        className: \"mt-2\",\n                                        children: stats.totalCars\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                            lineNumber: 140,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2.4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"bg-green-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 156,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                color: \"textSecondary\",\n                                                children: \"Total Users\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        variant: \"h4\",\n                                        component: \"h2\",\n                                        className: \"mt-2\",\n                                        children: stats.totalUsers\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 159,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                            lineNumber: 153,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2.4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"bg-yellow-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"text-yellow-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 169,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                color: \"textSecondary\",\n                                                children: \"Pending\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 170,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        variant: \"h4\",\n                                        component: \"h2\",\n                                        className: \"mt-2\",\n                                        children: stats.pendingApprovals\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                        lineNumber: 165,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2.4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"bg-green-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"text-green-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                color: \"textSecondary\",\n                                                children: \"Approved\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 181,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        variant: \"h4\",\n                                        component: \"h2\",\n                                        className: \"mt-2\",\n                                        children: stats.approvedCars\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                            lineNumber: 179,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2.4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"bg-red-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"text-red-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 195,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                color: \"textSecondary\",\n                                                children: \"Rejected\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 196,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 194,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        variant: \"h4\",\n                                        component: \"h2\",\n                                        className: \"mt-2\",\n                                        children: stats.rejectedCars\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                lineNumber: 193,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                            lineNumber: 192,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                        lineNumber: 191,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        item: true,\n                        xs: 12,\n                        sm: 6,\n                        md: 2.4,\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"bg-purple-50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Block_CheckCircle_DirectionsCar_Pending_Person_mui_icons_material__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"text-purple-600\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                color: \"textSecondary\",\n                                                children: \"Sold\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 207,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                        variant: \"h4\",\n                                        component: \"h2\",\n                                        className: \"mt-2\",\n                                        children: stats.soldCars\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                            lineNumber: 205,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                elevation: 1,\n                className: \"p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"Quick Actions\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                        lineNumber: 221,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                variant: \"contained\",\n                                color: \"primary\",\n                                onClick: ()=>handleQuickAction(\"/cars\", \"car management\"),\n                                children: \"View All Cars\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                variant: \"contained\",\n                                color: \"secondary\",\n                                onClick: ()=>handleQuickAction(\"/users\", \"user management\"),\n                                children: \"Manage Users\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                        lineNumber: 224,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                lineNumber: 220,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                elevation: 1,\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                        variant: \"h6\",\n                        gutterBottom: true,\n                        children: \"Recent Activity\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                        lineNumber: 244,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                children: \"Car\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                children: \"Owner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                children: \"Status\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                children: \"Posted Date\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 254,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                children: \"Action\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                        lineNumber: 250,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                    lineNumber: 249,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                    children: recentActivity.map((activity)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    children: [\n                                                        activity.carBrand,\n                                                        \" \",\n                                                        activity.carModel\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                    lineNumber: 261,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    children: activity.ownerName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                    lineNumber: 264,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: `px-2 py-1 rounded text-sm ${getStatusColor(activity.carStatus)}`,\n                                                        children: activity.carStatus.charAt(0).toUpperCase() + activity.carStatus.slice(1)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                    lineNumber: 265,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    children: new Date(activity.postedDate).toLocaleDateString()\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                    lineNumber: 275,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Button_Card_CardContent_Grid_Paper_Table_TableBody_TableCell_TableContainer_TableHead_TableRow_Typography_mui_material__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                        variant: \"outlined\",\n                                                        size: \"small\",\n                                                        onClick: ()=>router.push(`/application/${activity.id}`),\n                                                        children: \"View Details\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                        lineNumber: 279,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                                    lineNumber: 278,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, activity.id, true, {\n                                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                            lineNumber: 260,\n                                            columnNumber: 17\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                                    lineNumber: 258,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                            lineNumber: 248,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n                lineNumber: 243,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\page.tsx\",\n        lineNumber: 132,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"f89e4b1cf322\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZG1pbl9wYW5uZWwvLi9hcHAvZ2xvYmFscy5jc3M/OTkwZCJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcImY4OWU0YjFjZjMyMlwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/clientLayout.tsx":
/*!******************************!*\
  !*** ./app/clientLayout.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Projects\PRODUCTION\PROJECT_01\admin_pannel\app\clientLayout.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* reexport safe */ _metadata__WEBPACK_IMPORTED_MODULE_2__.metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _metadata__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./metadata */ \"(rsc)/./app/metadata.ts\");\n/* harmony import */ var _clientLayout__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./clientLayout */ \"(rsc)/./app/clientLayout.tsx\");\n\n\n\n\n\n\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_clientLayout__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\layout.tsx\",\n                lineNumber: 18,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\layout.tsx\",\n            lineNumber: 17,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Projects\\\\PRODUCTION\\\\PROJECT_01\\\\admin_pannel\\\\app\\\\layout.tsx\",\n        lineNumber: 16,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7O0FBS01BO0FBSmlCO0FBQ2U7QUFDSTtBQUl0QjtBQUVMLFNBQVNHLFdBQVcsRUFDakNDLFFBQVEsRUFHUjtJQUNBLHFCQUNFLDhEQUFDQztRQUFLQyxNQUFLO2tCQUNULDRFQUFDQztZQUFLQyxXQUFXUiwySkFBZTtzQkFDOUIsNEVBQUNFLHFEQUFZQTswQkFBRUU7Ozs7Ozs7Ozs7Ozs7Ozs7QUFJdkIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9hZG1pbl9wYW5uZWwvLi9hcHAvbGF5b3V0LnRzeD85OTg4Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEludGVyIH0gZnJvbSBcIm5leHQvZm9udC9nb29nbGVcIjtcbmltcG9ydCBcIi4vZ2xvYmFscy5jc3NcIjtcbmltcG9ydCB7IG1ldGFkYXRhIH0gZnJvbSBcIi4vbWV0YWRhdGFcIjtcbmltcG9ydCBDbGllbnRMYXlvdXQgZnJvbSBcIi4vY2xpZW50TGF5b3V0XCI7XG5cbmNvbnN0IGludGVyID0gSW50ZXIoeyBzdWJzZXRzOiBbXCJsYXRpblwiXSB9KTtcblxuZXhwb3J0IHsgbWV0YWRhdGEgfTtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gUm9vdExheW91dCh7XG4gIGNoaWxkcmVuLFxufTogUmVhZG9ubHk8e1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlO1xufT4pIHtcbiAgcmV0dXJuIChcbiAgICA8aHRtbCBsYW5nPVwiZW5cIj5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17aW50ZXIuY2xhc3NOYW1lfT5cbiAgICAgICAgPENsaWVudExheW91dD57Y2hpbGRyZW59PC9DbGllbnRMYXlvdXQ+XG4gICAgICA8L2JvZHk+XG4gICAgPC9odG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbImludGVyIiwibWV0YWRhdGEiLCJDbGllbnRMYXlvdXQiLCJSb290TGF5b3V0IiwiY2hpbGRyZW4iLCJodG1sIiwibGFuZyIsImJvZHkiLCJjbGFzc05hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/metadata.ts":
/*!*************************!*\
  !*** ./app/metadata.ts ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\nconst metadata = {\n    title: \"Cars Hub Admin\",\n    description: \"Admin panel for Cars Hub application\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbWV0YWRhdGEudHMiLCJtYXBwaW5ncyI6Ijs7OztBQUVPLE1BQU1BLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWRtaW5fcGFubmVsLy4vYXBwL21ldGFkYXRhLnRzPzI3NzIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTWV0YWRhdGEgfSBmcm9tIFwibmV4dFwiO1xyXG5cclxuZXhwb3J0IGNvbnN0IG1ldGFkYXRhOiBNZXRhZGF0YSA9IHtcclxuICB0aXRsZTogXCJDYXJzIEh1YiBBZG1pblwiLFxyXG4gIGRlc2NyaXB0aW9uOiBcIkFkbWluIHBhbmVsIGZvciBDYXJzIEh1YiBhcHBsaWNhdGlvblwiLFxyXG59O1xyXG4iXSwibmFtZXMiOlsibWV0YWRhdGEiLCJ0aXRsZSIsImRlc2NyaXB0aW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/metadata.ts\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Projects\PRODUCTION\PROJECT_01\admin_pannel\app\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__":
/*!**********************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__ ***!
  \**********************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9hcHAvZmF2aWNvbi5pY28/X19uZXh0X21ldGFkYXRhX18iLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUEsRUFBaUY7O0FBRWpGLEVBQUUsaUVBQWU7QUFDakIsdUJBQXVCO0FBQ3ZCLHFCQUFxQiw4RkFBbUI7O0FBRXhDO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCIsInNvdXJjZXMiOlsid2VicGFjazovL2FkbWluX3Bhbm5lbC8uL2FwcC9mYXZpY29uLmljbz83N2Y2Il0sInNvdXJjZXNDb250ZW50IjpbIiAgaW1wb3J0IHsgZmlsbE1ldGFkYXRhU2VnbWVudCB9IGZyb20gJ25leHQvZGlzdC9saWIvbWV0YWRhdGEvZ2V0LW1ldGFkYXRhLXJvdXRlJ1xuXG4gIGV4cG9ydCBkZWZhdWx0IChwcm9wcykgPT4ge1xuICAgIGNvbnN0IGltYWdlRGF0YSA9IHtcInR5cGVcIjpcImltYWdlL3gtaWNvblwiLFwic2l6ZXNcIjpcIjE2eDE2XCJ9XG4gICAgY29uc3QgaW1hZ2VVcmwgPSBmaWxsTWV0YWRhdGFTZWdtZW50KFwiLlwiLCBwcm9wcy5wYXJhbXMsIFwiZmF2aWNvbi5pY29cIilcblxuICAgIHJldHVybiBbe1xuICAgICAgLi4uaW1hZ2VEYXRhLFxuICAgICAgdXJsOiBpbWFnZVVybCArIFwiXCIsXG4gICAgfV1cbiAgfSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@mui","vendor-chunks/@emotion","vendor-chunks/prop-types","vendor-chunks/react-transition-group","vendor-chunks/stylis","vendor-chunks/hoist-non-react-statics","vendor-chunks/react-is","vendor-chunks/js-cookie","vendor-chunks/@babel","vendor-chunks/@swc","vendor-chunks/object-assign","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CProjects%5CPRODUCTION%5CPROJECT_01%5Cadmin_pannel&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();