import * as admin from 'firebase-admin';

// Firebase service account key - in production, this should be loaded from environment variables
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_PROJECT_ID || "carshu-1e768",
  private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
  private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  client_email: process.env.FIREBASE_CLIENT_EMAIL,
  client_id: process.env.FIREBASE_CLIENT_ID,
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: process.env.FIREBASE_CLIENT_CERT_URL,
};

// Initialize Firebase Admin SDK only if it hasn't been initialized already
if (!admin.apps.length) {
  try {
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
    });
    console.log('Firebase Admin initialized successfully');
  } catch (error) {
    console.error('Firebase Admin initialization error:', error);
    // Fallback: try to initialize with the original service account file if available
    try {
      // This is a fallback for development - in production use environment variables
      const key = require('../../SERVER/carshu-1e768-firebase-adminsdk-fbsvc-3503135aec.json');
      admin.initializeApp({
        credential: admin.credential.cert(key),
      });
      console.log('Firebase Admin initialized with fallback key');
    } catch (fallbackError) {
      console.error('Firebase Admin fallback initialization error:', fallbackError);
      throw new Error('Failed to initialize Firebase Admin SDK');
    }
  }
}

// Firestore database instance
const db = admin.firestore();

export { db, admin };
