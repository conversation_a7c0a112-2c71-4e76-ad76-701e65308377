{"expo": {"name": "CarApp", "slug": "CarApp", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.jpg", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.rougecipher.CarApp", "infoPlist": {"ITSAppUsesNonExemptEncryption": false}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/splashscreen.png", "backgroundColor": "#ffffff"}, "package": "com.rougecipher.CarApp"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/icon.jpg", "resizeMode": "cover", "backgroundColor": "#ffffff"}], ["expo-build-properties", {"android": {"usesCleartextTraffic": true}}]], "experiments": {"typedRoutes": true}, "extra": {"API_URL": "https://your-api.com", "router": {"origin": false}, "eas": {"projectId": "54a9a763-2445-49c1-953c-71a32030e855"}}}}