{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_utils", "_theming", "_MaterialCommunityIcon", "_interopRequireDefault", "_TouchableRipple", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "ANIMATION_DURATION", "CheckboxAndroid", "status", "theme", "themeOverrides", "disabled", "onPress", "testID", "rest", "useInternalTheme", "current", "scaleAnim", "useRef", "Animated", "Value", "isFirstRendering", "animation", "scale", "useEffect", "checked", "sequence", "timing", "toValue", "duration", "useNativeDriver", "start", "indeterminate", "rippleColor", "selectionControlColor", "getAndroidSelectionControlColor", "customColor", "color", "customUncheckedColor", "uncheckedColor", "borderWidth", "interpolate", "inputRange", "outputRange", "icon", "createElement", "borderless", "accessibilityRole", "accessibilityState", "accessibilityLiveRegion", "style", "styles", "container", "View", "transform", "allowFontScaling", "name", "size", "direction", "StyleSheet", "absoluteFill", "<PERSON><PERSON><PERSON><PERSON>", "fill", "borderColor", "exports", "displayName", "create", "borderRadius", "width", "height", "padding", "alignItems", "justifyContent", "_default"], "sourceRoot": "../../../../src", "sources": ["components/Checkbox/CheckboxAndroid.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAOA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAEA,IAAAI,sBAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,gBAAA,GAAAD,sBAAA,CAAAL,OAAA;AAAiE,SAAAK,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,CAAAQ,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAiCjE;AACA,MAAMG,kBAAkB,GAAG,GAAG;;AAE9B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,GAAGA,CAAC;EACvBC,MAAM;EACNC,KAAK,EAAEC,cAAc;EACrBC,QAAQ;EACRC,OAAO;EACPC,MAAM;EACN,GAAGC;AACE,CAAC,KAAK;EACX,MAAML,KAAK,GAAG,IAAAM,yBAAgB,EAACL,cAAc,CAAC;EAC9C,MAAM;IAAEM,OAAO,EAAEC;EAAU,CAAC,GAAG7C,KAAK,CAAC8C,MAAM,CACzC,IAAIC,qBAAQ,CAACC,KAAK,CAAC,CAAC,CACtB,CAAC;EACD,MAAMC,gBAAgB,GAAGjD,KAAK,CAAC8C,MAAM,CAAU,IAAI,CAAC;EAEpD,MAAM;IACJI,SAAS,EAAE;MAAEC;IAAM;EACrB,CAAC,GAAGd,KAAK;EAETrC,KAAK,CAACoD,SAAS,CAAC,MAAM;IACpB;IACA,IAAIH,gBAAgB,CAACL,OAAO,EAAE;MAC5BK,gBAAgB,CAACL,OAAO,GAAG,KAAK;MAChC;IACF;IAEA,MAAMS,OAAO,GAAGjB,MAAM,KAAK,SAAS;IAEpCW,qBAAQ,CAACO,QAAQ,CAAC,CAChBP,qBAAQ,CAACQ,MAAM,CAACV,SAAS,EAAE;MACzBW,OAAO,EAAE,IAAI;MACbC,QAAQ,EAAEJ,OAAO,GAAGnB,kBAAkB,GAAGiB,KAAK,GAAG,CAAC;MAClDO,eAAe,EAAE;IACnB,CAAC,CAAC,EACFX,qBAAQ,CAACQ,MAAM,CAACV,SAAS,EAAE;MACzBW,OAAO,EAAE,CAAC;MACVC,QAAQ,EAAEJ,OAAO,GACbnB,kBAAkB,GAAGiB,KAAK,GAC1BjB,kBAAkB,GAAGiB,KAAK,GAAG,IAAI;MACrCO,eAAe,EAAE;IACnB,CAAC,CAAC,CACH,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACvB,MAAM,EAAES,SAAS,EAAEM,KAAK,CAAC,CAAC;EAE9B,MAAME,OAAO,GAAGjB,MAAM,KAAK,SAAS;EACpC,MAAMwB,aAAa,GAAGxB,MAAM,KAAK,eAAe;EAEhD,MAAM;IAAEyB,WAAW;IAAEC;EAAsB,CAAC,GAC1C,IAAAC,sCAA+B,EAAC;IAC9B1B,KAAK;IACLE,QAAQ;IACRc,OAAO;IACPW,WAAW,EAAEtB,IAAI,CAACuB,KAAK;IACvBC,oBAAoB,EAAExB,IAAI,CAACyB;EAC7B,CAAC,CAAC;EAEJ,MAAMC,WAAW,GAAGvB,SAAS,CAACwB,WAAW,CAAC;IACxCC,UAAU,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACpBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC;EAEF,MAAMC,IAAI,GAAGZ,aAAa,GACtB,WAAW,GACXP,OAAO,GACP,iBAAiB,GACjB,wBAAwB;EAE5B,oBACErD,KAAA,CAAAyE,aAAA,CAACjE,gBAAA,CAAAG,OAAe,EAAAiB,QAAA,KACVc,IAAI;IACRgC,UAAU;IACVb,WAAW,EAAEA,WAAY;IACzBrB,OAAO,EAAEA,OAAQ;IACjBD,QAAQ,EAAEA,QAAS;IACnBoC,iBAAiB,EAAC,UAAU;IAC5BC,kBAAkB,EAAE;MAAErC,QAAQ;MAAEc;IAAQ,CAAE;IAC1CwB,uBAAuB,EAAC,QAAQ;IAChCC,KAAK,EAAEC,MAAM,CAACC,SAAU;IACxBvC,MAAM,EAAEA,MAAO;IACfJ,KAAK,EAAEA;EAAM,iBAEbrC,KAAA,CAAAyE,aAAA,CAACtE,YAAA,CAAA4C,QAAQ,CAACkC,IAAI;IAACH,KAAK,EAAE;MAAEI,SAAS,EAAE,CAAC;QAAE/B,KAAK,EAAEN;MAAU,CAAC;IAAE;EAAE,gBAC1D7C,KAAA,CAAAyE,aAAA,CAACnE,sBAAA,CAAAK,OAAqB;IACpBwE,gBAAgB,EAAE,KAAM;IACxBC,IAAI,EAAEZ,IAAK;IACXa,IAAI,EAAE,EAAG;IACTpB,KAAK,EAAEH,qBAAsB;IAC7BwB,SAAS,EAAC;EAAK,CAChB,CAAC,eACFtF,KAAA,CAAAyE,aAAA,CAACtE,YAAA,CAAA8E,IAAI;IAACH,KAAK,EAAE,CAACS,uBAAU,CAACC,YAAY,EAAET,MAAM,CAACU,aAAa;EAAE,gBAC3DzF,KAAA,CAAAyE,aAAA,CAACtE,YAAA,CAAA4C,QAAQ,CAACkC,IAAI;IACZH,KAAK,EAAE,CACLC,MAAM,CAACW,IAAI,EACX;MAAEC,WAAW,EAAE7B;IAAsB,CAAC,EACtC;MAAEM;IAAY,CAAC;EACf,CACH,CACG,CACO,CACA,CAAC;AAEtB,CAAC;AAACwB,OAAA,CAAAzD,eAAA,GAAAA,eAAA;AAEFA,eAAe,CAAC0D,WAAW,GAAG,kBAAkB;AAEhD,MAAMd,MAAM,GAAGQ,uBAAU,CAACO,MAAM,CAAC;EAC/Bd,SAAS,EAAE;IACTe,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,EAAE;IACTC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE;EACX,CAAC;EACDT,aAAa,EAAE;IACbU,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDV,IAAI,EAAE;IACJO,MAAM,EAAE,EAAE;IACVD,KAAK,EAAE;EACT;AACF,CAAC,CAAC;AAAC,IAAAK,QAAA,GAAAT,OAAA,CAAAjF,OAAA,GAEYwB,eAAe,EAE9B", "ignoreList": []}