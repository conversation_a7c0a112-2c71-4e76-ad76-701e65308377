/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/theme`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Auth)'}/Auth` | `/Auth`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Auth)'}/OTP` | `/OTP`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Auth)'}/Signup` | `/Signup`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/BuyCarPage` | `/components/BuyCarPage`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/BuyCars` | `/components/BuyCars`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/deleteableCarousol` | `/components/deleteableCarousol`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/Fav` | `/components/Fav`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/MyCars` | `/components/MyCars`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/Navbar` | `/components/Navbar`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/SellCars` | `/components/SellCars`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/SellCarsImageCard` | `/components/SellCarsImageCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/SingleChatCard` | `/components/SingleChatCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/Services/backendoperations` | `/Services/backendoperations`; params?: Router.UnknownInputParams; } | { pathname: `/Chats/chatServices`; params?: Router.UnknownInputParams; } | { pathname: `/Chats`; params?: Router.UnknownInputParams; } | { pathname: `/Chats/Conversation`; params?: Router.UnknownInputParams; } | { pathname: `/context/loadingContext`; params?: Router.UnknownInputParams; } | { pathname: `/context/userContext`; params?: Router.UnknownInputParams; };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/`; params?: Router.UnknownOutputParams; } | { pathname: `/theme`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Auth)'}/Auth` | `/Auth`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Auth)'}/OTP` | `/OTP`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Auth)'}/Signup` | `/Signup`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/components/BuyCarPage` | `/components/BuyCarPage`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/components/BuyCars` | `/components/BuyCars`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/components/deleteableCarousol` | `/components/deleteableCarousol`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/components/Fav` | `/components/Fav`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/components/MyCars` | `/components/MyCars`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/components/Navbar` | `/components/Navbar`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/components/SellCars` | `/components/SellCars`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/components/SellCarsImageCard` | `/components/SellCarsImageCard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/components/SingleChatCard` | `/components/SingleChatCard`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/home` | `/home`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(Home)'}/Services/backendoperations` | `/Services/backendoperations`; params?: Router.UnknownOutputParams; } | { pathname: `/Chats/chatServices`; params?: Router.UnknownOutputParams; } | { pathname: `/Chats`; params?: Router.UnknownOutputParams; } | { pathname: `/Chats/Conversation`; params?: Router.UnknownOutputParams; } | { pathname: `/context/loadingContext`; params?: Router.UnknownOutputParams; } | { pathname: `/context/userContext`; params?: Router.UnknownOutputParams; };
      href: Router.RelativePathString | Router.ExternalPathString | `/${`?${string}` | `#${string}` | ''}` | `/theme${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(Auth)'}/Auth${`?${string}` | `#${string}` | ''}` | `/Auth${`?${string}` | `#${string}` | ''}` | `${'/(Auth)'}/OTP${`?${string}` | `#${string}` | ''}` | `/OTP${`?${string}` | `#${string}` | ''}` | `${'/(Auth)'}/Signup${`?${string}` | `#${string}` | ''}` | `/Signup${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/components/BuyCarPage${`?${string}` | `#${string}` | ''}` | `/components/BuyCarPage${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/components/BuyCars${`?${string}` | `#${string}` | ''}` | `/components/BuyCars${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/components/deleteableCarousol${`?${string}` | `#${string}` | ''}` | `/components/deleteableCarousol${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/components/Fav${`?${string}` | `#${string}` | ''}` | `/components/Fav${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/components/MyCars${`?${string}` | `#${string}` | ''}` | `/components/MyCars${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/components/Navbar${`?${string}` | `#${string}` | ''}` | `/components/Navbar${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/components/SellCars${`?${string}` | `#${string}` | ''}` | `/components/SellCars${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/components/SellCarsImageCard${`?${string}` | `#${string}` | ''}` | `/components/SellCarsImageCard${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/components/SingleChatCard${`?${string}` | `#${string}` | ''}` | `/components/SingleChatCard${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/home${`?${string}` | `#${string}` | ''}` | `/home${`?${string}` | `#${string}` | ''}` | `${'/(Home)'}/Services/backendoperations${`?${string}` | `#${string}` | ''}` | `/Services/backendoperations${`?${string}` | `#${string}` | ''}` | `/Chats/chatServices${`?${string}` | `#${string}` | ''}` | `/Chats${`?${string}` | `#${string}` | ''}` | `/Chats/Conversation${`?${string}` | `#${string}` | ''}` | `/context/loadingContext${`?${string}` | `#${string}` | ''}` | `/context/userContext${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/`; params?: Router.UnknownInputParams; } | { pathname: `/theme`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Auth)'}/Auth` | `/Auth`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Auth)'}/OTP` | `/OTP`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Auth)'}/Signup` | `/Signup`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/BuyCarPage` | `/components/BuyCarPage`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/BuyCars` | `/components/BuyCars`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/deleteableCarousol` | `/components/deleteableCarousol`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/Fav` | `/components/Fav`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/MyCars` | `/components/MyCars`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/Navbar` | `/components/Navbar`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/SellCars` | `/components/SellCars`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/SellCarsImageCard` | `/components/SellCarsImageCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/components/SingleChatCard` | `/components/SingleChatCard`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/home` | `/home`; params?: Router.UnknownInputParams; } | { pathname: `${'/(Home)'}/Services/backendoperations` | `/Services/backendoperations`; params?: Router.UnknownInputParams; } | { pathname: `/Chats/chatServices`; params?: Router.UnknownInputParams; } | { pathname: `/Chats`; params?: Router.UnknownInputParams; } | { pathname: `/Chats/Conversation`; params?: Router.UnknownInputParams; } | { pathname: `/context/loadingContext`; params?: Router.UnknownInputParams; } | { pathname: `/context/userContext`; params?: Router.UnknownInputParams; };
    }
  }
}
