globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/application/[id]/page"]={"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"ssrModuleMapping":{"(app-pages-browser)/./app/login/page.tsx":{"*":{"id":"(ssr)/./app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/clientLayout.tsx":{"*":{"id":"(ssr)/./app/clientLayout.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/page.tsx":{"*":{"id":"(ssr)/./app/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/users/page.tsx":{"*":{"id":"(ssr)/./app/users/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/cars/page.tsx":{"*":{"id":"(ssr)/./app/cars/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./app/application/[id]/page.tsx":{"*":{"id":"(ssr)/./app/application/[id]/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\clientLayout.tsx":{"id":"(app-pages-browser)/./app/clientLayout.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\font\\google\\target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\globals.css":{"id":"(app-pages-browser)/./app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\esm\\client\\components\\app-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/app-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\esm\\client\\components\\not-found-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/not-found-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\page.tsx":{"id":"(app-pages-browser)/./app/page.tsx","name":"*","chunks":["app/page","static/chunks/app/page.js"],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\users\\page.tsx":{"id":"(app-pages-browser)/./app/users/page.tsx","name":"*","chunks":[],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\cars\\page.tsx":{"id":"(app-pages-browser)/./app/cars/page.tsx","name":"*","chunks":[],"async":false},"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\application\\[id]\\page.tsx":{"id":"(app-pages-browser)/./app/application/[id]/page.tsx","name":"*","chunks":["app/application/[id]/page","static/chunks/app/application/%5Bid%5D/page.js"],"async":false}},"entryCSSFiles":{"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\":[],"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\layout":["static/css/app/layout.css"],"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\page":[],"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\application\\[id]\\page":["static/css/app/application/[id]/page.css"],"C:\\Projects\\PRODUCTION\\PROJECT_01\\admin_pannel\\app\\application\\[id]\\layout":[]}}